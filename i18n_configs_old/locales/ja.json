{"errors": {"invalid_command": "無効なコマンドです", "permission_denied": "権限がありません。この操作を実行できません", "vip_required": "この機能にはVIPメンバーシップが必要です", "general_error": "エラーが発生しました。後でもう一度お試しください", "command_failed": "コマンドの実行に失敗しました。もう一度お試しください"}, "invite": {"start": {"link": "あなたの招待リンク：{link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "招待成功！\nユーザー：{user_id}を招待し、200ポイントを獲得しました！", "fail": "ユーザー{user_id}の招待に失敗しました！\nエラーメッセージ：{error}\nあなたの招待リンク：{link}", "invited_by": "あなたはユーザーID {user_id} によって招待されました, 君の招待コードは：{link}"}, "error": {"self_invite": "自分自身を招待することはできません！", "already_invited": "このユーザーはすでにユーザーID {invited_by} によって招待されています", "time_limit": "このユーザーは登録から24時間以上経過しているため、招待できません"}}, "keyboard": {"language": "言語設定", "instructions": "使用説明", "channel_notifications": "チャンネル通知", "get_invite_code": "招待コード取得", "group_management": "グループ管理"}, "instructions": {"content": "🤖TGAI 機能ガイド🤖\n🌐GPT4.0 オンライン\nコマンド: net\n使用方法: net Centrica株を分析する / net LAの天気予報を表示\n\n🔍GPT4mini\nコマンド: ask (またはa)\n使用方法: ask アメリカにあるブロックチェーン取引所はどこですか？\n機能: チャット、Q&A、画像認識、記憶追跡\n\n🧩GPT4o\nコマンド: 4o\n使用方法: 4o XXX株のトレンドを分析する\n機能: 株式分析、暗号通貨データ、スマートチャット、画像認識\n\n🍻Claude-h\nコマンド: ck\n使用方法: ck このメッセージにはどう返信すればよいか？\n機能: 迅速な返答、チャット支援、記憶追跡\n\n🤡顔交換\nコマンド: fc\n使用方法: 2枚の写真をアップロードしてfcを使用\n\n🎨画像生成\nコマンド: draw\n使用方法: draw タイムズスクエアの朝のシーンを生成\n\n🧑‍🔬OpenAI-o1\nコマンド: o1\n使用方法: o1 論理的にこの問題を分析する\n\n🎐背景変更\nコマンド: br\n使用方法: 人物+背景の写真を送信（同じサイズ）", "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ"}, "vip": {"status": {"active": "有効", "inactive": "無効", "expired": "期限切れ"}, "expires_at": "有効期限：{date}"}, "channel": {"notification": "チャンネルに参加してTAIの更新状況をフォローし、AIに関する最新情報をいち早く入手しましょう❕❕❕ \n\nボタン：TGAI公式チャンネル - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "最強の無料TGAI-ChatGPTへようこそ\n使用方法:\nask(a) + スペース + プロンプト = 質問\nvision(v) 画像認識（無制限使用）\nnet(n) 公式ウェブサイトオンラインモデル（無制限使用）\ndraw(d) 画像生成モデル（無制限使用）\nsd 画像交換モデル（40計算力消費）\nfc: 顔交換モデル（20計算力消費）\n4o(o) GPT-4oモデル（10計算力消費）\ncs: claude-sonnetモデル（10計算力消費）\nck: claude-haikuモデル（無制限使用）\no1: OpenAI最新のo1モデル（アルファテスト版、安定性向上中）\n\n高度な機能、全ネット唯一！:\n1. すべてのコマンドで画像認識がサポートされており、画像を送信するだけで使用可能（複数画像認識もサポート）\n2. 公式ウェブサイトと同様に前回のテキストを参照してコンテキスト質問が可能\n乱用防止のため、1人あたり1日200計算力が与えられ、これによりGPT-4oを約40回、SDを5回使用可能\n\n計算力が足りない場合、1人をTGAIに招待すると200計算力が無料で付与されます（24時間有効）！", "message_photo_id": ""}}, "group": {"management": {"tutorial": "グループの追加は2ステップで完了：（または下のボタンをクリック）\n1. ボットをグループに追加し、管理者に設定\n2. あなたがグループ管理者であることを確認\n3. 最初の2つのステップを完了してもグループが見つからない場合は、グループで /update コマンドを実行してください\n4. グループ名のボタンをクリックしてグループを管理", "add_bot": "【ここをクリックしてボットをグループに追加】", "prev_page": "◀️前へ", "next_page": "次へ▶️", "refresh": "🔄更新", "help": "❓ヘルプ", "group_info": "📑 現在選択中のグループ：\nグループ名：{title}\nグループID：{chat_id}\n\n操作を選択してください：", "delete_binding": "❌ グループのバインドを解除", "back_to_list": "⬅️ リストに戻る", "unbind_success": "グループのバインドを解除しました！", "unbind_failed": "バインド解除に失敗しました。後でもう一度お試しください", "group_not_found": "グループ情報が見つかりません"}, "help": {"title": "ヘルプセンター：", "content": "1. 誤ってグループのバインドを解除してしまった場合は？\n回答：ボットをグループから削除し、再度招待してください。\n\n2. ボットをグループに正しく追加するには？\n回答：【ここをクリックしてボットをグループに追加】ボタンをクリックし、対象のグループを選択してください。\n\n3. グループが表示されない理由は？\n回答：以下を確認してください：\n   - ボットがグループ管理者に設定されている\n   - あなたがグループ管理者である\n   - 更新ボタンをクリックした", "back": "戻る"}, "binding": {"success": "グループに追加していただき、ありがとうございます！\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}", "failed": "バインドに失敗しました！ボットを招待する際は、グループ管理者の身分を確認するため、管理者として設定してください。ボットを削除して再度グループに招待してください。\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}", "success_user": "グループに追加していただき、ありがとうございます！\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "広告を削除", "generate_remove_ads_code": "広告削除コードを生成"}, "invalid_params": "パラメータが無効です。形式を確認してください。\n正しい形式：{command} <認証コード>", "invalid_code": "認証コードが無効か、すでに使用されています！認証コードが正しいか確認してください。\n正しい形式：{command} <認証コード>", "remove_success": "広告を削除しました", "remove_failed": "広告の削除に失敗しました。エラー：{error}", "generate_success": "広告削除コードを生成しました。添付ファイルを確認してください", "error": "広告の削除に失敗しました。管理者に連絡してください。エラー：{error}"}, "language": {"choose": "言語を選択してください：\n\n現在サポートされている言語：", "changed": "✅ 言語が日本語に変更されました", "error": "言語の変更に失敗しました。後でもう一度お試しください", "current": "現在の言語：日本語"}}