{"errors": {"invalid_command": "Commande invalide", "permission_denied": "Permission refusée, impossible d'effectuer cette opération", "vip_required": "Cette fonctionnalité nécessite un abonnement VIP", "general_error": "Une erreur est survenue, veuillez réessayer plus tard", "command_failed": "Échec de l'exécution de la commande, ve<PERSON><PERSON><PERSON> réessayer"}, "invite": {"start": {"link": "Votre lien d'invitation est : {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "Invitation réussie!\nVous avez invité l'utilisateur : {user_id}, et avez reçu 200 de puissance de calcul!", "fail": "V<PERSON> avez invité l'utilisateur {user_id}, mais l'invitation a échoué!\nMessage d'erreur : {error}\nVotre lien d'invitation : {link}", "invited_by": "V<PERSON> avez été invité par l'utilisateur ID {user_id}, votre code d'invitation est : {link}"}, "error": {"self_invite": "Vous ne pouvez pas vous inviter vous-même!", "already_invited": "Cet utilisateur a déjà été invité par l'utilisateur ID {invited_by}", "time_limit": "Cet utilisateur s'est inscrit il y a plus de 24 heures et ne peut pas être invité"}}, "keyboard": {"language": "langue/语言", "instructions": "instructions", "channel_notifications": "notifications_du_canal", "get_invite_code": "obtenir_code_d_invitation", "group_management": "gestion_de_groupe"}, "instructions": {"content": "🤖Guide des Fonctions TGAI🤖\n\n🌐GPT4.0 En ligne\nCommande : net\nUtilisation : net Analyser l'action Centrica / net Météo de LA aujourd'hui\n\n🔍GPT4mini\nCommande : ask (ou a)\nUtilisation : ask Quelles sont les bourses de blockchain aux États-Unis ?\nFonctionnalités : Chat, Q&A, reconnaissance d'images, suivi de la mémoire\n\n🧩GPT4o\nCommande : 4o\nUtilisation : 4o Analyser la tendance des actions XXX\nFonctionnalités : <PERSON><PERSON><PERSON> boursière, donn<PERSON> crypto, chat intelligent, reconnaissance d'images\n\n🍻Claude-h\nCommande : ck\nUtilisation : ck Comment répondre à ce message ?\nFonctionnalités : Réponses rapides, aide au chat, suivi de la mémoire\n\n🤡Face Swap\nCommande : fc\nUtilisation : Télécharger 2 photos + fc\n\n🎨Génération d'Image\nCommande : draw\nUtilisation : draw Scène de Times Square le matin\n\n🧑‍🔬OpenAI-o1\nCommande : o1\nUtilisation : o1 Analyser ceci logiquement\n\n🎐Changement de Fond\nCommande : br\nUtilisation : Envoyer une photo de personne + fond (même taille)"}, "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ", "channel": {"notification": "Rejoignez le canal et suivez toujours les progrès de TAI. Obtenez les dernières informations sur l'IA dès que possible❕❕❕\n\nBouton : Canal officiel TGAI - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "Bienvenue sur le TGAI-ChatGPT gratuit le plus puissant\nInstructions d'utilisation :\nask(a) + espace + message = question\nvision(v) Reconnaissance d'image (utilisation illimitée)\nnet(n) Modèle en ligne du site officiel (utilisation illimitée)\ndraw(d) Modèle de génération d'images (utilisation illimitée)\nsd Modèle d'échange d'images (utilise 40 unités de puissance de calcul)\nfc: Modèle d'échange de visages (utilise 20 unités de puissance de calcul)\n4o(o) Modèle GPT-4o (utilise 10 unités de puissance de calcul)\ncs: <PERSON>d<PERSON><PERSON> (utilise 10 unités de puissance de calcul)\nck: Mod<PERSON><PERSON> (utilisation illimitée)\no1: Dernier modèle o1 de OpenAI (version alpha-test, stabilisation en cours)\n\nFonctionnalités avancées, uniques sur Internet!:\n1. Toutes les commandes prennent en charge la reconnaissance d'image, il suffit d'envoyer une image (prise en charge de la reconnaissance multi-image)\n2. Prend en charge les questions contextuelles comme sur le site officiel, il suffit de se référer au texte précédent\nPour éviter les abus, chaque utilisateur dispose de 200 unités de puissance de calcul par jour, ce qui permet environ 40 utilisations de GPT-4o ou 5 utilisations de SD\n\nSi vous manquez de puissance de calcul, inviter un utilisateur à utiliser TGAI vous accordera 200 unités de puissance de calcul gratuites (valable 24 heures)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "Deux étapes pour ajouter un groupe : (ou cliquez sur le bouton ci-dessous)\n1. Ajoutez ce bot à votre groupe et définissez-le comme administrateur.\n2. Assurez-vous que vous êtes administrateur du groupe.\n3. Si vous avez effectué les deux premières étapes mais que vous ne trouvez toujours pas votre groupe, exécutez la commande /update dans le groupe.\n4. Cliquez sur le bouton du nom du groupe pour gérer votre chat de groupe", "add_bot": "【Cliquez ici pour ajouter le bot au groupe】", "prev_page": "◀️Précédent", "next_page": "Suivant▶️", "refresh": "🔄Rafraîchir", "help": "❓Aide", "group_info": "📑 Groupe sélectionné actuellement :\nNom du groupe : {title}\nID du groupe : {chat_id}\n\nVeuillez sélectionner une action :", "delete_binding": "❌ Supprimer la liaison du groupe", "back_to_list": "⬅️ Retour à la liste", "unbind_success": "Liaison du groupe supprimée avec succès !", "unbind_failed": "Échec de la suppression de la liaison, veuil<PERSON>z réessayer plus tard", "group_not_found": "Informations sur le groupe non trouvées"}, "help": {"title": "Centre d'aide :", "content": "1. Que faire si j'ai accidentellement dissocié un groupe ?\nR : Supprimez le bot du groupe et invitez-le à nouveau.\n\n2. Comment ajouter correctement le bot à un groupe ?\nR : Cliquez sur le bouton 【Cliquez ici pour ajouter le bot au groupe】 et sélectionnez votre groupe cible.\n\n3. Pourquoi ne puis-je pas voir mon groupe ?\nR : <PERSON><PERSON><PERSON><PERSON> vous assurer que :\n   - Le bot est défini comme administrateur du groupe\n   - Vous êtes administrateur du groupe\n   - Vous avez cliqué sur le bouton de mise à jour", "back": "Retour"}, "binding": {"success": "Merci de m'avoir ajouté au groupe !\nID du groupe : {chat_id}\nTitre du groupe : {title}\nLié à l'utilisateur : {user_id}\nNom d'utilisateur lié : @{username}", "failed": "Échec de la liaison ! Veuillez définir le bot comme administrateur lors de son invitation pour vérifier votre identité d'administrateur du groupe. Supprimez le bot et invitez-le à nouveau.\nID du groupe : {chat_id}\nTitre du groupe : {title}\nLié à l'utilisateur : {user_id}\nNom d'utilisateur lié : @{username}", "success_user": "Merci de m'avoir ajouté au groupe !\nID du groupe : {chat_id}\nTitre du groupe : {title}\nLié à l'utilisateur : {user_id}\nNom d'utilisateur lié : @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "Supprimer les annonces du chat", "generate_remove_ads_code": "Générer des codes de suppression d'annonces"}, "invalid_params": "Paramètres invalides. Veuillez vérifier le format.\nFormat correct : {command} <code de vérification>", "invalid_code": "Code de vérification invalide ou déjà utilisé ! Veuillez vérifier si le code est correct.\nFormat correct : {command} <code de vérification>", "remove_success": "Annonces supprimées avec succès", "remove_failed": "Échec de la suppression des annonces. Erreur : {error}", "generate_success": "Codes de suppression d'annonces générés, veuillez vérifier la pièce jointe", "error": "Échec de la suppression des annonces. Veuillez contacter l'administrateur. Erreur : {error}"}, "language": {"choose": "Veuillez sélectionner votre langue préférée:\n\nLangues actuellement prises en charge:", "changed": "✅ Votre langue a été changée en Français", "error": "Échec du changement de langue, veuil<PERSON>z réessayer plus tard", "current": "Langue actuelle: Fran<PERSON>"}}