{"errors": {"invalid_command": "잘못된 명령어", "permission_denied": "권한이 거부되었습니다. 이 작업을 수행할 수 없습니다", "vip_required": "이 기능은 VIP 회원권이 필요합니다", "general_error": "오류가 발생했습니다. 나중에 다시 시도해 주세요", "command_failed": "명령 실행에 실패했습니다. 다시 시도해 주세요"}, "invite": {"start": {"link": "당신의 초대 링크는: {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "초대 성공!\n사용자 {user_id}를 초대했고 200 계산 능력을 받았습니다!", "fail": "사용자 {user_id}를 초대했지만 초대가 실패했습니다!\n오류 메시지: {error}\n당신의 초대 링크: {link}", "invited_by": "사용자 ID {user_id}가 당신을 초대했습니다. 당신의 초대 코드: {link}"}, "error": {"self_invite": "자신을 초대할 수 없습니다!", "already_invited": "이 사용자는 이미 사용자 ID {invited_by}에 의해 초대되었습니다", "time_limit": "이 사용자는 24시간 이상 전에 등록되어 초대할 수 없습니다"}}, "keyboard": {"language": "언어/语言", "instructions": "안내", "channel_notifications": "채널_알림", "get_invite_code": "초대_코드_받기", "group_management": "그룹_관리"}, "instructions": {"content": "🤖TGAI 기능 가이드🤖\n\n🌐GPT4.0 온라인\n명령어: net\n사용법: net Centrica 주식 분석 / net LA 날씨 오늘\n\n🔍GPT4mini\n명령어: ask (또는 a)\n사용법: ask 미국에 있는 블록체인 거래소는 무엇인가요?\n기능: 채팅, Q&A, 이미지 인식, 메모리 추적\n\n🧩GPT4o\n명령어: 4o\n사용법: 4o XXX 주식 트렌드 분석\n기능: 주식 분석, 암호화 데이터, 스마트 채팅, 이미지 인식\n\n🍻Claude-h\n명령어: ck\n사용법: ck 이 메시지에 어떻게 답장할까요?\n기능: 빠른 응답, 채팅 도움, 메모리 추적\n\n🤡Face Swap\n명령어: fc\n사용법: 2개의 사진 업로드 + fc\n\n🎨이미지 생성\n명령어: draw\n사용법: draw 타임스퀘어 아침 풍경\n\n🧑‍🔬OpenAI-o1\n명령어: o1\n사용법: o1 이 논리적으로 분석\n\n🎐배경 변경\n명령어: br\n사용법: 사람 + 배경 사진 보내기 (같은 크기)"}, "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ", "channel": {"notification": "채널에 가입하고 항상 TAI의 진행 상황을 팔로우하세요. 가능한 한 빨리 AI에 대한 최신 정보를 받아보세요❕❕❕\n\n버튼: TGAI 공식 채널 - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "최강의 무료TGAI-ChatGPT에 오신 것을 환영합니다.\n사용 방법:\nask(a) + 공백 + 질문 = 질문\nvision(v)이미지 인식 (무제한 사용)\net(n) 공식 웹사이트 온라인 모델 (무제한 사용)\ndraw(d) 이미지 생성 모델 (무제한 사용)\nsd 이미지 교환 모델 (40 컴퓨팅 파워 사용)\nfc: 얼굴 교환 모델 (20 컴퓨팅 파워 사용)\n4o(o) GPT-4o 모델 (10 컴퓨팅 파워 사용)\ncs: claude-sonnet 모델 (10 컴퓨팅 파워 사용)\nck: claude-haiku 모델 (무제한 사용)\no1: OpenAI 최신 o1 모델 (알파 테스트 버전, 안정성 작업 중)\n\n인터넷에서 유일한 고급 기능:\n모든 명령은 이미지 인식을 지원하며, 이미지를 보내면 됩니다 (다중 이미지 인식 지원)\n공식 웹사이트와 동일한 문맥 질문을 지원하며, 이전 텍스트를 참조하면 됩니다\n남용을 방지하기 위해 각 사용자에게 하루 200 컴퓨팅 파워가 제공되며, 약 40번의 GPT-4o 사용 또는 5번의 SD 모델을 사용할 수 있습니다.\n\n컴퓨팅 파워가 다 소진된 경우, 한 명의 사용자를 초대하여 TGAI를 사용하면 200 컴퓨팅 파워를 무료로 제공받을 수 있습니다 (유효 기간 24시간)！", "message_photo_id": ""}}, "group": {"management": {"tutorial": "그룹을 추가하는 두 단계: (또는 아래 버튼을 클릭하세요)\n1. 이 봇을 그룹에 추가하고 관리자로 설정하세요.\n2. 자신이 그룹 관리자임을 확인하세요.\n3. 첫 번째 두 단계를 완료했지만 여전히 그룹을 찾을 수 없다면, 그룹에서 /update 명령어를 실행하세요.\n4. 그룹 이름 버튼을 클릭하여 그룹 채팅을 관리하세요", "add_bot": "【여기를 클릭하여 봇을 그룹에 추가】", "prev_page": "◀️이전", "next_page": "다음▶️", "refresh": "🔄새로 고침", "help": "❓도움말", "group_info": "📑 현재 선택된 그룹:\n그룹 이름: {title}\n그룹 ID: {chat_id}\n\n다음 작업을 선택하세요:", "delete_binding": "❌ 그룹 바인딩 삭제", "back_to_list": "⬅️ 목록으로 돌아가기", "unbind_success": "그룹 바인딩이 성공적으로 삭제되었습니다!", "unbind_failed": "바인딩 삭제에 실패했습니다. 나중에 다시 시도해 주세요", "group_not_found": "그룹 정보를 찾을 수 없습니다"}, "help": {"title": "도움말 센터:", "content": "1. 실수로 그룹을 분리했을 경우 어떻게 해야 하나요?\nA: 그룹에서 봇을 제거하고 다시 초대하세요.\n\n2. 봇을 그룹에 올바르게 추가하려면 어떻게 하나요?\nA: 【여기를 클릭하여 봇을 그룹에 추가】 버튼을 클릭하고 대상 그룹을 선택하세요.\n\n3. 왜 내 그룹을 볼 수 없나요?\nA: 다음을 확인하세요:\n   - 봇이 그룹 관리자 역할로 설정되었는지 확인하세요\n   - 본인이 그룹 관리자임을 확인하세요\n   - 새로 고침 버튼을 클릭했는지 확인하세요", "back": "뒤로"}, "binding": {"success": "그룹에 저를 추가해 주셔서 감사합니다!\n그룹 ID: {chat_id}\n그룹 제목: {title}\n사용자에 바인딩됨: {user_id}\n바인딩된 사용자명: @{username}", "failed": "바인딩 실패! 봇을 초대할 때 관리자로 설정하여 그룹 관리자 신원을 확인해 주세요. 봇을 제거하고 다시 초대해 주세요.\n그룹 ID: {chat_id}\n그룹 제목: {title}\n사용자에 바인딩됨: {user_id}\n바인딩된 사용자명: @{username}", "success_user": "그룹에 저를 추가해 주셔서 감사합니다!\n그룹 ID: {chat_id}\n그룹 제목: {title}\n사용자에 바인딩됨: {user_id}\n바인딩된 사용자명: @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "채팅 광고 삭제", "generate_remove_ads_code": "광고 삭제 코드 생성"}, "invalid_params": "잘못된 매개변수입니다. 형식을 확인해주세요.\n올바른 형식: {command} <인증 코드>", "invalid_code": "유효하지 않거나 이미 사용된 인증 코드입니다! 코드가 정확한지 확인해주세요.\n올바른 형식: {command} <인증 코드>", "remove_success": "광고가 성공적으로 삭제되었습니다", "remove_failed": "광고 삭제에 실패했습니다. 오류: {error}", "generate_success": "광고 삭제 코드가 생성되었습니다. 첨부파일을 확인해주세요", "error": "광고 삭제에 실패했습니다. 관리자에게 문의하세요. 오류: {error}"}, "language": {"choose": "선호하는 언어를 선택하세요:\n\n현재 지원되는 언어:", "changed": "✅ 언어가 한국어로 변경되었습니다", "error": "언어 변경에 실패했습니다. 나중에 다시 시도해 주세요", "current": "현재 언어: 한국어"}}