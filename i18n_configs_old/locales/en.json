{"errors": {"invalid_command": "Invalid command", "permission_denied": "Permission denied, unable to perform this operation", "vip_required": "This feature requires VIP membership", "general_error": "An error occurred, please try again later", "command_failed": "Command execution failed, please try again"}, "invite": {"start": {"link": "Your invitation link is: {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "Invitation successful!\nYou have invited user: {user_id}, and received 200 computing power!", "fail": "You invited user {user_id}, but the invitation failed!\nError message: {error}\nYour invitation link: {link}", "invited_by": "You have been invited by user ID {user_id}, your invitation code is: {link}"}, "error": {"self_invite": "You cannot invite yourself!", "already_invited": "This user has already been invited by user ID {invited_by}", "time_limit": "This user registered more than 24 hours ago and cannot be invited"}}, "keyboard": {"language": "language/语言", "instructions": "instructions", "channel_notifications": "channel_notifications", "get_invite_code": "get_invite_code", "group_management": "group_management"}, "instructions": {"content": "🤖TGAI Functions Guide🤖\n\n🌐GPT4.0 Online\nCommand: net\nUse: net Analyze Centrica stock / net LA weather today\n\n🔍GPT4mini\nCommand: ask (or a)\nUse: ask What blockchain exchanges are in US?\nFeatures: Chat, Q&A, image recognition, memory tracking\n\n🧩GPT4o\nCommand: 4o\nUse: 4o Analyze XXX stock trend\nFeatures: Stock analysis, crypto data, smart chat, image recognition\n\n🍻Claude-h\nCommand: ck\nUse: ck How to reply this message?\nFeatures: Fast responses, chat help, memory tracking\n\n🤡Face Swap\nCommand: fc\nUse: Upload 2 photos + fc\n\n🎨Image Gen\nCommand: draw\nUse: draw Times Square morning scene\n\n🧑‍🔬OpenAI-o1\nCommand: o1\nUse: o1 Analyze this logically\n\n🎐Background Change\nCommand: br\nUse: Send person + background photo (same size)", "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ"}, "vip": {"status": {"active": "Active", "inactive": "Inactive", "expired": "Expired"}, "expires_at": "Expires at: {date}"}, "channel": {"notification": "Join the channel and always follow the update progress of TAI Get the latest information on AI as soon as possible❕❕❕ \n\nButton: TGAI official channel - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "Welcome to the strongest free TGAI-ChatGPT\nUsage Instructions:\nask(a) + space + prompt = question\nvision(v) Image recognition (unlimited use)\nnet(n) Official website online model (unlimited use)\ndraw(d) Image generation model (unlimited use)\nsd Swap image model (uses 40 computation power)\nfc: Face swap model (uses 20 computation power)\n4o(o) GPT-4o model (uses 10 computation power)\ncs: claude-sonnet model (uses 10 computation power)\nck: claude-haiku model (unlimited use)\no1: OpenAI's latest o1 model (alpha test version, working on stability)\n\nAdvanced features, the only one on the internet!: \n1. All commands support image recognition, just send an image (supports multi-image recognition)\n2. Supports the same contextual questioning as the official website, just refer to previous text\nTo prevent abuse, each user has 200 computation power per day, which allows for about 40 uses of GPT-4o or 5 uses of SD\n\nIf you run out of computation power, inviting one user to use TGAI will grant you 200 computation power for free (valid for 24 hours)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "Two steps to add a group: (or click the button below)\n1. Add this bot to your group and set it as an administrator.\n2. Make sure you are a group administrator.\n3. If you have completed the first two steps but still can't find your group, execute the /update command in the group.\n4. Click on the group name button to manage your group chat", "add_bot": "【Click here to add bot to group】", "prev_page": "◀️Previous", "next_page": "Next▶️", "refresh": "🔄Refresh", "help": "❓Help", "group_info": "📑 Current selected group:\nGroup name: {title}\nGroup ID: {chat_id}\n\nPlease select an action:", "delete_binding": "❌ Delete group binding", "back_to_list": "⬅️ Back to list", "unbind_success": "Group unbound successfully!", "unbind_failed": "Unbinding failed, please try again later", "group_not_found": "Group information not found"}, "help": {"title": "Help Center:", "content": "1. What if I accidentally unbound a group?\nA: Remove the bot from the group and invite it back.\n\n2. How to correctly add the bot to a group?\nA: Click the 【Click here to add bot to group】 button and select your target group.\n\n3. Why can't I see my group?\nA: Please ensure:\n   - The bot is set as a group administrator\n   - You are a group administrator\n   - You have clicked the refresh button", "back": "Back"}, "binding": {"success": "Thanks for adding me to the group!\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}", "failed": "Binding failed! Please set the bot as an administrator when inviting it to verify your group administrator identity. Please remove the bot and invite it to the group again.\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}", "success_user": "Thanks for adding me to the group!\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "Delete chat ads", "generate_remove_ads_code": "Generate ad removal codes"}, "invalid_params": "Invalid parameters. Please check the format.\nCorrect format: {command} <verification code>", "invalid_code": "Invalid or used verification code! Please check if the code is correct.\nCorrect format: {command} <verification code>", "remove_success": "Ads removed successfully", "remove_failed": "Failed to remove ads. Error: {error}", "generate_success": "Ad removal codes generated, please check the attachment", "error": "Failed to remove ads. Please contact administrator. Error: {error}"}, "language": {"choose": "Please select your preferred language:\n\nCurrently supported languages:", "changed": "✅ Your language has been changed to English", "error": "Failed to change language, please try again later", "current": "Current language: English"}}