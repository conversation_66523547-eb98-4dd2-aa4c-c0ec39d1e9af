{"errors": {"invalid_command": "Ung<PERSON><PERSON><PERSON>hl", "permission_denied": "<PERSON><PERSON><PERSON> verweigert, diese Operation kann nicht ausgeführt werden", "vip_required": "Diese Funktion erfordert eine VIP-Mitgliedschaft", "general_error": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später erneut", "command_failed": "Befehlsausführung fehlgeschlagen, bitte versuchen Sie es erneut"}, "invite": {"start": {"link": "Ihr Einladungslink lautet: {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "Einladung erfolgreich!\nSie haben den Benutzer {user_id} eingeladen und 200 Rechenleistung erhalten!", "fail": "Sie haben den Benutzer {user_id} eingeladen, aber die Einladung ist fehlgeschlagen!\nFehlermeldung: {error}\nIhr Einladungslink: {link}", "invited_by": "<PERSON><PERSON> w<PERSON><PERSON>-ID {user_id} e<PERSON><PERSON><PERSON>, Ihr Einladungslink lautet: {link}"}, "error": {"self_invite": "Sie können sich nicht selbst einladen!", "already_invited": "<PERSON><PERSON> wurde bereits von <PERSON>-ID {invited_by} eingeladen", "time_limit": "Dieser <PERSON>utzer hat sich vor mehr als 24 Stunden registriert und kann nicht eingeladen werden"}}, "keyboard": {"language": "Sprache/语言", "instructions": "Anleitungen", "channel_notifications": "Kanalbenachrichtigungen", "get_invite_code": "Einladungscode erhalten", "group_management": "Gruppenverwaltung"}, "instructions": {"content": "🤖TGAI Funktionen Anleitung🤖\n\n🌐GPT4.0 Online\nBefehl: net\nVerwendung: net Analysiere Centrica Aktien / net Wetter in LA heute\n\n🔍GPT4mini\nBefehl: ask (oder a)\nVerwendung: ask <PERSON>e Blockchain-Börsen gibt es in den USA?\nFunktionen: Chat, Q&A, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Speicherverfolgung\n\n🧩GPT4o\nBefehl: 4o\nVerwendung: 4o Analysiere den Trend der XXX-Aktien\nFunktionen: Aktienanalyse, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>rkennung\n\n🍻Claude-h\nBefehl: ck\nVerwendung: ck Wie antworte ich auf diese Nachricht?\nFunktionen: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Speicherverfolgung\n\n🤡Face Swap\nBefehl: fc\nVerwendung: Lade 2 Fotos hoch + fc\n\n🎨Bild Gen\nBefehl: draw\nVerwendung: draw Times Square Morgenszene\n\n🧑‍🔬OpenAI-o1\nBefehl: o1\nVerwendung: o1 Analysiere dies logisch\n\n🎐Hintergrundwechsel\nBefehl: br\nVerwendung: Sende Personen- + Hintergrundfoto (gleiche Größe)"}, "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ", "channel": {"notification": "Treten Sie dem Kanal bei und verfolgen Sie immer den Fortschritt von TAI. Erhalten Sie die neuesten Informationen zu KI so schnell wie möglich❕❕❕\n\nButton: TGAI offizieller Kanal - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "Willkommen beim stärksten kostenlosen TGAI-ChatGPT\nNutzungsanleitung:\nask(a) + Leerzei<PERSON> + Eingabe = Frage\nvision(v) Bilderkennung (unbegrenzte Nutzung)\nnet(n) Offizielle Website Online-Modell (unbegrenzte Nutzung)\ndraw(d) Bildgenerierungsmodell (unbegrenzte Nutzung)\nsd Tauschbild-Modell (verbraucht 40 Rechenleistung)\nfc: Gesichtstausch-Modell (verbraucht 20 Rechenleistung)\n4o(o) GPT-4o Modell (verbraucht 10 Rechenleistung)\ncs: Claude<PERSON><PERSON>net Modell (verbraucht 10 Rechenleistung)\nck: <PERSON><PERSON><PERSON><PERSON>l (unbegrenzte Nutzung)\no1: OpenAIs neuestes o1-Modell (Alpha-Testversion, Stabilität in Arbeit)\n\nErweiterte Funktionen, einzigartig im Internet!:\n1. Alle Befehle unterstützen Bilderkennung, einfach ein Bild senden (Mehrfachbilderkennung wird unterstützt)\n2. Unterstützt kontextbezogene Fragen wie auf der offiziellen Website, einfach auf vorherigen Text verweisen\nUm Missbrauch zu verhindern, erhält jeder Benutzer 200 Rechenleistung pro Tag, das reicht für ca. 40 Nutzungen von GPT-4o oder 5 Nutzungen von SD\n\nFalls die Rechenleistung aufgebraucht ist, erhältst du 200 zusätzliche Rechenleistung für das Einladen eines Benutzers zu TGAI (gültig für 24 Stunden)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "<PERSON><PERSON> Schritte, um eine Gruppe hinzuzufügen: (oder klicken Sie auf die Schaltfläche unten)\n1. <PERSON><PERSON><PERSON> <PERSON><PERSON> diesen Bot zu Ihrer Gruppe hinzu und setzen Sie ihn als Administrator.\n2. <PERSON><PERSON><PERSON>, dass Sie ein Gruppenadministrator sind.\n3. Wenn <PERSON>e die ersten beiden Schritte abgeschlossen haben, aber Ihre Gruppe immer noch nicht finden können, führen Sie den Befehl /update in der Gruppe aus.\n4. Klicken Sie auf die Schaltfläche des Gruppennamens, um Ihren Gruppenchat zu verwalten", "add_bot": "【Klicken <PERSON> hier, um den Bot zur Gruppe hinzuzufügen】", "prev_page": "◀️Zurück", "next_page": "Weiter▶️", "refresh": "🔄Aktualisieren", "help": "❓Hilfe", "group_info": "📑 Aktuell ausgewählte Gruppe:\nGruppenname: {title}\nGruppen-ID: {chat_id}\n\nBitte wählen Sie eine Aktion aus:", "delete_binding": "❌ Gruppenbindung löschen", "back_to_list": "⬅️ Zurück zur Liste", "unbind_success": "Gruppenbindung erfolgreich entfernt!", "unbind_failed": "Entbindung fehlgeschlagen, bitte versuchen Sie es später erneut", "group_not_found": "Gruppeninformationen nicht gefunden"}, "help": {"title": "Hilfe-Center:", "content": "1. Was passiert, wenn ich versehentlich eine Gruppe entbinde?\nA: Entfernen Sie den Bot aus der Gruppe und laden Si<PERSON> ihn wieder ein.\n\n2. Wie füge ich den Bot korrekt einer Gruppe hinzu?\nA: Klicken Sie auf die Schaltfläche 【Klicken Sie hier, um den Bot zur Gruppe hinzuzufügen】 und wählen Sie Ihre Zielgruppe aus.\n\n3. Warum kann ich meine Gruppe nicht sehen?\nA: <PERSON>te stellen Si<PERSON> sicher:\n   - Der Bot ist als Gruppenadministrator gesetzt\n   - Sie sind Gruppenadministrator\n   - Sie haben die Schaltfläche \"Aktualisieren\" gedrückt", "back": "Zurück"}, "binding": {"success": "<PERSON><PERSON>, dass Sie mich zur Gruppe hinzugefügt haben!\nGruppen-ID: {chat_id}\nGruppentitel: {title}\nGebunden an Benutzer: {user_id}\nGebundener Benutzername: @{username}", "failed": "Bindung fehlgeschlagen! Bitte setzen Si<PERSON> den <PERSON>t beim Einladen als Administrator, um Ihre Gruppenadministrator-Identität zu überprüfen. Entfernen Sie den Bot und laden Si<PERSON> ihn erneut in die Gruppe ein.\nGruppen-ID: {chat_id}\nGruppentitel: {title}\nGebunden an Benutzer: {user_id}\nGebundener Benutzername: @{username}", "success_user": "<PERSON><PERSON>, dass Sie mich zur Gruppe hinzugefügt haben!\nGruppen-ID: {chat_id}\nGruppentitel: {title}\nGebunden an Benutzer: {user_id}\nGebundener Benutzername: @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "Chat-Werbung löschen", "generate_remove_ads_code": "Werbung Entfernen Codes generieren"}, "invalid_params": "Ungültige Parameter. Bitte überprüfen Sie das Format.\nKorrektes Format: {command} <Bestätigungscode>", "invalid_code": "Ungültiger oder bereits verwendeter Bestätigungscode! Bitte überprüfen Sie, ob der Code korrekt ist.\nKorrektes Format: {command} <Bestätigungscode>", "remove_success": "Werbung erfolgreich entfernt", "remove_failed": "Werbung konnte nicht entfernt werden. <PERSON>hler: {error}", "generate_success": "Werbung Entfernen Codes wurden generiert, bitte überprüfen Sie den Anhang", "error": "Fehler beim Entfernen der Werbung. Bitte kontaktieren Sie den Administrator. <PERSON><PERSON>: {error}"}, "language": {"choose": "Bitte wählen Sie Ihre bevorzugte Sprache:\n\nDerzeit unterstützte Sprachen:", "changed": "✅ Ihre Sprache wurde auf Deutsch geändert", "error": "Fehler beim Ändern der Sprache, bitte versuchen Sie es später erneut", "current": "Aktuelle Sprache: Deutsch"}}