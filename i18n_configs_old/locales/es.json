{"errors": {"invalid_command": "Comand<PERSON>", "permission_denied": "Permiso denegado, no se puede realizar esta operación", "vip_required": "Esta función requiere una membresía VIP", "general_error": "<PERSON><PERSON><PERSON><PERSON> un error, por favor intente nuevamente más tarde", "command_failed": "La ejecución del comando falló, por favor intente de nuevo"}, "invite": {"start": {"link": "Tu enlace de invitación es: {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "¡Invitación exitosa!\nHas invitado al usuario: {user_id}, y recibido 200 de poder computacional!", "fail": "Has invitado al usuario {user_id}, pero la invitación falló!\nMensaje de error: {error}\nTu enlace de invitación: {link}", "invited_by": "Has sido invitado por el usuario con ID {user_id}, tu código de invitación es: {link}"}, "error": {"self_invite": "¡No puedes invitarte a ti mismo!", "already_invited": "Este usuario ya ha sido invitado por el usuario ID {invited_by}", "time_limit": "Este usuario se registró hace más de 24 horas y no puede ser invitado"}}, "keyboard": {"language": "idioma/语言", "instructions": "instrucciones", "channel_notifications": "notificaciones_del_canal", "get_invite_code": "obtener_codigo_de_invite", "group_management": "gestión_de_grupos"}, "instructions": {"content": "🤖Guía de Funciones TGAI🤖\n\n🌐GPT4.0 Online\nComando: net\nUso: net Analizar acciones de Centrica / net Clima en LA hoy\n\n🔍GPT4mini\nComando: ask (o a)\nUso: ask ¿Qué intercambios de blockchain existen en EE. UU.?\nFunciones: Chat, Preguntas y respuestas, Reconocimiento de imágenes, Seguimiento de memoria\n\n🧩GPT4o\nComando: 4o\nUso: 4o Analizar la tendencia de las acciones de XXX\nFunciones: Análisis de acciones, datos de cripto, chat inteligente, reconocimiento de imágenes\n\n🍻Claude-h\nComando: ck\nUso: ck ¿Cómo responder a este mensaje?\nFunciones: Respuestas rápidas, ayuda con el chat, seguimiento de memoria\n\n🤡Face Swap\nComando: fc\nUso: Subir 2 fotos + fc\n\n🎨Generación de Imágenes\nComando: draw\nUso: draw Escena de Times Square por la mañana\n\n🧑‍🔬OpenAI-o1\nComando: o1\nUso: o1 Analizar esto lógicamente\n\n🎐Cambio de Fondo\nComando: br\nUso: Enviar foto de persona + fondo (del mismo tamaño)"}, "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ", "channel": {"notification": "Únete al canal y sigue siempre el progreso de TAI. ¡Obtén la información más reciente sobre IA lo antes posible❕❕❕\n\nBotón: Canal oficial de TGAI - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "Bienvenido al TGAI-ChatGPT gratuito más potente\nInstrucciones de uso:\nask(a) + espacio + mensaje = pregunta\nvision(v) Reconocimiento de imágenes (uso ilimitado)\nnet(n) Modelo en línea del sitio web oficial (uso ilimitado)\ndraw(d) Modelo de generación de imágenes (uso ilimitado)\nsd Modelo de intercambio de imágenes (consume 40 unidades de potencia de cálculo)\nfc: Modelo de intercambio de rostros (consume 20 unidades de potencia de cálculo)\n4o(o) Modelo GPT-4o (consume 10 unidades de potencia de cálculo)\ncs: <PERSON><PERSON> Claude-Sonnet (consume 10 unidades de potencia de cálculo)\nck: <PERSON><PERSON>-<PERSON> (uso ilimitado)\no1: Último modelo o1 de OpenAI (versión de prueba alfa, en proceso de estabilización)\n\nFunciones avanzadas, ¡únicas en Internet!:\n1. Todos los comandos admiten reconocimiento de imágenes, solo envía una imagen (se admite reconocimiento de múltiples imágenes)\n2. Admite preguntas contextuales como en el sitio web oficial, solo haz referencia a texto anterior\nPara evitar abusos, cada usuario tiene 200 unidades de potencia de cálculo por día, lo que permite alrededor de 40 usos de GPT-4o o 5 usos de SD\n\nSi te quedas sin potencia de cálculo, ¡invitar a un usuario a usar TGAI te otorgará 200 unidades de potencia de cálculo gratis (válido por 24 horas)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "Dos pasos para agregar un grupo: (o haz clic en el botón de abajo)\n1. Agrega este bot a tu grupo y configúralo como administrador.\n2. Asegúrate de ser un administrador del grupo.\n3. <PERSON> has completado los dos primeros pasos pero aún no puedes encontrar tu grupo, ejecuta el comando /update en el grupo.\n4. Haz clic en el botón del nombre del grupo para gestionar tu chat grupal", "add_bot": "【Haz clic aquí para agregar el bot al grupo】", "prev_page": "◀️Anterior", "next_page": "Siguiente▶️", "refresh": "🔄Actualizar", "help": "❓Ayuda", "group_info": "📑 Grupo seleccionado actualmente:\nNombre del grupo: {title}\nID del grupo: {chat_id}\n\nPor favor, selecciona una acción:", "delete_binding": "❌ Eliminar vínculo de grupo", "back_to_list": "⬅️ Volver a la lista", "unbind_success": "¡Vinculación del grupo eliminada con éxito!", "unbind_failed": "No se pudo eliminar la vinculación, por favor intente más tarde", "group_not_found": "Información del grupo no encontrada"}, "help": {"title": "Centro de ayuda:", "content": "1. ¿Qué hago si accidentalmente desvinculé un grupo?\nR: Elimina el bot del grupo e invítalo de nuevo.\n\n2. ¿Cómo agregar correctamente el bot a un grupo?\nR: Haz clic en el botón 【Haz clic aquí para agregar el bot al grupo】 y selecciona tu grupo objetivo.\n\n3. ¿Por qué no puedo ver mi grupo?\nR: Asegúrate de lo siguiente:\n   - El bot está configurado como administrador del grupo\n   - Eres administrador del grupo\n   - Has hecho clic en el botón de actualizar", "back": "Atrás"}, "binding": {"success": "¡Gracias por agregarme al grupo!\nID del grupo: {chat_id}\nTítulo del grupo: {title}\nVinculado al usuario: {user_id}\nNombre de usuario vinculado: @{username}", "failed": "¡La vinculación falló! Por favor, configura el bot como administrador al invitarlo para verificar tu identidad de administrador del grupo. Elimina el bot e invítalo de nuevo.\nID del grupo: {chat_id}\nTítulo del grupo: {title}\nVinculado al usuario: {user_id}\nNombre de usuario vinculado: @{username}", "success_user": "¡Gracias por agregarme al grupo!\nID del grupo: {chat_id}\nTítulo del grupo: {title}\nVinculado al usuario: {user_id}\nNombre de usuario vinculado: @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "Eliminar anuncios del chat", "generate_remove_ads_code": "Generar códigos de eliminación de anuncios"}, "invalid_params": "Parámetros no válidos. <PERSON><PERSON> favor, revise el formato.\nFormato correcto: {command} <código de verificación>", "invalid_code": "¡Código de verificación no válido o ya utilizado! Por favor, verifique si el código es correcto.\nFormato correcto: {command} <código de verificación>", "remove_success": "Anuncios eliminados con éxito", "remove_failed": "No se pudo eliminar el anuncio. Error: {error}", "generate_success": "Códigos de eliminación de anuncios generados, por favor revise el archivo adjunto", "error": "Error al eliminar los anuncios. Por favor contacte con el administrador. Error: {error}"}, "language": {"choose": "Por favor seleccione su idioma preferido:\n\nIdiomas actualmente soportados:", "changed": "✅ Su idioma ha sido cambiado a Español", "error": "No se pudo cambiar el idioma, por favor intente nuevamente más tarde", "current": "Idioma actual: Español"}}