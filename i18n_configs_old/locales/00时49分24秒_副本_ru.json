{"errors": {"invalid_command": "Неверная команда", "permission_denied": "Доступ запрещен, невозможно выполнить эту операцию", "vip_required": "Эта функция требует подписки VIP", "general_error": "Произошла ошибка, пожалуйста, попробуйте позже", "command_failed": "Не удалось выполнить команду, пожалуйста, попробуйте снова"}, "invite": {"start": {"link": "Ваша ссылка на приглашение: {link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "Приглашение успешно!\nВы пригласили пользователя: {user_id}, и получили 200 вычислительной мощности!", "fail": "Вы пригласили пользователя {user_id}, но приглашение не удалось!\nСообщение об ошибке: {error}\nВаша ссылка на приглашение: {link}", "invited_by": "Вы были приглашены пользователем с ID {user_id}, ваш код приглашения: {link}"}, "error": {"self_invite": "Вы не можете пригласить себя!", "already_invited": "Этот пользователь уже был приглашен пользователем с ID {invited_by}", "time_limit": "Этот пользователь зарегистрировался более 24 часов назад и не может быть приглашен"}}, "keyboard": {"language": "язык/语言", "instructions": "инструкции", "channel_notifications": "уведомления_канала", "get_invite_code": "получить_код_приглашения", "group_management": "управление_группой"}, "instructions": {"content": "🤖Руководство по функциям TGAI🤖\n\n🌐GPT4.0 Онлайн\nКоманда: net\nИспользование: net Анализ акций Centrica / net Погода в ЛА сегодня\n\n🔍GPT4mini\nКоманда: ask (или a)\nИспользование: ask Какие блокчейн-обменники есть в США?\nФункции: Чат, Вопросы и ответы, Распознавание изображений, Отслеживание памяти\n\n🧩GPT4o\nКоманда: 4o\nИспользование: 4o Анализ тренда акций XXX\nФункции: Анализ акций, данные о криптовалюте, умный чат, распознавание изображений\n\n🍻Claude-h\nКоманда: ck\nИспользование: ck Как ответить на это сообщение?\nФункции: Быстрые ответы, помощь в чате, отслеживание памяти\n\n🤡Face Swap\nКоманда: fc\nИспользование: Загрузить 2 фото + fc\n\n🎨Генерация изображений\nКоманда: draw\nИспользование: draw Утренний пейзаж на Таймс-сквер\n\n🧑‍🔬OpenAI-o1\nКоманда: o1\nИспользование: o1 Логический анализ этого\n\n🎐Смена фона\nКоманда: br\nИспользование: Отправить фото человека + фон (одинакового размера)"}, "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ", "channel": {"notification": "Присоединяйтесь к каналу и всегда следите за ходом обновлений TAI. Получайте последние новости об ИИ как можно быстрее❕❕❕\n\nКнопка: Официальный канал TGAI - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "Добро пожаловать в самый мощный бесплатный TGAI-ChatGPT\nИнструкции по использованию:\nask(a) + пробел + запрос = вопрос\nvision(v) Распознавание изображений (неограниченное использование)\nnet(n) Онлайн-модель официального сайта (неограниченное использование)\ndraw(d) Модель генерации изображений (неограниченное использование)\nsd Модель замены изображений (использует 40 вычислительных единиц)\nfc: Модель замены лиц (использует 20 вычислительных единиц)\n4o(o) Модель GPT-4o (использует 10 вычислительных единиц)\ncs: Модель Claude-Sonnet (использует 10 вычислительных единиц)\nck: Модель Claude-Haiku (неограниченное использование)\no1: Новейшая модель o1 от OpenAI (альфа-тестирование, работает над стабильностью)\n\nРасширенные функции, единственные в интернете!:\n1. Все команды поддерживают распознавание изображений, просто отправьте изображение (поддерживается распознавание нескольких изображений)\n2. Поддерживает контекстные вопросы, как на официальном сайте, просто ссылайтесь на предыдущий текст\nЧтобы предотвратить злоупотребления, каждому пользователю предоставляется 200 вычислительных единиц в день, чего хватает на 40 использований GPT-4o или 5 использований SD\n\nЕсли у вас закончились вычислительные единицы, приглашение одного пользователя в TGAI даст вам 200 бесплатных вычислительных единиц (действительно 24 часа)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "Два шага для добавления группы: (или нажмите кнопку ниже)\n1. Добавьте этого бота в свою группу и установите его администратором.\n2. Убедитесь, что вы являетесь администратором группы.\n3. Если вы выполнили первые два шага, но не можете найти свою группу, выполните команду /update в группе.\n4. Нажмите на кнопку с названием группы, чтобы управлять вашим групповым чатом", "add_bot": "【Нажмите здесь, чтобы добавить бота в группу】", "prev_page": "◀️Предыдущая", "next_page": "Следующая▶️", "refresh": "🔄Обновить", "help": "❓Помощь", "group_info": "📑 Текущая выбранная группа:\nНазвание группы: {title}\nID группы: {chat_id}\n\nВыберите действие:", "delete_binding": "❌ Удалить привязку группы", "back_to_list": "⬅️ Вернуться в список", "unbind_success": "Привязка группы успешно удалена!", "unbind_failed": "Не удалось удалить привязку, попробуйте позже", "group_not_found": "Информация о группе не найдена"}, "help": {"title": "Центр помощи:", "content": "1. Что делать, если я случайно отвязал группу?\nО: Удалите бота из группы и пригласите его снова.\n\n2. Как правильно добавить бота в группу?\nО: Нажмите кнопку 【Нажмите здесь, чтобы добавить бота в группу】 и выберите вашу целевую группу.\n\n3. Почему я не вижу свою группу?\nО: Пожалуйста, убедитесь:\n   - Бот установлен как администратор группы\n   - Вы являетесь администратором группы\n   - Вы нажали кнопку обновления", "back": "Назад"}, "binding": {"success": "Спасибо, что добавили меня в группу!\nID группы: {chat_id}\nНазвание группы: {title}\nПривязан к пользователю: {user_id}\nПривязанный пользователь: @{username}", "failed": "Ошибка привязки! Пожалуйста, установите бота как администратора при его приглашении, чтобы подтвердить вашу личность администратора группы. Удалите бота и пригласите его снова.\nID группы: {chat_id}\nНазвание группы: {title}\nПривязан к пользователю: {user_id}\nПривязанный пользователь: @{username}", "success_user": "Спасибо, что добавили меня в группу!\nID группы: {chat_id}\nНазвание группы: {title}\nПривязан к пользователю: {user_id}\nПривязанный пользователь: @{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "Удалить чат-рекламу", "generate_remove_ads_code": "Сгенерировать коды для удаления рекламы"}, "invalid_params": "Неверные параметры. Пожалуйста, проверьте формат.\nПравильный формат: {command} <код подтверждения>", "invalid_code": "Неверный или уже использованный код подтверждения! Пожалуйста, проверьте правильность кода.\nПравильный формат: {command} <код подтверждения>", "remove_success": "Реклама успешно удалена", "remove_failed": "Не удалось удалить рекламу. Ошибка: {error}", "generate_success": "Коды для удаления рекламы сгенерированы, пожалуйста, проверьте вложение", "error": "Не удалось удалить рекламу. Пожалуйста, свяжитесь с администратором. Ошибка: {error}"}, "language": {"choose": "Пожалуйста, выберите предпочитаемый язык:\n\nПоддерживаемые языки в настоящее время:", "changed": "✅ Ваш язык был изменен на русский", "error": "Не удалось изменить язык, пожалуйста, попробуйте позже", "current": "Текущий язык: Русский"}}