{"errors": {"invalid_command": "无效的命令", "permission_denied": "权限不足，无法执行此操作", "vip_required": "此功能需要VIP会员才能使用", "general_error": "发生错误，请稍后重试", "command_failed": "命令执行失败，请重试"}, "invite": {"start": {"link": "您的邀请链接是：{link}", "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ", "success": "邀请成功！\n您已邀请用户：{user_id}，获得200算力！", "fail": "您邀请的用户 {user_id} 邀请失败！\n错误信息：{error}\n您的邀请链接：{link}", "invited_by": "您已被用户ID {user_id} 邀请, 你的邀请码是：{link}"}, "error": {"self_invite": "您不能邀请自己！", "already_invited": "该用户已被用户ID {invited_by} 邀请", "time_limit": "该用户注册时间超过24小时，无法被邀请"}}, "keyboard": {"language": "language/语言", "instructions": "使用说明", "channel_notifications": "频道通知", "get_invite_code": "获取邀请码", "group_management": "群组管理"}, "instructions": {"content": "🤖TGAI使用功能大全🤖\n\n🌐联网版GPT4.0\n命令：net\n操作：net 帮我分析 Centrica 这支股票/net 美国洛杉矶今天天气如何？\n\n🔍GPT4mini：\n命令：ask\n简约命令：a\n操作：ask 美国有哪些区块链交易所（ask后面需要添加空格）\n功能：聊天，帮助解答，多图识别，带数据记忆跟踪（一直引用上一条信息回复，适用于发展型聊天跟进）\n\n🧩GPT4o：\n命令：4o\n操作：4o 帮我分析Xxx这支股票未来的走势如何？（4o后面需要添加空格）\n功能：全方位解析股票数据，加密货币数据，高情商对话解答，识图解析数据。带数据记忆功能（一直引用上一条信息回复，适用于发展型聊天跟进）支持多图识图功能\n\n🍻Claude-h：\n命令：ck\n操作：ck xxxxxxxx我将如何高情商回复这句话？\n功能：高情商AI模型，反应速度比cs更快！如果是解决聊天困扰，请用ck解答。带数据记忆功能（一直引用上一条信息回复，适用于发展型聊天跟进）支持多图识图功能。\n\n🤡换脸AI：\n命令：fc\n操作：上传两张照片➕fc（换脸结果为前脸换后脸）\n解答：换脸AI是结合人体五官各种体征合成更自然的表情效果。\n\n🎨生图AI：\n命令：draw\n操作：draw 早上在纽约时代广场吃汉堡和可乐\n解答：这次换成拟真照片生成模型，只要你的指令输入到位，照片就会更加真实！\n\n🧑‍🔬OpenAI-o1（最新AI）\n命令：o1\n操作：o1 帮我解答这句话如何回答？帮我有逻辑的分析这段话！\n\n🎐AI换背景（最新AI）\n命令：br\n操作：br （人物➕背景照片一起发送）\n换背景的照片和背景照片大小要一致！不然会代码错乱！", "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ"}, "vip": {"status": {"active": "已激活", "inactive": "未激活", "expired": "已过期"}, "expires_at": "到期时间：{date}"}, "channel": {"notification": "加入频道时刻关注TGAI更新进度\n\n第一时间掌握AI新资讯❕❕❕\n\n按钮：TGAI官方频道 - @ChatGPT_TGAI", "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"}, "start": {"welcome": {"message": "欢迎使用全网最强免费\nTGAI-ChatGPT\n使用说明:\nask(a) + 空格 + 提示词 = 提问\nvision(v) 识图（无限使用）\nnet(n) 官网联网模型（无限使用）\ndraw(d) 生照片模型（无限使用）\nsd 换图模型（消耗40算力）\nfc: 换脸模型（消耗20算力）\n4o(o) 为gpt-4o模型（消耗10算力）\ncs: claude-sonnet模型（消耗10算力）\nck: claude-haiku模型（无限使用）\no1: openai最新o1模型(alpha测试版 正在为稳定性努力中)\n\n高级特性, 全网唯一! : \n1. 所有命令均支持识图, 只需发送图片即可(并且支持多图识别)\n2. 支持和官网一样的上下文提问, 只需引用上文即可\n为防止滥用, 每人每天200算力, 约可以使用40次gpt-4o或者5次sd\n\n如算力不够用, 邀请一位用户使用TGAI可以免费获得200算力(24h有效)!", "message_photo_id": ""}}, "group": {"management": {"tutorial": "添加群组只需两步：（或点击下方按钮）\n1. 将机器人添加到群组并设置为管理员\n2. 确保您是群组管理员\n3. 如果完成前两步仍然找不到群组，请在群组中执行 /update 命令\n4. 点击群组名称按钮管理您的群组", "add_bot": "【点击这里将机器人添加到群组】", "prev_page": "◀️上一页", "next_page": "下一页▶️", "refresh": "🔄刷新", "help": "❓帮助", "group_info": "📑 当前选择的群组：\n群组名称：{title}\n群组ID：{chat_id}\n\n请选择操作：", "delete_binding": "❌ 解除群组绑定", "back_to_list": "⬅️ 返回列表", "unbind_success": "群组解绑成功！", "unbind_failed": "解绑失败，请稍后重试", "group_not_found": "未找到群组信息"}, "help": {"title": "帮助中心：", "content": "1. 如果我不小心解绑了群组怎么办？\n答：将机器人从群组移除后重新邀请即可。\n\n2. 如何正确添加机器人到群组？\n答：点击【点击这里将机器人添加到群组】按钮，选择目标群组。\n\n3. 为什么我看不到我的群组？\n答：请确保：\n   - 机器人被设置为群组管理员\n   - 您是群组管理员\n   - 您已点击刷新按钮", "back": "返回"}, "binding": {"success": "感谢将我添加到群组！\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}", "failed": "绑定失败！邀请机器人时请将其设置为管理员以验证您的群组管理员身份。请将机器人移除后重新邀请到群组。\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}", "success_user": "感谢将我添加到群组！\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}"}}, "chat_ads": {"commands": {"remove_chat_ads": "去广告", "generate_remove_ads_code": "生成去广告"}, "invalid_params": "参数无效。请检查格式。\n正确格式：{command} <验证码>", "invalid_code": "验证码无效或已使用！请检查验证码是否正确。\n正确格式：{command} <验证码>", "remove_success": "广告移除成功", "remove_failed": "移除广告失败。错误：{error}", "generate_success": "广告移除码已生成，请查看附件", "error": "移除广告失败。请联系管理员。错误：{error}"}, "language": {"choose": "请选择您的语言：\n\n当前支持的语言：", "changed": "✅ 您的语言已更改为中文", "error": "更改语言失败，请稍后重试", "current": "当前语言：中文"}}