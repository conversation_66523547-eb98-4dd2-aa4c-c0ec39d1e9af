"""
全局翻译键定义
"""

class CommonTranslationKeys:
    class Errors:
        INVALID_COMMAND = 'errors.invalid_command' # args: {}
        PERMISSION_DENIED = 'errors.permission_denied' # args: {}
        VIP_REQUIRED = 'errors.vip_required' # args: {}
        GENERAL = 'errors.general_error' # args: {}
        COMMAND_FAILED = 'errors.command_failed' # args: {}

class GroupTranslationKeys:
    class Management:
        TUTORIAL = 'group.management.tutorial' # args: {}
        ADD_BOT = 'group.management.add_bot' # args: {}
        PREV_PAGE = 'group.management.prev_page' # args: {}
        NEXT_PAGE = 'group.management.next_page' # args: {}
        REFRESH = 'group.management.refresh' # args: {}
        HELP = 'group.management.help' # args: {}
        GROUP_INFO = 'group.management.group_info' # args: {'title': str, 'chat_id': str}
        DELETE_BINDING = 'group.management.delete_binding' # args: {}
        BACK_TO_LIST = 'group.management.back_to_list' # args: {}
        UNBIND_SUCCESS = 'group.management.unbind_success' # args: {}
        UNBIND_FAILED = 'group.management.unbind_failed' # args: {}
        GROUP_NOT_FOUND = 'group.management.group_not_found' # args: {}
    
    class Help:
        TITLE = 'group.help.title' # args: {}
        CONTENT = 'group.help.content' # args: {}
        BACK = 'group.help.back' # args: {}
    
    class Binding:
        SUCCESS = 'group.binding.success' # args: {'chat_id': str, 'title': str, 'user_id': str, 'username': str}
        FAILED = 'group.binding.failed' # args: {'chat_id': str, 'title': str, 'user_id': str, 'username': str}
        SUCCESS_USER = 'group.binding.success_user' # args: {'chat_id': str, 'title': str, 'user_id': str, 'username': str}

class StartTranslationKeys:    
    class Welcome:
        MESSAGE = 'start.welcome.message'
        MESSAGE_PHOTO_ID = 'start.welcome.message_photo_id'

class VipTranslationKeys:
    class Status:
        ACTIVE = 'vip.status.active'
        INACTIVE = 'vip.status.inactive'
        EXPIRED = 'vip.status.expired'
    EXPIRES_AT = 'vip.expires_at'

class ChatAdsTranslationKeys:
    class Commands:
        REMOVE_CHAT_ADS = 'chat_ads.commands.remove_chat_ads' # args: {}
        GENERATE_REMOVE_ADS_CODE = 'chat_ads.commands.generate_remove_ads_code' # args: {}
    INVALID_PARAMS = 'chat_ads.invalid_params' # args: {'command': str}
    INVALID_CODE = 'chat_ads.invalid_code' # args: {'command': str}
    REMOVE_SUCCESS = 'chat_ads.remove_success' # args: {}
    REMOVE_FAILED = 'chat_ads.remove_failed' # args: {'error': str}
    GENERATE_SUCCESS = 'chat_ads.generate_success' # args: {}
    ERROR = 'chat_ads.error' # args: {'error': str}

class InstructionsTranslationKeys:
    TITLE = 'instructions.title' # args: {}
    CONTENT = 'instructions.content' # args: {}
    PHOTO_ID = 'instructions.photo_id' # args: {}

class LanguageTranslationKeys:
    CHOOSE = 'language.choose'
    CHANGED = 'language.changed'
    ERROR = 'language.error'
    CURRENT = 'language.current'

class InviteTranslationKeys:
    class Start:
        LINK = 'invite.start.link' # args: {'link': str}
        SUCCESS = 'invite.start.success' # args: {'user_id': str}
        FAIL = 'invite.start.fail' # args: {'user_id': str, 'error': str, 'link': str}
        INVITED_BY = 'invite.start.invited_by' # args: {'user_id': str, 'link': str} 
        LINK_PHOTO_ID = 'invite.start.link_photo_id'
    class Error:
        SELF_INVITE = 'invite.error.self_invite' # args: {}
        ALREADY_INVITED = 'invite.error.already_invited' # args: {'invited_by': str}
        TIME_LIMIT = 'invite.error.time_limit' # args: {}

class KeyboardTranslationKeys:
    # commamds表示都是以命令的形式去使用的
    class Commands:
        LANGUAGE = 'keyboard.language'
        INSTRUCTIONS = 'keyboard.instructions'
        CHANNEL_NOTIFICATIONS = 'keyboard.channel_notifications'
        GET_INVITE_CODE = 'keyboard.get_invite_code'
        GROUP_MANAGEMENT = 'keyboard.group_management'

class ChannelTranslationKeys:
    NOTIFICATION = 'channel.notification' 
    NOTIFICATION_PHOTO_ID = 'channel.notification_photo_id'