# Telegram ChatBot Go v3

> 🤖 一个现代化的 Telegram Bot 项目，使用 Go 语言开发，支持国际化、中间件、模块化架构

[![Go Version](https://img.shields.io/badge/Go-1.23+-blue.svg)](https://golang.org/)
[![Telegram Bot API](https://img.shields.io/badge/Telegram%20Bot%20API-v4-green.svg)](https://core.telegram.org/bots/api)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ 特性

- 🏗️ **模块化架构** - 基于模块的三层架构设计
- 🌍 **国际化支持** - 完整的多语言支持系统
- 🔧 **中间件系统** - 灵活的请求处理管道
- 📱 **智能路由** - 支持命令、消息、回调查询的统一处理
- 🎯 **类型安全** - 利用 Go 的静态类型系统
- ⚡ **高性能** - 原生并发支持，适合高负载场景
- 🛡️ **错误处理** - 完善的错误恢复和日志系统

## 📁 项目结构

```
telegram-chatbot-go/
├── cmd/
│   └── bot/
│       └── main.go                 # 应用程序入口
├── internal/
│   ├── config/                     # 配置管理
│   ├── handler/                    # 请求处理器
│   │   ├── handler.go             # 核心处理逻辑
│   │   ├── command.go             # 命令处理
│   │   ├── message.go             # 消息处理
│   │   └── callback.go            # 回调查询处理
│   ├── service/                    # 业务服务层
│   │   ├── command/               # 命令服务
│   │   └── callback/              # 回调服务
│   ├── middleware/                 # 中间件
│   ├── i18n/                      # 国际化
│   ├── keyboard/                  # 键盘布局
│   ├── menu/                      # 菜单系统
│   ├── model/                     # 数据模型
│   ├── types/                     # 类型定义
│   ├── utils/                     # 工具函数
│   └── svc/                       # 服务上下文
├── data/
│   └── i18n_configs/
│       └── locales/               # 语言文件
│           ├── zh_CN.json         # 中文
│           └── en.json            # 英文
├── etc/
│   └── config.yaml.example        # 配置文件模板
├── docs/                          # 文档
└── go.mod
```

## 🚀 快速开始

### 前置要求

- Go 1.23 或更高版本
- Telegram Bot Token (从 [@BotFather](https://t.me/botfather) 获取)

### 安装

1. **克隆项目**

   ```bash
   git clone https://github.com/Kiyliy/telegram-chatbot-go-v3.git
   cd telegram-chatbot-go-v3
   ```

2. **安装依赖**

   ```bash
   go mod download
   ```

3. **配置环境**

   ```bash
   # 复制配置文件
   cp etc/config.yaml.example etc/config.yaml

   # 编辑配置文件，填入你的 Bot Token
   vim etc/config.yaml
   ```

4. **运行项目**
   ```bash
   go run cmd/bot/bot.go
   ```

## ⚙️ 配置

在 `etc/config.yaml` 中配置你的 Bot：

```yaml
Bot:
  Token: "YOUR_BOT_TOKEN_HERE"
  Timeout: 10s
# 其他配置项...
```

## 🏗️ 架构设计

### 核心组件

#### 1. 服务上下文 (ServiceContext)

统一管理所有服务和依赖：

```go
type ServiceContext struct {
    Config     config.Config
    Bot        *tele.Bot
    I18n    *i18n.Manager
    // 其他服务...
}
```

#### 2. 处理器系统 (Handler)

模块化的请求处理架构：

- **命令处理器** - 处理 `/start`, `/help` 等命令
- **消息处理器** - 处理文本、图片、文件等消息
- **回调处理器** - 处理内联键盘回调

#### 3. 中间件系统

支持请求处理管道：

- **日志中间件** - 记录请求日志
- **错误恢复** - 全局错误处理
- **上下文注入** - 自动注入用户信息
- **自动转义** - Markdown 格式处理

## 📦 部署

### Docker 部署

```dockerfile
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o bot ./cmd/bot/

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/bot .
CMD ["./bot"]
```

### 二进制部署

```bash
# 编译
go build -o bot ./cmd/bot/

# 运行
./bot
```

## 🙏 致谢

- [telebot](https://github.com/tucnak/telebot) - Telegram Bot 库
- [Telegram Bot API](https://core.telegram.org/bots/api) - 官方 API

---
