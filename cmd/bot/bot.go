package main

import (
	"fmt"
	"log"
	"net/http"
	"runtime/debug"
	"time"

	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/handler"
	"telegram-chatbot-go/internal/middleware"
	"telegram-chatbot-go/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
	m "gopkg.in/telebot.v4/middleware"
)

func main() {
	// 全局panic恢复
	defer func() {
		if r := recover(); r != nil {
			log.Printf("程序发生panic: %v\n堆栈信息:\n%s", r, debug.Stack())
		}
	}()

	// 加载配置
	var c config.Config
	conf.MustLoad("data/config/config.json", &c)
	logx.MustSetup(c.Log)

	// 创建Bot实例
	pref := tele.Settings{
		Token:  c.Bot.To<PERSON>,
		Poller: &tele.LongPoller{Timeout: c.Bot.Timeout},
	}

	b, err := tele.NewBot(pref)
	if err != nil {
		log.Fatal("创建Bot失败:", err)
	}

	// 创建ServiceContext
	svcCtx := svc.NewServiceContext(c, b)

	// 中间件注册
	b.Use(middleware.RecoveryMiddleware)        // 1. 最外层 - 捕获panic
	b.Use(middleware.MediaGroupMiddleware)      // 2. 媒体组处理（必须在ContextMiddleware之前）
	b.Use(middleware.ContextMiddleware(svcCtx)) // 3. 注入注入ServiceContext，TimeoutContext
	b.Use(middleware.LoggingMiddleware)         // 4. 日志中间件
	b.Use(middleware.ErrorHandlerMiddleware)    // 5. 统一错误处理
	b.Use(middleware.ThreadIgnoreMiddleware)    // 6. 线程ID处理
	// 如果开启EnableEavesdropMode则侦听所有群组消息
	if c.Operation.EnableEavesdropMode {
		b.Use(middleware.MessageRecordMiddleware) // 消息记录
	}
	b.Use(middleware.UserSetupMiddleware) // 7. 用户设置
	b.Use(m.AutoRespond())                // 8. 自动应答

	// 创建处理器
	h := handler.NewHandler(b, svcCtx)
	h.Init()

	// 启动HTTP服务器（如果启用）
	if c.HTTP.Enabled {
		go startHTTPServer(&c, svcCtx)
	}

	fmt.Println("🤖 =====================================")
	fmt.Println("🚀 Telegram Bot 启动成功!")
	fmt.Println("📡 正在监听消息...")
	fmt.Println("🤖 =====================================")

	b.Start()
}

// startHTTPServer 启动HTTP服务器
func startHTTPServer(c *config.Config, svcCtx *svc.ServiceContext) {
	// 创建支付处理器
	paymentHandler := handler.NewPaymentHandler(svcCtx)

	// 创建HTTP路由
	mux := http.NewServeMux()

	// 注册支付回调端点
	mux.HandleFunc("/api/v1/payment/callback", paymentHandler.HandleCallback)

	// 健康检查端点
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	// 启动服务器
	addr := fmt.Sprintf(":%d", c.HTTP.Port)
	log.Printf("HTTP服务器启动在端口 %d", c.HTTP.Port)

	server := &http.Server{
		Addr:         addr,
		Handler:      mux,
		ReadTimeout:  10 * time.Second,
		WriteTimeout: 10 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Printf("HTTP服务器启动失败: %v", err)
	}
}
