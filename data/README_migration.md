# i18n 配置迁移脚本使用说明

## 概述

这个脚本用于将旧的 i18n 配置格式迁移到新的格式。脚本会自动备份现有文件，合并配置，并提供恢复功能。

## 功能特性

- ✅ 自动备份现有配置文件
- ✅ 智能合并配置（保留现有配置，添加新配置）
- ✅ 详细的操作日志和进度显示
- ✅ JSON 格式验证
- ✅ 配置差异预览
- ✅ 交互式确认
- ✅ 备份恢复功能

## 使用方法

### 1. 执行迁移

将脚本放到生产环境的 `data` 目录下，然后运行：

```bash
cd /path/to/your/data/directory
python3 migrate_i18n.py
```

### 2. 恢复备份（如果需要）

如果迁移后发现问题，可以从备份恢复：

```bash
python3 migrate_i18n.py --restore
```

## 脚本执行流程

1. **环境检查**: 验证目录结构和文件存在性
2. **文件验证**: 检查现有 JSON 文件格式是否正确
3. **配置预览**: 显示将要进行的配置变更
4. **用户确认**: 询问是否继续执行迁移
5. **文件备份**: 自动备份所有现有的 locale 文件
6. **配置合并**: 智能合并旧配置到新格式
7. **文件更新**: 更新所有 locale 文件
8. **结果验证**: 验证更新后的文件格式
9. **总结报告**: 显示迁移结果和统计信息

## 安全特性

- **自动备份**: 每次运行都会创建带时间戳的备份目录
- **格式验证**: 运行前后都会验证 JSON 格式
- **交互确认**: 执行前需要用户确认
- **错误处理**: 详细的错误信息和失败处理
- **恢复功能**: 可以随时从备份恢复

## 目录结构

```
data/
├── migrate_i18n.py              # 迁移脚本
├── README_migration.md          # 使用说明
├── i18n_configs/
│   └── locales/
│       ├── zh_CN.json          # 中文配置
│       ├── en.json             # 英文配置
│       ├── ja.json             # 日文配置
│       └── ...                 # 其他语言配置
└── backup_YYYYMMDD_HHMMSS/     # 自动创建的备份目录
    ├── zh_CN.json
    ├── en.json
    └── ...
```

## 配置合并策略

- **保留现有配置**: 如果新旧配置中都存在相同的键，保留现有值
- **添加新配置**: 将旧配置中新的键值对添加到现有配置
- **深度合并**: 支持嵌套对象的递归合并
- **差异记录**: 详细记录所有配置变更

## 注意事项

1. **运行环境**: 确保在正确的 `data` 目录下运行脚本
2. **权限要求**: 确保脚本有读写 locale 文件的权限
3. **备份重要**: 虽然脚本会自动备份，但建议手动备份重要数据
4. **测试验证**: 建议先在测试环境验证迁移结果
5. **Python版本**: 需要 Python 3.6 或更高版本

## 故障排除

### 常见错误

1. **目录不存在**
   ```
   ❌ Locales目录不存在: /path/to/locales
   ```
   解决方案: 确保在正确的 data 目录下运行脚本

2. **JSON格式错误**
   ```
   ❌ JSON格式错误: Expecting ',' delimiter
   ```
   解决方案: 修复损坏的 JSON 文件格式

3. **权限不足**
   ```
   ❌ 文件读取错误: Permission denied
   ```
   解决方案: 检查文件权限，使用 sudo 或修改文件权限

### 恢复步骤

如果迁移出现问题：

1. 停止使用新配置
2. 运行恢复命令: `python3 migrate_i18n.py --restore`
3. 选择合适的备份进行恢复
4. 检查恢复后的文件是否正常

## 联系支持

如果遇到问题，请提供以下信息：
- 脚本运行的完整输出日志
- 错误发生时的环境信息
- 相关的配置文件内容（如果可能）
