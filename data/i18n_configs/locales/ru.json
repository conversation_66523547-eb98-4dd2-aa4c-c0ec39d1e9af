{"errors": {"invalid_command": "Неверная команда", "permission_denied": "Отказано в доступе, невозможно выполнить эту операцию", "vip_required": "Для этой функции требуется VIP-членство", "general_error": "Произошла ошибка, попробуйте позже", "command_failed": "Команда не выполнена, попробуйте позже", "missing_prompt": "Не получен промпт, попробуйте снова!\n\nФормат: {command} промпт", "missing_code": "Неверные параметры!\n\nФормат: {command} код активации\n\nПример: {command} VIP2024", "callback_not_owned": "Эта кнопка не для вас", "insufficient_balance": "❌ Недостаточно баллов!\n\nДля использования этой функции нужно {cost} баллов\nТекущий баланс: {balance} баллов\n\nПолучить больше баллов:\n• Используйте коды активации: /sk <код>\n• Приглашайте друзей за награды"}, "vip": {"status": {"active": "Акти<PERSON><PERSON>н", "inactive": "Неактивен", "expired": "Истек"}, "expires_at": "Срок действия до：{date}"}, "instructions": {"title": "🤖Руководство по функциям TGAI🤖", "content": "🌐GPT4.0 онлайн\nКоманда: net\nИспользование: net анализ акций Centrica / net погода в Лос-Анджелесе сегодня\n\n🔍GPT4mini\nКоманда: ask(или a)\nИспользование: ask какие биржи криптовалют есть в США?\nФункции: чат, вопросы-ответы, распознавание изображений, отслеживание памяти\n\n🧩GPT4o\nКоманда: 4o\nИспользование: 4o анализ тренда акций XXX\nФункции: анализ акций, данные криптовалют, умный чат, распознавание изображений\n\n🍻Claude-h\nКоманда: ck\nИспользование: ck как ответить на это сообщение?\nФункции: быстрый ответ, помощь в чате, отслеживание памяти\n\n🤡Замена лица\nКоманда: fc\nИспользование: загрузить 2 фото + fc\n\n🎨Создание изображений\nКоманда: draw\nИспользование: draw утренняя сцена на Таймс-сквер\n\n🧑‍🔬OpenAI-o1\nКоманда: o1\nИспользование: o1 проанализировать это с логической точки зрения\n\n🎐Замена фона\nКоманда: br\nИспользование: отправить фото человека + фото фона (одинакового размера)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "Присоединяйтесь к каналу и следите за обновлениями TAI, получайте последние новости об AI первыми❕❕❕ \n\nКнопка: Официальный канал TGAI - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "Настройки языка", "instructions": "Инструкции", "channel_notifications": "Уведомления канала", "get_invite_code": "Получить код приглашения", "group_management": "Управление группой", "back_to_menu": "← Вернуться в меню"}, "start": {"welcome": {"message": "Добро пожаловать в нашего бота для рисования! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "Ваша пригласительная ссылка: {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "Приглашение успешно!\nПриглашенный пользователь: {user_id}, получено 200 очков!", "fail": "Не удалось пригласить пользователя {user_id}!\nСообщение об ошибке: {error}\nВаша пригласительная ссылка: {link}", "invited_by": "Вы были приглашены пользователем ID {user_id}. Ваш пригласительный код: {link}"}, "error": {"self_invite": "Вы не можете пригласить самого себя!", "already_invited": "Этот пользователь уже был приглашен пользователем ID {invited_by}", "time_limit": "Пользователь зарегистрирован более 24 часов назад и не может быть приглашен"}}, "language": {"choose": "Пожалуйста, выберите ваш язык：\n\nДоступные языки：", "changed": "✅ Ваш язык изменен на русский", "error": "Не удалось изменить язык, попробуйте позже", "current": "Текущий язык: русский"}, "group": {"management": {"tutorial": "Для добавления группы нужно всего два шага：（или нажмите кнопку ниже）\n1. Добавьте бота в группу и назначьте администратором\n2. Убедитесь, что вы администратор группы\n3. Если после выполнения первых двух шагов группа не отображается, выполните команду /update в группе\n4. Нажмите на название группы для управления", "add_bot": "【Нажмите здесь, чтобы добавить бота в группу】", "prev_page": "◀️Предыдущая", "next_page": "Следующая▶️", "refresh": "🔄Обновить", "help": "❓Помощь", "group_info": "📑 Выбранная группа：\nНазвание группы：{title}\nID группы：{chat_id}\n\nВыберите действие：", "delete_binding": "❌ Отвязать группу", "back_to_list": "⬅️ Вернуться к списку", "unbind_success": "Группа успешно отвязана！", "unbind_failed": "Не удалось отвязать, попробуйте позже", "group_not_found": "Информация о группе не найдена"}, "help": {"title": "Центр помощи：", "content": "1. Что делать, если я случайно отвязал группу？\nОтвет：Удалите бота из группы и пригласите снова.\n\n2. Как правильно добавить бота в группу？\nОтвет：Нажмите кнопку【Нажмите здесь, чтобы добавить бота в группу】и выберите целевую группу.\n\n3. Почему я не вижу свою группу？\nОтвет：Убедитесь, что：\n   - Бот назначен администратором группы\n   - Вы являетесь администратором группы\n   - Вы нажали кнопку обновления", "back": "Назад"}, "binding": {"success": "Спасибо за добавление в группу！\nID группы：{chat_id}\nНазвание группы：{title}\nПользователь：{user_id}\nИмя пользователя：@{username}", "failed": "Привязка не удалась！При приглашении бота назначьте его администратором для подтверждения вашего статуса администратора группы. Удалите бота и пригласите снова.\nID группы：{chat_id}\nНазвание группы：{title}\nПользователь：{user_id}\nИмя пользователя：@{username}", "success_user": "Спасибо за добавление в группу！\nID группы：{chat_id}\nНазвание группы：{title}\nПользователь：{user_id}\nИмя пользователя：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "УдалитьРекламу", "generate_remove_ads_code": "СоздатьКодУдаленияРекламы"}, "invalid_params": "Неверные параметры. Проверьте формат.\nПравильный формат: {command} <код подтверждения>", "invalid_code": "Неверный или уже использованный код подтверждения! Проверьте код.\nПравильный формат: {command} <код подтверждения>", "remove_success": "Реклама успешно удалена", "remove_failed": "Не удалось удалить рекламу. Ошибка: {error}", "generate_success": "Код удаления рекламы создан, проверьте вложение", "error": "Не удалось удалить рекламу. Обратитесь к администратору. Ошибка: {error}"}, "info": {"title": "📊 Информация о пользователе", "user_id": "ID пользователя: {user_id}", "username": "Имя пользователя: @{username}", "language": "Язык: {language}", "vip_status": "VIP статус: {status}", "remaining_power": "Оставшаяся энергия: {power}", "vip_status_normal": "Обычный пользователь", "vip_status_vip": "VIP пользователь", "vip_status_expired": "Истек"}, "sk": {"verifying": "🎫 Проверка кода активации: {code}...", "success": "✅ Код активации успешно проверен!\n\nПолученная награда: {reward}", "invalid": "❌ Неверный или истекший код активации", "already_used": "❌ Этот код активации уже использован"}, "sign_in": {"success": "✅ Вход выполнен успешно!\n\n🎁 Получено ежедневных баллов: {reward}\n💰 Текущий ежедневный баланс: {daily_balance}\n📊 Общий баланс: {total_balance}", "already_signed_in": "Вы уже входили сегодня!\n\nСледующий вход доступен через {next_check_in_hours} часов", "cannot_sign_in": "❌ Невозможно войти в данный момент, попробуйте позже", "check_status": "🔍 Проверка статуса входа..."}, "admin": {"moderate_image": {"usage": "Использование: /moderate_image <URL_изображения>\nПример: /moderate_image https://example.com/image.jpg\n\nИли используйте /moderate_reply для модерации изображения, ответив на сообщение с изображением."}}, "ai_command": {"draw": {"processing": "💡Получен запрос на генерацию изображения!\n🌟Ваш промпт: {prompt}\n🌈Пожалуйста, подождите...", "success": "🎨 Изображение успешно сгенерировано!", "failed": "❌ Ошибка генерации изображения: {error}", "no_prompt": "❌ Пожалуйста, предоставьте промпт для генерации изображения!\n\nФормат: {command} промпт\nПример: {command} красивый пейзаж"}, "fc": {"processing": "🤡 Обработка замены лица, пожалуйста, подождите...", "success": "✅ Замена лица завершена!", "failed": "❌ Ошибка замены лица: {error}", "need_two_photo": "❌ Для замены лица нужны две фотографии!\n\nПожалуйста, загрузите две фотографии и используйте команду {command}"}, "br": {"processing": "🎐 Обработка замены фона, пожалуйста, подождите...", "success": "✅ Замена фона завершена!", "failed": "❌ Ошибка замены фона: {error}", "need_two_photo": "❌ Для замены фона нужны две фотографии!\n\nПожалуйста, отправьте фото человека и фото фона, затем используйте команду {command}", "same_size_needed": "⚠️ Рекомендуется использовать фотографии одинакового размера для лучших результатов"}, "be": {"processing": "🎨 Обработка удаления фона, пожалуйста, подождите...", "success": "✅ Удаление фона завершено!", "failed": "❌ Ошибка удаления фона: {error}", "need_photo": "❌ Для удаления фона нужно одно изображение!\n\nПожалуйста, загрузите изображение и используйте команду {command}"}, "moderation": {"failed": "❌ Ошибка модерации изображения: {error}", "rejected": "❌ Модерация изображения не пройдена, пожалуйста, проверьте соответствие содержимого изображения правилам", "passed": "✅ Модерация изображения пройдена, обработка ИИ...", "processing": "🔍 Модерация изображения в процессе, пожалуйста, подождите..."}}}