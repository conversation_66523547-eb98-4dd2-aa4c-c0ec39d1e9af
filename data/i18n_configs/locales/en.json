{"errors": {"invalid_command": "Invalid command", "permission_denied": "Permission denied, unable to perform this operation", "vip_required": "This feature requires VIP membership", "general_error": "An error occurred, please try again later", "command_failed": "Command execution failed, please try again", "missing_prompt": "No prompt provided, please try again!\n\nFormat: {command} prompt", "missing_code": "Incorrect parameters, please try again!\n\nFormat: {command} code\n\nExample: {command} VIP2024", "callback_not_owned": "This button is not for you", "insufficient_balance": "❌ Insufficient balance!\n\nYou need {cost} points to use this feature.\nCurrent balance: {balance} points\n\nGet more points:\n• Use redemption codes: /sk <code>\n• Invite friends for rewards"}, "vip": {"status": {"active": "Active", "inactive": "Inactive", "expired": "Expired"}, "expires_at": "Expires at: {date}"}, "instructions": {"title": "🤖TGAI Functions Guide🤖", "content": "🌐GPT4.0 Online\nCommand: net\nUse: net Analyze Centrica stock / net LA weather today\n\n🔍GPT4mini\nCommand: ask (or a)\nUse: ask What blockchain exchanges are in US?\nFeatures: Chat, Q&A, image recognition, memory tracking\n\n🧩GPT4o\nCommand: 4o\nUse: 4o Analyze XXX stock trend\nFeatures: Stock analysis, crypto data, smart chat, image recognition\n\n🍻Claude-h\nCommand: ck\nUse: ck How to reply this message?\nFeatures: Fast responses, chat help, memory tracking\n\n🤡Face Swap\nCommand: fc\nUse: Upload 2 photos + fc\n\n🎨Image Gen\nCommand: draw\nUse: draw Times Square morning scene\n\n🧑‍🔬OpenAI-o1\nCommand: o1\nUse: o1 Analyze this logically\n\n🎐Background Change\nCommand: br\nUse: Send person + background photo (same size)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "Join the channel and always follow the update progress of TAI Get the latest information on AI as soon as possible❕❕❕ \n\nButton: TGAI official channel - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "language/语言", "instructions": "instructions", "channel_notifications": "channel_notifications", "get_invite_code": "get_invite_code", "group_management": "group_management", "back_to_menu": "← Back to <PERSON>u"}, "start": {"welcome": {"message": "Welcome to our drawing bot! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "Your invitation link is: {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "Invitation successful!\nYou have invited user: {user_id}, and received 200 computing power!", "fail": "You invited user {user_id}, but the invitation failed!\nError message: {error}\nYour invitation link: {link}", "invited_by": "You have been invited by user ID {user_id}, your invitation code is: {link}"}, "error": {"self_invite": "You cannot invite yourself!", "already_invited": "This user has already been invited by user ID {invited_by}", "time_limit": "This user registered more than 24 hours ago and cannot be invited"}}, "language": {"choose": "Please select your preferred language:\n\nCurrently supported languages:", "changed": "✅ Your language has been changed to English", "error": "Failed to change language, please try again later", "current": "Current language: English"}, "group": {"management": {"tutorial": "Two steps to add a group: (or click the button below)\n1. Add this bot to your group and set it as an administrator.\n2. Make sure you are a group administrator.\n3. If you have completed the first two steps but still can't find your group, execute the /update command in the group.\n4. Click on the group name button to manage your group chat", "add_bot": "【Click here to add bot to group】", "prev_page": "◀️Previous", "next_page": "Next▶️", "refresh": "🔄Refresh", "help": "❓Help", "group_info": "📑 Current selected group:\nGroup name: {title}\nGroup ID: {chat_id}\n\nPlease select an action:", "delete_binding": "❌ Delete group binding", "back_to_list": "⬅️ Back to list", "unbind_success": "Group unbound successfully!", "unbind_failed": "Unbinding failed, please try again later", "group_not_found": "Group information not found"}, "help": {"title": "Help Center:", "content": "1. What if I accidentally unbound a group?\nA: Remove the bot from the group and invite it back.\n\n2. How to correctly add the bot to a group?\nA: Click the 【Click here to add bot to group】 button and select your target group.\n\n3. Why can't I see my group?\nA: Please ensure:\n   - The bot is set as a group administrator\n   - You are a group administrator\n   - You have clicked the refresh button", "back": "Back"}, "binding": {"success": "Thanks for adding me to the group!\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}", "failed": "Binding failed! Please set the bot as an administrator when inviting it to verify your group administrator identity. Please remove the bot and invite it to the group again.\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}", "success_user": "Thanks for adding me to the group!\nGroup ID: {chat_id}\nGroup title: {title}\nBound to user: {user_id}\nBound username: @{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "Delete chat ads", "generate_remove_ads_code": "Generate ad removal codes"}, "invalid_params": "Invalid parameters. Please check the format.\nCorrect format: {command} <verification code>", "invalid_code": "Invalid or used verification code! Please check if the code is correct.\nCorrect format: {command} <verification code>", "remove_success": "Ads removed successfully", "remove_failed": "Failed to remove ads. Error: {error}", "generate_success": "Ad removal codes generated, please check the attachment", "error": "Failed to remove ads. Please contact administrator. Error: {error}"}, "info": {"title": "📊 User Information", "user_id": "User ID: {user_id}", "username": "Username: @{username}", "language": "Language: {language}", "vip_status": "VIP Status: {status}", "remaining_power": "Remaining Power: {power}", "vip_status_normal": "Normal User", "vip_status_vip": "VIP User", "vip_status_expired": "Expired"}, "sk": {"verifying": "🎫 Verifying redemption code: {code}...", "success": "✅ Redemption code verified successfully!\n\n<PERSON><PERSON> received: {reward}", "invalid": "❌ Invalid or expired redemption code", "already_used": "❌ This redemption code has already been used"}, "sign_in": {"success": "✅ Check-in successful!\n\n🎁 Daily points earned: {reward}\n💰 Current daily balance: {daily_balance}\n📊 Total balance: {total_balance}", "already_signed_in": "You have already checked in today!\n\nNext check-in available in {next_check_in_hours} hours", "cannot_sign_in": "❌ Unable to check in at the moment, please try again later", "check_status": "🔍 Checking sign-in status..."}, "admin": {"moderate_image": {"usage": "Usage: /moderate_image <image_url>\nExample: /moderate_image https://example.com/image.jpg\n\nOr use /moderate_reply to moderate an image by replying to a message containing an image."}}, "ai_command": {"draw": {"processing": "💡Received your image generation request!\n🌟Your prompt: {prompt}\n🌈Please wait...", "success": "🎨 Image generated successfully!", "failed": "❌ Image generation failed: {error}", "no_prompt": "❌ Please provide a prompt for image generation!\n\nFormat: {command} prompt\nExample: {command} beautiful landscape painting"}, "fc": {"processing": "🤡 Face swap processing, please wait...", "success": "✅ Face swap completed!", "failed": "❌ Face swap failed: {error}", "need_two_photo": "❌ Face swap requires two photos!\n\nPlease upload two photos and use {command} command"}, "br": {"processing": "🎐 Background replacement processing, please wait...", "success": "✅ Background replacement completed!", "failed": "❌ Background replacement failed: {error}", "need_two_photo": "❌ Background replacement requires two photos!\n\nPlease send person photo and background photo, then use {command} command", "same_size_needed": "⚠️ Recommend using photos of the same size for best results"}, "be": {"processing": "🎨 Background removal processing, please wait...", "success": "✅ Background removal completed!", "failed": "❌ Background removal failed: {error}", "need_photo": "❌ Background removal requires one image!\n\nPlease upload an image and use {command} command"}, "moderation": {"failed": "❌ Image moderation failed: {error}", "rejected": "❌ Image moderation failed, please check if the image content complies with guidelines", "passed": "✅ Image moderation passed, AI processing...", "processing": "🔍 Image moderation in progress, please wait..."}}, "payment": {"errors": {"amount_too_small": "❌ Payment amount cannot be less than {min_amount} CNY", "amount_too_large": "❌ Payment amount cannot exceed {max_amount} CNY", "create_order_failed": "❌ Failed to create order: {error}", "invalid_amount": "❌ Invalid amount format, please enter a valid number", "service_unavailable": "❌ Payment service is currently unavailable"}, "order_created": "💰 Payment order created successfully!\n\n🆔 Order ID: `{order_id}`\n💵 Payment amount: {amount} CNY\n💎 Actual payment: {actual_amount} USDT\n📍 Wallet address: `{wallet_address}`\n⏰ Please complete payment before {expiration_time}\n\n⚠️ Please copy the wallet address and transfer using USDT(TRC20)\n💡 You will be notified automatically upon successful payment", "payment_success": "🎉 Payment successful!\n\n🆔 Order ID: {order_id}\n💵 Payment amount: {amount} CNY\n✅ Payment status: Completed\n⏰ Completion time: {completion_time}\n\nThank you for your payment!", "copy_wallet": "📋 Copy wallet address", "wallet_copied": "✅ Wallet address sent", "wallet_address": "📋 Wallet address:\n\n`{address}`\n\n💡 Click address to copy", "invalid_wallet": "❌ Invalid wallet address", "usage": "💰 Usage: /pay <amount>\n\nExample: /pay 10"}}