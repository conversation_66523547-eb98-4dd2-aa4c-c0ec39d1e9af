{"errors": {"invalid_command": "無効なコマンドです", "permission_denied": "権限がありません。この操作を実行できません", "vip_required": "この機能にはVIPメンバーシップが必要です", "general_error": "エラーが発生しました。後でもう一度お試しください", "command_failed": "コマンドの実行に失敗しました。もう一度お試しください", "missing_prompt": "プロンプトが取得できませんでした。もう一度お試しください！\n\n形式：{command} プロンプト", "missing_code": "パラメータが正しくありません！\n\n形式：{command} 引き換えコード\n\n例：{command} VIP2024", "callback_not_owned": "このボタンはあなた用ではありません", "insufficient_balance": "❌ ポイント不足！\n\nこの機能を使用するには {cost} ポイントが必要です\n現在の残高：{balance} ポイント\n\nより多くのポイントを取得：\n• 引き換えコードを使用：/sk <コード>\n• 友達を招待して報酬を獲得"}, "vip": {"status": {"active": "有効", "inactive": "無効", "expired": "期限切れ"}, "expires_at": "有効期限：{date}"}, "instructions": {"title": "🤖TGAI機能ガイド🤖", "content": "🌐GPT4.0オンライン\nコマンド：net\n使用例：net Centrica株式分析 / net ロサンゼルスの今日の天気\n\n🔍GPT4mini\nコマンド：ask（またはa）\n使用例：ask 米国のブロックチェーン取引所は？\n機能：チャット、Q&A、画像認識、メモリ追跡\n\n🧩GPT4o\nコマンド：4o\n使用例：4o XXX株価トレンド分析\n機能：株式分析、暗号資産データ、スマートチャット、画像認識\n\n🍻Claude-h\nコマンド：ck\n使用例：ck このメッセージにどう返信する？\n機能：高速レスポンス、チャットヘルプ、メモリ追跡\n\n🤡フェイススワップ\nコマンド：fc\n使用例：2枚の写真をアップロード + fc\n\n🎨画像生成\nコマンド：draw\n使用例：draw タイムズスクエアの朝の風景\n\n🧑‍🔬OpenAI-o1\nコマンド：o1\n使用例：o1 これを論理的に分析\n\n🎐背景変更\nコマンド：br\n使用例：人物 + 背景写真（同サイズ）を送信", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "チャンネルに参加してTAIの更新状況をフォローし、AIに関する最新情報をいち早く入手しましょう❕❕❕ \n\nボタン：TGAI公式チャンネル - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "言語設定", "instructions": "使用説明", "channel_notifications": "チャンネル通知", "get_invite_code": "招待コード取得", "group_management": "グループ管理", "back_to_menu": "← メニューに戻る"}, "start": {"welcome": {"message": "お絵描きボットへようこそ！🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "あなたの招待リンク：{link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "招待成功！\nユーザー：{user_id}を招待し、200ポイントを獲得しました！", "fail": "ユーザー{user_id}の招待に失敗しました！\nエラーメッセージ：{error}\nあなたの招待リンク：{link}", "invited_by": "あなたはユーザーID {user_id} によって招待されました, 君の招待コードは：{link}"}, "error": {"self_invite": "自分自身を招待することはできません！", "already_invited": "このユーザーはすでにユーザーID {invited_by} によって招待されています", "time_limit": "このユーザーは登録から24時間以上経過しているため、招待できません"}}, "language": {"choose": "言語を選択してください：\n\n現在サポートされている言語：", "changed": "✅ 言語が日本語に変更されました", "error": "言語の変更に失敗しました。後でもう一度お試しください", "current": "現在の言語：日本語"}, "group": {"management": {"tutorial": "グループの追加は2ステップで完了：（または下のボタンをクリック）\n1. ボットをグループに追加し、管理者に設定\n2. あなたがグループ管理者であることを確認\n3. 最初の2つのステップを完了してもグループが見つからない場合は、グループで /update コマンドを実行してください\n4. グループ名のボタンをクリックしてグループを管理", "add_bot": "【ここをクリックしてボットをグループに追加】", "prev_page": "◀️前へ", "next_page": "次へ▶️", "refresh": "🔄更新", "help": "❓ヘルプ", "group_info": "📑 現在選択中のグループ：\nグループ名：{title}\nグループID：{chat_id}\n\n操作を選択してください：", "delete_binding": "❌ グループのバインドを解除", "back_to_list": "⬅️ リストに戻る", "unbind_success": "グループのバインドを解除しました！", "unbind_failed": "バインド解除に失敗しました。後でもう一度お試しください", "group_not_found": "グループ情報が見つかりません"}, "help": {"title": "ヘルプセンター：", "content": "1. 誤ってグループのバインドを解除してしまった場合は？\n回答：ボットをグループから削除し、再度招待してください。\n\n2. ボットをグループに正しく追加するには？\n回答：【ここをクリックしてボットをグループに追加】ボタンをクリックし、対象のグループを選択してください。\n\n3. グループが表示されない理由は？\n回答：以下を確認してください：\n   - ボットがグループ管理者に設定されている\n   - あなたがグループ管理者である\n   - 更新ボタンをクリックした", "back": "戻る"}, "binding": {"success": "グループに追加していただき、ありがとうございます！\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}", "failed": "バインドに失敗しました！ボットを招待する際は、グループ管理者の身分を確認するため、管理者として設定してください。ボットを削除して再度グループに招待してください。\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}", "success_user": "グループに追加していただき、ありがとうございます！\nグループID：{chat_id}\nグループ名：{title}\nバインドユーザー：{user_id}\nバインドユーザー名：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "広告を削除", "generate_remove_ads_code": "広告削除コードを生成"}, "invalid_params": "パラメータが無効です。形式を確認してください。\n正しい形式：{command} <認証コード>", "invalid_code": "認証コードが無効か、すでに使用されています！認証コードが正しいか確認してください。\n正しい形式：{command} <認証コード>", "remove_success": "広告を削除しました", "remove_failed": "広告の削除に失敗しました。エラー：{error}", "generate_success": "広告削除コードを生成しました。添付ファイルを確認してください", "error": "広告の削除に失敗しました。管理者に連絡してください。エラー：{error}"}, "info": {"title": "📊 ユーザー情報", "user_id": "ユーザーID: {user_id}", "username": "ユーザー名: @{username}", "language": "言語: {language}", "vip_status": "VIPステータス: {status}", "remaining_power": "残りエネルギー: {power}", "vip_status_normal": "一般ユーザー", "vip_status_vip": "VIPユーザー", "vip_status_expired": "期限切れ"}, "sk": {"verifying": "🎫 引き換えコードを確認中: {code}...", "success": "✅ 引き換えコードの確認が完了しました！\n\n獲得報酬: {reward}", "invalid": "❌ 無効または期限切れの引き換えコード", "already_used": "❌ この引き換えコードは既に使用されています"}, "sign_in": {"success": "✅ チェックイン成功！\n\n🎁 獲得デイリーポイント: {reward}\n💰 現在のデイリー残高: {daily_balance}\n📊 総残高: {total_balance}", "already_signed_in": "今日は既にチェックイン済みです！\n\n次回チェックイン可能時間: {next_check_in_hours}時間後", "cannot_sign_in": "❌ 現在チェックインできません。後でもう一度お試しください", "check_status": "🔍 チェックイン状況を確認中..."}, "admin": {"moderate_image": {"usage": "使用法: /moderate_image <画像URL>\n例: /moderate_image https://example.com/image.jpg\n\nまたは /moderate_reply を使用して画像を含むメッセージに返信してモデレートしてください。"}}, "ai_command": {"draw": {"processing": "💡画像生成リクエストを受信しました！\n🌟あなたのプロンプト: {prompt}\n🌈お待ちください...", "success": "🎨 画像生成成功！", "failed": "❌ 画像生成失敗: {error}", "no_prompt": "❌ 画像生成のプロンプトを提供してください！\n\n形式: {command} プロンプト\n例: {command} 美しい風景画"}, "fc": {"processing": "🤡 フェイススワップ処理中、お待ちください...", "success": "✅ フェイススワップ完了！", "failed": "❌ フェイススワップ失敗: {error}", "need_two_photo": "❌ フェイススワップには2枚の写真が必要です！\n\n2枚の写真をアップロードして {command} コマンドを使用してください"}, "br": {"processing": "🎐 背景置換処理中、お待ちください...", "success": "✅ 背景置換完了！", "failed": "❌ 背景置換失敗: {error}", "need_two_photo": "❌ 背景置換には2枚の写真が必要です！\n\n人物写真と背景写真を送信してから {command} コマンドを使用してください", "same_size_needed": "⚠️ 最良の結果を得るために同じサイズの写真を使用することをお勧めします"}, "be": {"processing": "🎨 背景除去処理中、お待ちください...", "success": "✅ 背景除去完了！", "failed": "❌ 背景除去失敗: {error}", "need_photo": "❌ 背景除去には1枚の画像が必要です！\n\n画像をアップロードして {command} コマンドを使用してください"}, "moderation": {"failed": "❌ 画像審査失敗: {error}", "rejected": "❌ 画像審査に通りませんでした。画像内容がガイドラインに適合しているかご確認ください", "passed": "✅ 画像審査通過、AI処理中...", "processing": "🔍 画像審査中、お待ちください..."}}}