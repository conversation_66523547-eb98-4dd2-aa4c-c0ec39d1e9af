{"errors": {"invalid_command": "잘못된 명령어입니다", "permission_denied": "권한이 없어 이 작업을 수행할 수 없습니다", "vip_required": "이 기능은 VIP 멤버십이 필요합니다", "general_error": "오류가 발생했습니다. 나중에 다시 시도해주세요", "command_failed": "명령 실행에 실패했습니다. 다시 시도해주세요", "missing_prompt": "프롬프트를 받지 못했습니다. 다시 시도해주세요!\n\n형식: {command} 프롬프트", "missing_code": "매개변수가 올바르지 않습니다!\n\n형식: {command} 교환코드\n\n예시: {command} VIP2024", "callback_not_owned": "이 버튼은 당신을 위한 것이 아닙니다", "insufficient_balance": "❌ 포인트가 부족합니다!\n\n이 기능을 사용하려면 {cost} 포인트가 필요합니다\n현재 잔액: {balance} 포인트\n\n더 많은 포인트 얻기:\n• 교환코드 사용: /sk <코드>\n• 친구 초대로 보상 받기"}, "vip": {"status": {"active": "활성화됨", "inactive": "비활성화됨", "expired": "만료됨"}, "expires_at": "만료일：{date}"}, "instructions": {"title": "🤖TGAI 기능 가이드🤖", "content": "🌐GPT4.0 온라인\n명령어: net\n사용법: net Centrica 주식 분석 / net 로스앤젤레스 오늘 날씨\n\n🔍GPT4mini\n명령어: ask(또는 a)\n사용법: ask 미국의 블록체인 거래소는 어떤 것들이 있나요?\n기능: 채팅, 질문답변, 이미지 인식, 기억 추적\n\n🧩GPT4o\n명령어: 4o\n사용법: 4o XXX 주식 동향 분석\n기능: 주식 분석, 암호화폐 데이터, 스마트 채팅, 이미지 인식\n\n🍻Claude-h\n명령어: ck\n사용법: ck 이 메시지에 어떻게 답변할까요?\n기능: 빠른 응답, 채팅 도움, 기억 추적\n\n🤡얼굴 바꾸기\n명령어: fc\n사용법: 사진 2장 업로드 + fc\n\n🎨이미지 생성\n명령어: draw\n사용법: draw 타임스퀘어 아침 풍경\n\n🧑‍🔬OpenAI-o1\n명령어: o1\n사용법: o1 논리적 관점에서 이것을 분석\n\n🎐배경 변경\n명령어: br\n사용법: 인물 + 배경 사진 전송(동일한 크기)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "채널에 가입하여 TAI의 업데이트 진행 상황을 확인하고 AI의 최신 정보를 가장 먼저 받아보세요❕❕❕ \n\n버튼: TGAI 공식 채널 - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "언어 설정", "instructions": "사용 설명서", "channel_notifications": "채널 알림", "get_invite_code": "초대 코드 받기", "group_management": "그룹 관리", "back_to_menu": "← 메뉴로 돌아가기"}, "start": {"welcome": {"message": "그림 그리기 봇에 오신 것을 환영합니다! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "귀하의 초대 링크: {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "초대 성공!\n초대한 사용자: {user_id}, 200 포인트를 획득했습니다!", "fail": "초대한 사용자 {user_id} 초대 실패!\n오류 메시지: {error}\n귀하의 초대 링크: {link}", "invited_by": "사용자 ID {user_id}님의 초대를 받았습니다. 귀하의 초대 코드: {link}"}, "error": {"self_invite": "자기 자신을 초대할 수 없습니다!", "already_invited": "해당 사용자는 이미 사용자 ID {invited_by}님의 초대를 받았습니다", "time_limit": "해당 사용자의 가입 시간이 24시간을 초과하여 초대할 수 없습니다"}}, "language": {"choose": "언어를 선택해주세요：\n\n현재 지원되는 언어：", "changed": "✅ 언어가 한국어로 변경되었습니다", "error": "언어 변경에 실패했습니다. 나중에 다시 시도해주세요", "current": "현재 언어: 한국어"}, "group": {"management": {"tutorial": "그룹 추가는 두 단계만 필요합니다：（또는 아래 버튼 클릭）\n1. 봇을 그룹에 추가하고 관리자로 설정\n2. 귀하가 그룹 관리자인지 확인\n3. 첫 두 단계를 완료했는데도 그룹이 보이지 않는다면, 그룹에서 /update 명령어를 실행해주세요\n4. 그룹 이름 버튼을 클릭하여 그룹을 관리하세요", "add_bot": "【여기를 클릭하여 봇을 그룹에 추가】", "prev_page": "◀️이전 페이지", "next_page": "다음 페이지▶️", "refresh": "🔄새로고침", "help": "❓도움말", "group_info": "📑 현재 선택된 그룹：\n그룹 이름：{title}\n그룹 ID：{chat_id}\n\n작업을 선택하세요：", "delete_binding": "❌ 그룹 연동 해제", "back_to_list": "⬅️ 목록으로 돌아가기", "unbind_success": "그룹 연동이 해제되었습니다！", "unbind_failed": "연동 해제 실패, 나중에 다시 시도해주세요", "group_not_found": "그룹 정보를 찾을 수 없습니다"}, "help": {"title": "도움말 센터：", "content": "1. 실수로 그룹 연동을 해제했다면 어떻게 하나요？\n답：봇을 그룹에서 제거한 후 다시 초대하면 됩니다.\n\n2. 봇을 그룹에 올바르게 추가하는 방법은？\n답：【여기를 클릭하여 봇을 그룹에 추가】 버튼을 클릭하고 대상 그룹을 선택하세요.\n\n3. 내 그룹이 보이지 않는 이유는？\n답：다음 사항을 확인해주세요：\n   - 봇이 그룹 관리자로 설정되어 있는지\n   - 귀하가 그룹 관리자인지\n   - 새로고침 버튼을 클릭했는지", "back": "돌아가기"}, "binding": {"success": "그룹에 추가해주셔서 감사합니다！\n그룹 ID：{chat_id}\n그룹 이름：{title}\n연동 사용자：{user_id}\n연동 사용자명：@{username}", "failed": "연동 실패！봇을 초대할 때 관리자로 설정하여 귀하의 그룹 관리자 신분을 확인해주세요. 봇을 제거한 후 그룹에 다시 초대해주세요.\n그룹 ID：{chat_id}\n그룹 이름：{title}\n연동 사용자：{user_id}\n연동 사용자명：@{username}", "success_user": "그룹에 추가해주셔서 감사합니다！\n그룹 ID：{chat_id}\n그룹 이름：{title}\n연동 사용자：{user_id}\n연동 사용자명：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "광고제거", "generate_remove_ads_code": "광고제거코드생성"}, "invalid_params": "매개변수가 잘못되었습니다. 형식을 확인해주세요.\n올바른 형식: {command} <인증코드>", "invalid_code": "인증코드가 잘못되었거나 이미 사용되었습니다! 인증코드를 확인해주세요.\n올바른 형식: {command} <인증코드>", "remove_success": "광고가 성공적으로 제거되었습니다", "remove_failed": "광고 제거 실패. 오류: {error}", "generate_success": "광고 제거 코드가 생성되었습니다. 첨부파일을 확인해주세요", "error": "광고 제거에 실패했습니다. 관리자에게 문의해주세요. 오류: {error}"}, "info": {"title": "📊 사용자 정보", "user_id": "사용자 ID: {user_id}", "username": "사용자명: @{username}", "language": "언어: {language}", "vip_status": "VIP 상태: {status}", "remaining_power": "남은 에너지: {power}", "vip_status_normal": "일반 사용자", "vip_status_vip": "VIP 사용자", "vip_status_expired": "만료됨"}, "sk": {"verifying": "🎫 교환코드 확인 중: {code}...", "success": "✅ 교환코드 확인 완료!\n\n받은 보상: {reward}", "invalid": "❌ 잘못되었거나 만료된 교환코드", "already_used": "❌ 이 교환코드는 이미 사용되었습니다"}, "sign_in": {"success": "✅ 체크인 성공!\n\n🎁 획득한 일일 포인트: {reward}\n💰 현재 일일 잔액: {daily_balance}\n📊 총 잔액: {total_balance}", "already_signed_in": "오늘 이미 체크인했습니다!\n\n다음 체크인 가능 시간: {next_check_in_hours}시간 후", "cannot_sign_in": "❌ 현재 체크인할 수 없습니다. 나중에 다시 시도해주세요", "check_status": "🔍 체크인 상태 확인 중..."}, "admin": {"moderate_image": {"usage": "사용법: /moderate_image <이미지_URL>\n예시: /moderate_image https://example.com/image.jpg\n\n또는 /moderate_reply를 사용하여 이미지가 포함된 메시지에 답장하여 검토하세요."}}, "ai_command": {"draw": {"processing": "💡이미지 생성 요청을 받았습니다!\n🌟당신의 프롬프트: {prompt}\n🌈잠시만 기다려주세요...", "success": "🎨 이미지 생성 성공!", "failed": "❌ 이미지 생성 실패: {error}", "no_prompt": "❌ 이미지 생성을 위한 프롬프트를 제공해주세요!\n\n형식: {command} 프롬프트\n예시: {command} 아름다운 풍경화"}, "fc": {"processing": "🤡 얼굴 바꾸기 처리 중, 잠시만 기다려주세요...", "success": "✅ 얼굴 바꾸기 완료!", "failed": "❌ 얼굴 바꾸기 실패: {error}", "need_two_photo": "❌ 얼굴 바꾸기에는 두 장의 사진이 필요합니다!\n\n두 장의 사진을 업로드하고 {command} 명령어를 사용해주세요"}, "br": {"processing": "🎐 배경 교체 처리 중, 잠시만 기다려주세요...", "success": "✅ 배경 교체 완료!", "failed": "❌ 배경 교체 실패: {error}", "need_two_photo": "❌ 배경 교체에는 두 장의 사진이 필요합니다!\n\n인물 사진과 배경 사진을 보낸 후 {command} 명령어를 사용해주세요", "same_size_needed": "⚠️ 최상의 결과를 위해 같은 크기의 사진을 사용하는 것을 권장합니다"}, "be": {"processing": "🎨 배경 제거 처리 중, 잠시만 기다려주세요...", "success": "✅ 배경 제거 완료!", "failed": "❌ 배경 제거 실패: {error}", "need_photo": "❌ 배경 제거에는 한 장의 이미지가 필요합니다!\n\n이미지를 업로드하고 {command} 명령어를 사용해주세요"}, "moderation": {"failed": "❌ 이미지 검토 실패: {error}", "rejected": "❌ 이미지 검토를 통과하지 못했습니다. 이미지 내용이 가이드라인에 부합하는지 확인해주세요", "passed": "✅ 이미지 검토 통과, AI 처리 중...", "processing": "🔍 이미지 검토 중, 잠시만 기다려주세요..."}}}