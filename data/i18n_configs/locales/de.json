{"errors": {"invalid_command": "Ung<PERSON><PERSON><PERSON>hl", "permission_denied": "<PERSON><PERSON><PERSON> ver<PERSON><PERSON>, dieser Vorgang kann nicht ausgeführt werden", "vip_required": "<PERSON><PERSON><PERSON> diese Funktion ist eine VIP-Mitgliedschaft erforderlich", "general_error": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später erneut", "command_failed": "Befehlsausführung fehlgeschlagen, bitte versuchen Sie es erneut", "missing_prompt": "<PERSON>ine Eingabeaufforderung erhalten, bitte versuchen Si<PERSON> es erneut!\n\nFormat: {command} Eingabeaufforderung", "missing_code": "Falsche Parameter!\n\nFormat: {command} Einlösecode\n\nBeispiel: {command} VIP2024", "callback_not_owned": "<PERSON><PERSON>fläche ist nicht für Sie bestimmt", "insufficient_balance": "❌ Unzureichendes Guthaben!\n\nSie benötigen {cost} Punkte für diese Funktion\nAktuelles Guthaben: {balance} Punkte\n\nMehr Punkte erhalten:\n• Einlösecodes verwenden: /sk <Code>\n• Freunde einladen für Belohnungen"}, "vip": {"status": {"active": "Aktiv", "inactive": "Inaktiv", "expired": "Abgelaufen"}, "expires_at": "Ablaufdatum：{date}"}, "instructions": {"title": "🤖TGAI-Funktionsanleitung🤖", "content": "🌐GPT4.0 online\nBefehl: net\nVerwendung: net Centrica Aktienanalyse / net Wetter in Los Angeles heute\n\n🔍GPT4mini\nBefehl: ask(oder a)\nVerwendung: ask welche Kryptobörsen gibt es in den USA?\nFunktionen: Chat, Fragen und Antworten, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Gedächtnisverfolgung\n\n🧩GPT4o\nBefehl: 4o\nVerwendung: 4o XXX Aktientrend-Analyse\nFunktionen: Aktienanalyse, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>rk<PERSON>nung\n\n🍻Claude-h\nBefehl: ck\nVerwendung: ck wie antworte ich auf diese Nachricht?\nFunktionen: Sc<PERSON><PERSON> Antwort, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Gedächtnisverfolgung\n\n🤡Gesichtstausch\nBefehl: fc\nVerwendung: 2 Fotos hochladen + fc\n\n🎨Bildgenerierung\nBefehl: draw\nVerwendung: draw Morgenszene am Times Square\n\n🧑‍🔬OpenAI-o1\nBefehl: o1\nVerwendung: o1 dies aus logischer Sicht analysieren\n\n🎐Hintergrundwechsel\nBefehl: br\nVerwendung: Personenfoto + Hintergrundfoto senden (gleiche Größe)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "Treten Sie dem Kanal bei und bleiben Sie über TAI-Updates auf dem <PERSON>den, erhalten Sie als Erster die neuesten KI-Nachrichten❕❕❕ \n\nButton: Offizieller TGAI-Kanal - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "Spracheinstellungen", "instructions": "Anleitung", "channel_notifications": "Kanalbenachrichtigungen", "get_invite_code": "Einladungscode erhalten", "group_management": "Gruppenverwaltung", "back_to_menu": "← <PERSON><PERSON><PERSON> zum Menü"}, "start": {"welcome": {"message": "Willkommen bei unserem Zeichen-Bot! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "Ihr Einladungslink: {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "Einladung erfolgreich!\nEingeladener Benutzer: {user_id}, Sie haben 200 Punkte gewonnen!", "fail": "Einladung des Benutzers {user_id} fehlgeschlagen!\nFehlermeldung: {error}\nIhr Einladungslink: {link}", "invited_by": "<PERSON><PERSON> w<PERSON><PERSON>-ID {user_id} eingeladen. Ihr Einladungscode: {link}"}, "error": {"self_invite": "Sie können sich nicht selbst einladen!", "already_invited": "<PERSON><PERSON> wurde bereits von <PERSON>-ID {invited_by} eingeladen", "time_limit": "Der Benutzer hat sich vor über 24 Stunden registriert und kann nicht eingeladen werden"}}, "language": {"choose": "Bitte wählen Sie Ihre Sprache：\n\nVerfügbare Sprachen：", "changed": "✅ Ihre Sprache wurde auf Deutsch geändert", "error": "Sprache konnte nicht geändert werden, bitte versuchen Sie es später erneut", "current": "Aktuelle Sprache: Deutsch"}, "group": {"management": {"tutorial": "Nur zwei Schritte zum Hinzufügen einer Gruppe：（oder klicken Sie auf die Schaltfläche unten）\n1. Fügen Sie den Bot zur Gruppe hinzu und setzen Sie ihn als Administrator\n2. <PERSON><PERSON><PERSON>, dass Sie Gruppenadministrator sind\n3. Wenn Sie die Gruppe nach Abschluss der ersten beiden Schritte nicht sehen, führen Sie den Befehl /update in der Gruppe aus\n4. Klicken Sie auf den Gruppennamen, um sie zu verwalten", "add_bot": "【Klicken <PERSON> hier, um den Bot zur Gruppe hinzuzufügen】", "prev_page": "◀️Vorherige", "next_page": "Nächste▶️", "refresh": "🔄Aktualisieren", "help": "❓Hilfe", "group_info": "📑 Ausgewählte Gruppe：\nGruppenname：{title}\nGruppen-ID：{chat_id}\n\nWählen Sie eine Aktion：", "delete_binding": "❌ Gruppe trennen", "back_to_list": "⬅️ Zurück zur Liste", "unbind_success": "Gruppe erfolgreich getrennt！", "unbind_failed": "Trennung fehlgeschlagen, bitte versuchen Sie es später erneut", "group_not_found": "Gruppeninformationen nicht gefunden"}, "help": {"title": "Hilfezentrum：", "content": "1. Was tun, wenn ich versehentlich die Gruppe getrennt habe？\nAntwort：Entfernen Sie den Bot aus der Gruppe und laden Sie ihn erneut ein.\n\n2. Wie füge ich den Bot korrekt zur Gruppe hinzu？\nAntwort：Klicken Sie auf die Schaltfläche【Klicken Sie hier, um den Bot zur Gruppe hinzuzufügen】und wählen Sie die Zielgruppe aus.\n\n3. Warum sehe ich meine Gruppe nicht？\nAntwort：Stelle<PERSON> sic<PERSON>, dass：\n   - Der Bot als Gruppenadministrator eingestellt ist\n   - Sie Gruppenadministrator sind\n   - Sie auf die Aktualisieren-Schaltfläche geklickt haben", "back": "Zurück"}, "binding": {"success": "<PERSON><PERSON>, dass Sie mich zur Gruppe hinzugefügt haben！\nGruppen-ID：{chat_id}\nGruppenname：{title}\nBen<PERSON>er：{user_id}\nBenutzername：@{username}", "failed": "Bindung fehlgeschlagen！<PERSON>zen Si<PERSON> den Bot beim Einladen als Administrator ein, um Ihren Gruppenadministratorstatus zu überprüfen. Entfernen Sie den Bot und laden Sie ihn erneut zur Gruppe ein.\nGruppen-ID：{chat_id}\nGruppenname：{title}\nBenutzer：{user_id}\nBenutzername：@{username}", "success_user": "<PERSON><PERSON>, dass Sie mich zur Gruppe hinzugefügt haben！\nGruppen-ID：{chat_id}\nGruppenname：{title}\nBen<PERSON>er：{user_id}\nBenutzername：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "WerbungEntfernen", "generate_remove_ads_code": "WerbungEntfernungsCodeGenerieren"}, "invalid_params": "Ungültige Parameter. Bitte überprüfen Sie das Format.\nKorrektes Format: {command} <Verifizierungscode>", "invalid_code": "Ungültiger oder bereits verwendeter Verifizierungscode! Bitte überprüfen Sie den Code.\nKorrektes Format: {command} <Verifizierungscode>", "remove_success": "Werbung erfolgreich entfernt", "remove_failed": "Werbung konnte nicht entfernt werden. <PERSON>hler: {error}", "generate_success": "Werbungsentfernungscode generiert, überprüfen Sie den Anhang", "error": "Werbung konnte nicht entfernt werden. Bitte kontaktieren Sie den Administrator. <PERSON><PERSON>: {error}"}, "info": {"title": "📊 Benutzerinformationen", "user_id": "Benutzer-ID: {user_id}", "username": "<PERSON>utzername: @{username}", "language": "Sprache: {language}", "vip_status": "VIP-Status: {status}", "remaining_power": "Verbleibende Energie: {power}", "vip_status_normal": "<PERSON><PERSON>", "vip_status_vip": "VIP-<PERSON><PERSON><PERSON>", "vip_status_expired": "Abgelaufen"}, "sk": {"verifying": "🎫 Einlösecode wird überprüft: {code}...", "success": "✅ Einlösecode erfolgreich eingelöst!\n\nBelohnung erhalten: {reward}", "invalid": "❌ Ungültiger oder abgelaufener Einlösecode", "already_used": "❌ Dieser Einlösecode wurde bereits verwendet"}, "sign_in": {"success": "✅ Anmeldung erfolgreich!\n\n🎁 Tägliche Punkte erhalten: {reward}\n💰 Aktuelles tägliches Guthaben: {daily_balance}\n📊 Gesamtguthaben: {total_balance}", "already_signed_in": "Sie haben sich heute bereits angemeldet!\n\nNächste Anmeldung verfügbar in {next_check_in_hours} Stunden", "cannot_sign_in": "❌ Anmeldung derzeit nicht möglich, bitte versuchen Sie es später erneut", "check_status": "🔍 Anmeldestatus wird überprüft..."}, "admin": {"moderate_image": {"usage": "Verwendung: /moderate_image <Bild-URL>\nBeispiel: /moderate_image https://example.com/image.jpg\n\nOder verwenden Sie /moderate_reply, um ein Bild durch Antworten auf eine Nachricht mit einem Bild zu moderieren."}}, "ai_command": {"draw": {"processing": "💡Ihre Bildgenerierungsanfrage erhalten!\n🌟Ihr Prompt: {prompt}\n🌈Bitte warten...", "success": "🎨 Bild erfolgreich generiert!", "failed": "❌ Bildgenerierung fehlgeschlagen: {error}", "no_prompt": "❌ Bitte geben Sie einen Prompt für die Bildgenerierung an!\n\nFormat: {command} Prompt\nBeispiel: {command} schöne Landschaftsmalerei"}, "fc": {"processing": "🤡 Gesichtsta<PERSON>ch wird verarbeitet, bitte warten...", "success": "✅ Gesichtstausch abgeschlossen!", "failed": "❌ Gesichtstausch fehlgeschlagen: {error}", "need_two_photo": "❌ Gesichtstausch benötigt zwei Fotos!\n\nBitte laden Sie zwei Fotos hoch und verwenden Sie den {command} Be<PERSON>hl"}, "br": {"processing": "🎐 Hintergrundersetzung wird verarbeitet, bitte warten...", "success": "✅ Hintergrundersetzung abgeschlossen!", "failed": "❌ Hintergrundersetzung fehlgeschlagen: {error}", "need_two_photo": "❌ Hintergrundersetzung benötigt zwei Fotos!\n\nBitte senden Sie Personenfoto und Hintergrundfoto, dann verwenden <PERSON> den {command} Be<PERSON>hl", "same_size_needed": "⚠️ Empfehlung: Verwenden Sie Fotos derselben Größe für beste Ergebnisse"}, "be": {"processing": "🎨 Hintergrundentfernung wird verarbeitet, bitte warten...", "success": "✅ Hintergrundentfernung abgeschlossen!", "failed": "❌ Hintergrundentfernung fehlgeschlagen: {error}", "need_photo": "❌ Hintergrundentfernung benötigt ein Bild!\n\nBitte laden Sie ein Bild hoch und verwenden Sie den {command} Be<PERSON>hl"}, "moderation": {"failed": "❌ Bildmoderation fehlgeschlagen: {error}", "rejected": "❌ Bildmoderation nicht bestanden, bitte überprüfen Sie, ob der Bildinhalt den Richtlinien entspricht", "passed": "✅ Bildmoderation bestanden, KI-Verarbeitung...", "processing": "🔍 Bildmoderation läuft, bitte warten..."}}}