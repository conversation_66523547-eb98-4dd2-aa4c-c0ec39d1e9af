{"errors": {"invalid_command": "Commande invalide", "permission_denied": "Permission refusée, impossible d'effectuer cette opération", "vip_required": "Cette fonction nécessite un abonnement VIP", "general_error": "Une erreur s'est produite, veuillez réessayer plus tard", "command_failed": "Échec de l'exécution de la commande, ve<PERSON><PERSON><PERSON> réessayer", "missing_prompt": "Aucune invite reç<PERSON>, veuil<PERSON><PERSON> réessayer!\n\nFormat: {command} invite", "missing_code": "Paramètres incorrects!\n\nFormat: {command} code de rédemption\n\nExemple: {command} VIP2024", "callback_not_owned": "Ce bouton n'est pas pour vous", "insufficient_balance": "❌ Solde insuffisant!\n\nVous avez besoin de {cost} points pour cette fonction\nSolde actuel: {balance} points\n\nObtenez plus de points:\n• Utilisez des codes de rédemption: /sk <code>\n• Invitez des amis pour des récompenses"}, "vip": {"status": {"active": "Actif", "inactive": "Inactif", "expired": "Expiré"}, "expires_at": "Date d'expiration：{date}"}, "instructions": {"title": "🤖Guide des fonctions TGAI🤖", "content": "🌐GPT4.0 en ligne\nCommande : net\nUtilisation : net analyse des actions Centrica / net météo à Los Angeles aujourd'hui\n\n🔍GPT4mini\nCommande : ask(ou a)\nUtilisation : ask quelles sont les plateformes d'échange de cryptomonnaies aux États-Unis ?\nFonctions : chat, questions-réponses, reconnaissance d'images, suivi de mémoire\n\n🧩GPT4o\nCommande : 4o\nUtilisation : 4o analyse de la tendance des actions XXX\nFonctions : analyse d'actions, données cryptomonnaies, chat intelligent, reconnaissance d'images\n\n🍻Claude-h\nCommande : ck\nUtilisation : ck comment répondre à ce message ?\nFonctions : réponse rapide, aide au chat, suivi de mémoire\n\n🤡Changement de visage\nCommande : fc\nUtilisation : télécharger 2 photos + fc\n\n🎨Génération d'images\nCommande : draw\nUtilisation : draw scène matinale à Times Square\n\n🧑‍🔬OpenAI-o1\nCommande : o1\nUtilisation : o1 analyser ceci d'un point de vue logique\n\n🎐Changement d'arrière-plan\nCommande : br\nUtilisation : envoyer photo de personne + photo d'arrière-plan (même taille)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "Rejoignez le canal et suivez les mises à jour de TAI, soyez le premier à recevoir les dernières nouvelles sur l'IA❕❕❕ \n\nBouton : Canal Officiel TGAI - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "Configuration de la langue", "instructions": "Instructions", "channel_notifications": "Notifications du canal", "get_invite_code": "Obtenir le code d'invitation", "group_management": "Gestion des groupes", "back_to_menu": "← Retour au menu"}, "start": {"welcome": {"message": "Bienvenue sur notre bot de dessin ! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "Votre lien d'invitation : {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "Invitation réussie !\nUtilisateur invité : {user_id}, vous avez gagné 200 points !", "fail": "Échec de l'invitation de l'utilisateur {user_id} !\nMessage d'erreur : {error}\nVotre lien d'invitation : {link}", "invited_by": "<PERSON><PERSON> avez été invité par l'utilisateur ID {user_id}. Votre code d'invitation : {link}"}, "error": {"self_invite": "Vous ne pouvez pas vous inviter vous-même !", "already_invited": "Cet utilisateur a déjà été invité par l'utilisateur ID {invited_by}", "time_limit": "L'utilisateur s'est inscrit il y a plus de 24 heures et ne peut pas être invité"}}, "language": {"choose": "Veuillez sélectionner votre langue：\n\nLangues disponibles：", "changed": "✅ Votre langue a été changée en français", "error": "Échec du changement de langue, veuil<PERSON>z réessayer plus tard", "current": "Langue actuelle : français"}, "group": {"management": {"tutorial": "Seulement deux étapes pour ajouter un groupe：（ou cliquez sur le bouton ci-dessous）\n1. Ajoutez le bot au groupe et définissez-le comme administrateur\n2. Assurez-vous que vous êtes administrateur du groupe\n3. Si après avoir terminé les deux premières étapes vous ne voyez pas le groupe, exécutez la commande /update dans le groupe\n4. Cliquez sur le nom du groupe pour le gérer", "add_bot": "【Cliquez ici pour ajouter le bot au groupe】", "prev_page": "◀️Précédent", "next_page": "Suivant▶️", "refresh": "🔄Actualiser", "help": "❓Aide", "group_info": "📑 Groupe sélectionné：\nNom du groupe：{title}\nID du groupe：{chat_id}\n\nSélectionnez une action：", "delete_binding": "❌ Dissocier le groupe", "back_to_list": "⬅️ Retour à la liste", "unbind_success": "Groupe dissocié avec succès！", "unbind_failed": "Échec de la dissociation, veuillez réessayer plus tard", "group_not_found": "Informations du groupe non trouvées"}, "help": {"title": "Centre d'aide：", "content": "1. Que faire si j'ai accidentellement dissocié le groupe？\nRéponse：Supprimez le bot du groupe et réinvitez-le.\n\n2. Comment ajouter correctement le bot au groupe？\nRéponse：Cliquez sur le bouton【Cliquez ici pour ajouter le bot au groupe】et sélectionnez le groupe cible.\n\n3. Pourquoi je ne vois pas mon groupe？\nRéponse：Assurez-vous que：\n   - Le bot est défini comme administrateur du groupe\n   - Vous êtes administrateur du groupe\n   - Vous avez cliqué sur le bouton d'actualisation", "back": "Retour"}, "binding": {"success": "<PERSON><PERSON><PERSON> de m'avoir ajouté au groupe！\nID du groupe：{chat_id}\nNom du groupe：{title}\nUtilisateur：{user_id}\nNom d'utilisateur：@{username}", "failed": "Échec de la liaison！Lors de l'invitation du bot, définissez-le comme administrateur pour vérifier votre statut d'administrateur du groupe. Supprimez le bot et réinvitez-le au groupe.\nID du groupe：{chat_id}\nNom du groupe：{title}\nUtilisateur：{user_id}\nNom d'utilisateur：@{username}", "success_user": "<PERSON><PERSON><PERSON> de m'avoir ajouté au groupe！\nID du groupe：{chat_id}\nNom du groupe：{title}\nUtilisateur：{user_id}\nNom d'utilisateur：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "SupprimerPubs", "generate_remove_ads_code": "GénérerCodeSuppressionPubs"}, "invalid_params": "Paramètres invalides. Veuillez vérifier le format.\nFormat correct : {command} <code de vérification>", "invalid_code": "Code de vérification invalide ou déjà utilisé ! Veuillez vérifier le code.\nFormat correct : {command} <code de vérification>", "remove_success": "Publicités supprimées avec succès", "remove_failed": "Échec de la suppression des publicités. Erreur : {error}", "generate_success": "Code de suppression des publicités généré, vérifiez la pièce jointe", "error": "Échec de la suppression des publicités. Veuillez contacter l'administrateur. Erreur : {error}"}, "info": {"title": "📊 Informations utilisateur", "user_id": "ID utilisateur: {user_id}", "username": "Nom d'utilisateur: @{username}", "language": "Langue: {language}", "vip_status": "Statut VIP: {status}", "remaining_power": "Énergie restante: {power}", "vip_status_normal": "Utilisateur normal", "vip_status_vip": "Utilisateur VIP", "vip_status_expired": "Expiré"}, "sk": {"verifying": "🎫 Vérification du code de rédemption: {code}...", "success": "✅ Code de rédemption vérifié avec succès!\n\nRécompense reçue: {reward}", "invalid": "❌ Code de rédemption invalide ou expiré", "already_used": "❌ Ce code de rédemption a déjà été utilisé"}, "sign_in": {"success": "✅ Connexion réussie!\n\n🎁 Points quotidiens gagnés: {reward}\n💰 Solde quotidien actuel: {daily_balance}\n📊 Solde total: {total_balance}", "already_signed_in": "Vous vous êtes déjà connecté aujourd'hui!\n\nProchaine connexion disponible dans {next_check_in_hours} heures", "cannot_sign_in": "❌ Impossible de se connecter pour le moment, veuil<PERSON>z réessayer plus tard", "check_status": "🔍 Vérification du statut de connexion..."}, "admin": {"moderate_image": {"usage": "Usage: /moderate_image <url_image>\nExemple: /moderate_image https://example.com/image.jpg\n\nOu utilisez /moderate_reply pour modérer une image en répondant à un message contenant une image."}}, "ai_command": {"draw": {"processing": "💡Demande de génération d'image reçue!\n🌟Votre prompt: {prompt}\n🌈Veuillez patienter...", "success": "🎨 Image générée avec succès!", "failed": "❌ Échec de la génération d'image: {error}", "no_prompt": "❌ Veuillez fournir un prompt pour la génération d'image!\n\nFormat: {command} prompt\nExemple: {command} belle peinture de paysage"}, "fc": {"processing": "🤡 Échange de visage en cours, veuil<PERSON><PERSON> patienter...", "success": "✅ Échange de visage terminé!", "failed": "❌ Échec de l'échange de visage: {error}", "need_two_photo": "❌ L'échange de visage nécessite deux photos!\n\nVeuillez télécharger deux photos et utiliser la commande {command}"}, "br": {"processing": "🎐 Remplacement d'arrière-plan en cours, veuillez patienter...", "success": "✅ Remplacement d'arrière-plan terminé!", "failed": "❌ Échec du remplacement d'arrière-plan: {error}", "need_two_photo": "❌ Le remplacement d'arrière-plan nécessite deux photos!\n\nV<PERSON><PERSON>z envoyer une photo de personne et une photo d'arrière-plan, puis utiliser la commande {command}", "same_size_needed": "⚠️ Recommandation: utilisez des photos de même taille pour de meilleurs résultats"}, "be": {"processing": "🎨 Suppression d'arrière-plan en cours, veuillez patienter...", "success": "✅ Suppression d'arrière-plan terminée!", "failed": "❌ Échec de la suppression d'arrière-plan: {error}", "need_photo": "❌ La suppression d'arrière-plan nécessite une image!\n\nVeuillez télécharger une image et utiliser la commande {command}"}, "moderation": {"failed": "❌ Échec de la modération d'image: {error}", "rejected": "❌ La modération d'image a échoué, veuillez vérifier si le contenu de l'image respecte les directives", "passed": "✅ Modération d'image réussie, traitement IA...", "processing": "🔍 Modération d'image en cours, veuillez patienter..."}}}