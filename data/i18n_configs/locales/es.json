{"errors": {"invalid_command": "Comand<PERSON>", "permission_denied": "Permiso denegado, no se puede realizar esta operación", "vip_required": "Esta función requiere membresía VIP", "general_error": "Se produjo un error, por favor inténtelo más tarde", "command_failed": "Error al ejecutar el comando, por favor inténtelo de nuevo", "missing_prompt": "No se recibió prompt, ¡inténtelo de nuevo!\n\nFormato: {command} prompt", "missing_code": "¡Parámetros incorrectos!\n\nFormato: {command} código de canje\n\nEjemplo: {command} VIP2024", "callback_not_owned": "Este botón no es para usted", "insufficient_balance": "❌ ¡Saldo insuficiente!\n\nNecesita {cost} puntos para usar esta función\nSaldo actual: {balance} puntos\n\nObtener más puntos:\n• Use códigos de canje: /sk <código>\n• Invite amigos para obtener recompensas"}, "vip": {"status": {"active": "Activo", "inactive": "Inactivo", "expired": "<PERSON><PERSON><PERSON>"}, "expires_at": "<PERSON><PERSON> venc<PERSON>o：{date}"}, "instructions": {"title": "🤖Guía de funciones TGAI🤖", "content": "🌐GPT4.0 en línea\nComando: net\nUso: net análisis de acciones Centrica / net clima en Los Ángeles hoy\n\n🔍GPT4mini\nComando: ask(o a)\nUso: ask ¿qué exchanges de criptomonedas hay en EE.UU.?\nFunciones: chat, preguntas y respuestas, reconocimiento de imágenes, seguimiento de memoria\n\n🧩GPT4o\nComando: 4o\nUso: 4o análisis de tendencia de acciones XXX\nFunciones: análisis de acciones, datos de criptomonedas, chat inteligente, reconocimiento de imágenes\n\n🍻Claude-h\nComando: ck\nUso: ck ¿cómo responder a este mensaje?\nFunciones: respuesta rápida, ayuda en chat, seguimiento de memoria\n\n🤡Cambio de cara\nComando: fc\nUso: subir 2 fotos + fc\n\n🎨Generación de imágenes\nComando: draw\nUso: draw escena matutina en Times Square\n\n🧑‍🔬OpenAI-o1\nComando: o1\nUso: o1 analizar esto desde un punto de vista lógico\n\n🎐Cambio de fondo\nComando: br\nUso: enviar foto de persona + foto de fondo (mismo tamaño)", "photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "channel": {"notification": "¡Únete al canal y mantente al día con las actualizaciones de TAI, sé el primero en recibir las últimas noticias sobre IA❕❕❕ \n\nBotón: Canal Oficial de TGAI - https://t.me/TGAI_ChatGPTs", "notification_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}, "keyboard": {"language": "Configuración de idioma", "instructions": "Instrucciones", "channel_notifications": "Notificaciones del canal", "get_invite_code": "Obtener código de invitación", "group_management": "Gestión de grupos", "back_to_menu": "← Volver al menú"}, "start": {"welcome": {"message": "¡Bienvenido a nuestro bot de dibujo! 🎨", "message_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ"}}, "invite": {"start": {"link": "Tu enlace de invitación: {link}", "link_photo_id": "AgACAgUAAxkBAAJOgmenR8hkiXnoG7wlgQuNKNHkYpqPAAKPwTEbkVdAVXBfK3DziOdAAQADAgADeQADNgQ", "success": "¡Invitación exitosa!\nUsuario invitado: {user_id}, ¡has ganado 200 puntos!", "fail": "¡Error al invitar al usuario {user_id}!\nMensaje de error: {error}\nTu enlace de invitación: {link}", "invited_by": "Has sido invitado por el usuario ID {user_id}. Tu código de invitación: {link}"}, "error": {"self_invite": "¡No puedes invitarte a ti mismo!", "already_invited": "Este usuario ya fue invitado por el usuario ID {invited_by}", "time_limit": "El usuario se registró hace más de 24 horas y no puede ser invitado"}}, "language": {"choose": "Por favor, seleccione su idioma：\n\nIdiomas disponibles：", "changed": "✅ Su idioma ha sido cambiado a español", "error": "Error al cambiar el idioma, por favor inténtelo más tarde", "current": "Idioma actual: español"}, "group": {"management": {"tutorial": "Solo dos pasos para agregar un grupo：（o haga clic en el botón de abajo）\n1. Agregue el bot al grupo y asígnelo como administrador\n2. Asegúrese de que usted es administrador del grupo\n3. Si después de completar los dos primeros pasos no ve el grupo, ejecute el comando /update en el grupo\n4. Haga clic en el nombre del grupo para administrarlo", "add_bot": "【Haga clic aquí para agregar el bot al grupo】", "prev_page": "◀️Anterior", "next_page": "Siguiente▶️", "refresh": "🔄Actualizar", "help": "❓Ayuda", "group_info": "📑 Grupo seleccionado：\nNombre del grupo：{title}\nID del grupo：{chat_id}\n\nSeleccione una acción：", "delete_binding": "❌ Desvincular grupo", "back_to_list": "⬅️ Volver a la lista", "unbind_success": "¡Grupo desvinculado con éxito！", "unbind_failed": "<PERSON><PERSON><PERSON> al desvin<PERSON>, inténtelo más tarde", "group_not_found": "Información del grupo no encontrada"}, "help": {"title": "Centro de ayuda：", "content": "1. ¿Qué hacer si desvincule accidentalmente el grupo？\nRespuesta：Elimine el bot del grupo y vuelva a invitarlo.\n\n2. ¿Cómo agregar correctamente el bot al grupo？\nRespuesta：Haga clic en el botón【Haga clic aquí para agregar el bot al grupo】y seleccione el grupo objetivo.\n\n3. ¿Por qué no veo mi grupo？\nRespuesta：Asegúrese de que：\n   - El bot está configurado como administrador del grupo\n   - Usted es administrador del grupo\n   - Ha hecho clic en el botón de actualizar", "back": "Volver"}, "binding": {"success": "¡Gracias por agregarme al grupo！\nID del grupo：{chat_id}\nNombre del grupo：{title}\nUsuario：{user_id}\nNombre de usuario：@{username}", "failed": "¡Error al vincular！Al invitar al bot, as<PERSON><PERSON>lo como administrador para verificar su estado de administrador del grupo. Elimine el bot y vuelva a invitarlo al grupo.\nID del grupo：{chat_id}\nNombre del grupo：{title}\nUsuario：{user_id}\nNombre de usuario：@{username}", "success_user": "¡Gracias por agregarme al grupo！\nID del grupo：{chat_id}\nNombre del grupo：{title}\nUsuario：{user_id}\nNombre de usuario：@{username}"}, "setting": {}}, "chat_ads": {"commands": {"remove_chat_ads": "EliminarAnuncios", "generate_remove_ads_code": "GenerarCódigoEliminarAnuncios"}, "invalid_params": "Parámetros inválidos. Por favor, verifica el formato.\nFormato correcto: {command} <código de verificación>", "invalid_code": "¡Código de verificación inválido o ya utilizado! Por favor, verifica el código.\nFormato correcto: {command} <código de verificación>", "remove_success": "Anuncios eliminados con éxito", "remove_failed": "Error al eliminar anuncios. Error: {error}", "generate_success": "Código de eliminación de anuncios generado, revisa el archivo adjunto", "error": "Error al eliminar anuncios. Por favor, contacta al administrador. Error: {error}"}, "info": {"title": "📊 Información del usuario", "user_id": "ID de usuario: {user_id}", "username": "Nombre de usuario: @{username}", "language": "Idioma: {language}", "vip_status": "Estado VIP: {status}", "remaining_power": "Energía restante: {power}", "vip_status_normal": "Usuario normal", "vip_status_vip": "Usuario VIP", "vip_status_expired": "<PERSON><PERSON><PERSON>"}, "sk": {"verifying": "🎫 Verificando código de canje: {code}...", "success": "✅ ¡Código de canje verificado con éxito!\n\nRecompensa recibida: {reward}", "invalid": "❌ Código de canje inválido o expirado", "already_used": "❌ Este código de canje ya ha sido utilizado"}, "sign_in": {"success": "✅ ¡Check-in exitoso!\n\n🎁 Puntos diarios obtenidos: {reward}\n💰 Saldo diario actual: {daily_balance}\n📊 Saldo total: {total_balance}", "already_signed_in": "¡Ya has hecho check-in hoy!\n\nPróximo check-in disponible en {next_check_in_hours} horas", "cannot_sign_in": "❌ No se puede hacer check-in en este momento, inténtelo más tarde", "check_status": "🔍 Verificando estado de check-in..."}, "admin": {"moderate_image": {"usage": "Uso: /moderate_image <URL_imagen>\nEjemplo: /moderate_image https://example.com/image.jpg\n\nO use /moderate_reply para moderar una imagen respondiendo a un mensaje que contenga una imagen."}}, "ai_command": {"draw": {"processing": "💡¡Solicitud de generación de imagen recibida!\n🌟Su prompt: {prompt}\n🌈Por favor espere...", "success": "🎨 ¡Imagen generada con éxito!", "failed": "❌ Error en la generación de imagen: {error}", "no_prompt": "❌ ¡Por favor proporcione un prompt para la generación de imagen!\n\nFormato: {command} prompt\n<PERSON><PERSON><PERSON><PERSON>: {command} hermoso paisaje"}, "fc": {"processing": "🤡 Procesando cambio de cara, por favor espere...", "success": "✅ ¡Cambio de cara completado!", "failed": "❌ Error en el cambio de cara: {error}", "need_two_photo": "❌ ¡El cambio de cara requiere dos fotos!\n\nPor favor suba dos fotos y use el comando {command}"}, "br": {"processing": "🎐 Procesando reemplazo de fondo, por favor espere...", "success": "✅ ¡Reemplazo de fondo completado!", "failed": "❌ Error en el reemplazo de fondo: {error}", "need_two_photo": "❌ ¡El reemplazo de fondo requiere dos fotos!\n\nPor favor envíe foto de persona y foto de fondo, luego use el comando {command}", "same_size_needed": "⚠️ Se recomienda usar fotos del mismo tamaño para mejores resultados"}, "be": {"processing": "🎨 Procesando eliminación de fondo, por favor espere...", "success": "✅ ¡Eliminación de fondo completada!", "failed": "❌ Error en la eliminación de fondo: {error}", "need_photo": "❌ ¡La eliminación de fondo requiere una imagen!\n\nPor favor suba una imagen y use el comando {command}"}, "moderation": {"failed": "❌ Error en la moderación de imagen: {error}", "rejected": "❌ La moderación de imagen falló, por favor verifique si el contenido de la imagen cumple con las pautas", "passed": "✅ Moderación de imagen aprobada, procesando con IA...", "processing": "🔍 Moderación de imagen en progreso, por favor espere..."}}}