#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
i18n 配置迁移脚本
将旧的 i18n 配置格式迁移到新的格式

使用方法:
1. 将此脚本放到生产环境的 data 目录下
2. 运行: python3 migrate_i18n.py
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any
import shutil
from datetime import datetime

# 新格式的模板结构 (基于 zh_CN.json 的结构)
NEW_FORMAT_TEMPLATE = {
    "errors": {
        "invalid_command": "",
        "permission_denied": "",
        "vip_required": "",
        "general_error": "",
        "command_failed": "",
        "missing_prompt": "",
        "missing_code": "",
        "callback_not_owned": "",
        "insufficient_balance": ""
    },
    "vip": {
        "status": {
            "active": "",
            "inactive": "",
            "expired": ""
        },
        "expires_at": ""
    },
    "instructions": {
        "title": "",
        "content": "",
        "photo_id": ""
    },
    "channel": {
        "notification": "",
        "notification_photo_id": ""
    },
    "keyboard": {
        "language": "",
        "instructions": "",
        "channel_notifications": "",
        "get_invite_code": "",
        "group_management": "",
        "back_to_menu": ""
    },
    "start": {
        "welcome": {
            "message": "",
            "message_photo_id": ""
        }
    },
    "invite": {
        "start": {
            "link": "",
            "link_photo_id": "",
            "success": "",
            "fail": "",
            "invited_by": ""
        },
        "error": {
            "self_invite": "",
            "already_invited": "",
            "time_limit": ""
        }
    },
    "language": {
        "choose": "",
        "changed": "",
        "error": "",
        "current": ""
    },
    "group": {
        "management": {
            "tutorial": "",
            "add_bot": "",
            "prev_page": "",
            "next_page": "",
            "refresh": "",
            "help": "",
            "group_info": "",
            "delete_binding": "",
            "back_to_list": "",
            "unbind_success": "",
            "unbind_failed": "",
            "group_not_found": ""
        },
        "help": {
            "title": "",
            "content": "",
            "back": ""
        },
        "binding": {
            "success": "",
            "failed": "",
            "success_user": ""
        },
        "setting": {}
    },
    "chat_ads": {
        "commands": {
            "remove_chat_ads": "",
            "generate_remove_ads_code": ""
        },
        "invalid_params": "",
        "invalid_code": "",
        "remove_success": "",
        "remove_failed": "",
        "generate_success": "",
        "error": ""
    },
    "info": {
        "title": "",
        "user_id": "",
        "username": "",
        "language": "",
        "vip_status": "",
        "remaining_power": "",
        "vip_status_normal": "",
        "vip_status_vip": "",
        "vip_status_expired": ""
    },
    "sk": {
        "verifying": "",
        "success": "",
        "invalid": "",
        "already_used": ""
    },
    "sign_in": {
        "success": "",
        "already_signed_in": "",
        "cannot_sign_in": "",
        "check_status": ""
    },
    "admin": {
        "moderate_image": {
            "usage": ""
        }
    },
    "ai_command": {
        "draw": {
            "processing": "",
            "success": "",
            "failed": "",
            "no_prompt": ""
        },
        "fc": {
            "processing": "",
            "success": "",
            "failed": "",
            "need_two_photo": ""
        },
        "br": {
            "processing": "",
            "success": "",
            "failed": "",
            "need_two_photo": "",
            "same_size_needed": ""
        },
        "be": {
            "processing": "",
            "success": "",
            "failed": "",
            "need_photo": ""
        },
        "moderation": {
            "failed": "",
            "rejected": "",
            "passed": "",
            "processing": ""
        }
    },
    "payment": {
        "errors": {
            "amount_too_small": "",
            "amount_too_large": "",
            "create_order_failed": "",
            "invalid_amount": "",
            "service_unavailable": ""
        },
        "order_created": "",
        "payment_success": "",
        "copy_wallet": "",
        "wallet_copied": "",
        "wallet_address": "",
        "invalid_wallet": "",
        "usage": ""
    }
}


def print_banner():
    """打印脚本横幅"""
    print("=" * 60)
    print("🔄 i18n 配置迁移脚本")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()


def backup_existing_files(locales_dir: Path) -> Path:
    """备份现有的locale文件"""
    print("📦 开始备份现有文件...")
    
    backup_dir = locales_dir.parent / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    backup_dir.mkdir(exist_ok=True)
    
    backed_up_files = []
    for locale_file in locales_dir.glob("*.json"):
        backup_file = backup_dir / locale_file.name
        shutil.copy2(locale_file, backup_file)
        backed_up_files.append(locale_file.name)
        print(f"  ✅ 已备份: {locale_file.name} -> {backup_file}")
    
    print(f"📦 备份完成! 备份目录: {backup_dir}")
    print(f"📦 共备份 {len(backed_up_files)} 个文件: {', '.join(backed_up_files)}")
    print()
    
    return backup_dir


def migrate_to_new_format(old_config: Dict[str, Any]) -> Dict[str, Any]:
    """将旧格式配置迁移到新格式"""
    print("🔄 开始格式迁移...")

    # 创建新格式的副本
    new_config = json.loads(json.dumps(NEW_FORMAT_TEMPLATE))

    def map_old_to_new(old_data: Dict[str, Any], new_data: Dict[str, Any], path: str = "") -> None:
        """将旧配置映射到新格式"""
        for key, value in old_data.items():
            current_path = f"{path}.{key}" if path else key

            if key in new_data:
                if isinstance(value, dict) and isinstance(new_data[key], dict):
                    # 递归处理嵌套字典
                    map_old_to_new(value, new_data[key], current_path)
                else:
                    # 直接映射值
                    new_data[key] = value
                    print(f"  ✅ 映射: {current_path} = {repr(value)}")
            else:
                # 旧配置中有新格式中没有的键
                print(f"  ⚠️  跳过未知键: {current_path}")

    # 执行映射
    map_old_to_new(old_config, new_config)

    print("🔄 格式迁移完成!")
    print()

    return new_config


def update_locale_file(locale_file: Path) -> bool:
    """更新单个locale文件到新格式"""
    print(f"📝 处理文件: {locale_file.name}")

    try:
        # 读取现有配置
        if not locale_file.exists():
            print(f"  ❌ 文件不存在: {locale_file.name}")
            return False

        with open(locale_file, 'r', encoding='utf-8') as f:
            old_config = json.load(f)
        print(f"  📖 已读取旧配置，包含 {len(old_config)} 个顶级键")

        # 检查是否已经是新格式
        if is_new_format(old_config):
            print(f"  ✅ 文件已经是新格式，跳过: {locale_file.name}")
            return True

        # 迁移到新格式
        new_config = migrate_to_new_format(old_config)

        # 写入更新后的配置
        with open(locale_file, 'w', encoding='utf-8') as f:
            json.dump(new_config, f, ensure_ascii=False, indent=2)

        print(f"  ✅ 文件迁移成功: {locale_file.name}")
        print(f"  📊 新配置包含 {len(new_config)} 个顶级键")
        print()

        return True

    except Exception as e:
        print(f"  ❌ 迁移文件失败: {locale_file.name}")
        print(f"  错误: {str(e)}")
        print()
        return False


def is_new_format(config: Dict[str, Any]) -> bool:
    """检查配置是否已经是新格式"""
    # 检查新格式特有的键
    new_format_indicators = [
        "ai_command",
        "payment",
        "sign_in",
        "sk",
        "info"
    ]

    for indicator in new_format_indicators:
        if indicator in config:
            return True

    # 检查是否有新格式的嵌套结构
    if "instructions" in config and isinstance(config["instructions"], dict):
        if "title" in config["instructions"]:
            return True

    return False


def validate_json_file(file_path: Path) -> bool:
    """验证JSON文件格式是否正确"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        return True
    except json.JSONDecodeError as e:
        print(f"  ❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 文件读取错误: {e}")
        return False


def show_config_diff(old_config: Dict[str, Any], new_config: Dict[str, Any]) -> None:
    """显示配置差异"""
    print("📋 配置变更摘要:")

    def count_keys(config: Dict[str, Any], prefix: str = "") -> int:
        """递归计算配置中的键数量"""
        count = 0
        for key, value in config.items():
            current_path = f"{prefix}.{key}" if prefix else key
            count += 1
            if isinstance(value, dict):
                count += count_keys(value, current_path)
        return count

    old_keys = count_keys(old_config)
    new_keys = count_keys(new_config)

    print(f"  📊 原有配置项: {old_keys}")
    print(f"  📊 新增配置项: {new_keys - old_keys}")
    print(f"  📊 总配置项: {new_keys}")
    print()


def confirm_migration() -> bool:
    """确认是否继续迁移"""
    print("⚠️  重要提示:")
    print("  - 此操作将修改现有的locale文件")
    print("  - 已自动创建备份，可以随时恢复")
    print("  - 建议在测试环境先验证结果")
    print()

    while True:
        response = input("🤔 是否继续执行迁移? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes/是 或 n/no/否")


def main():
    """主函数"""
    print_banner()

    # 确定工作目录
    script_dir = Path(__file__).parent
    locales_dir = script_dir / "i18n_configs" / "locales"

    print(f"📂 工作目录: {script_dir}")
    print(f"📂 Locales目录: {locales_dir}")
    print()

    # 检查目录是否存在
    if not locales_dir.exists():
        print(f"❌ Locales目录不存在: {locales_dir}")
        print("请确保脚本在正确的data目录下运行")
        sys.exit(1)

    # 获取所有locale文件
    locale_files = list(locales_dir.glob("*.json"))
    if not locale_files:
        print(f"❌ 在 {locales_dir} 中未找到任何JSON文件")
        sys.exit(1)

    print(f"🔍 找到 {len(locale_files)} 个locale文件:")
    for f in locale_files:
        print(f"  - {f.name}")
    print()

    # 验证现有文件格式
    print("🔍 验证现有文件格式...")
    invalid_files = []
    for locale_file in locale_files:
        print(f"  检查: {locale_file.name}")
        if not validate_json_file(locale_file):
            invalid_files.append(locale_file.name)

    if invalid_files:
        print(f"❌ 发现 {len(invalid_files)} 个无效的JSON文件:")
        for f in invalid_files:
            print(f"  - {f}")
        print("请修复这些文件后重新运行脚本")
        sys.exit(1)

    print("✅ 所有文件格式验证通过!")
    print()

    # 显示配置预览
    sample_file = locale_files[0]  # 使用第一个文件作为示例
    with open(sample_file, 'r', encoding='utf-8') as f:
        existing_config = json.load(f)

    if not is_new_format(existing_config):
        migrated_sample = migrate_to_new_format(existing_config.copy())
        show_config_diff(existing_config, migrated_sample)
    else:
        print("📋 示例文件已经是新格式，无需迁移")
        print()

    # 确认是否继续
    if not confirm_migration():
        print("❌ 用户取消操作")
        sys.exit(0)

    # 备份现有文件
    backup_dir = backup_existing_files(locales_dir)

    # 更新每个locale文件
    success_count = 0
    failed_files = []

    print("🚀 开始更新locale文件...")
    print()

    for locale_file in locale_files:
        if update_locale_file(locale_file):
            success_count += 1
            # 验证更新后的文件
            if validate_json_file(locale_file):
                print(f"  ✅ 文件验证通过: {locale_file.name}")
            else:
                print(f"  ⚠️  文件验证失败: {locale_file.name}")
        else:
            failed_files.append(locale_file.name)

    # 打印总结
    print("=" * 60)
    print("📊 迁移完成总结")
    print("=" * 60)
    print(f"✅ 成功更新: {success_count} 个文件")
    if failed_files:
        print(f"❌ 失败文件: {len(failed_files)} 个")
        for f in failed_files:
            print(f"  - {f}")
    print(f"📦 备份目录: {backup_dir}")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    if failed_files:
        print("⚠️  部分文件更新失败，请检查错误信息并手动处理")
        print(f"💡 可以从备份目录恢复: {backup_dir}")
        sys.exit(1)
    else:
        print("🎉 所有文件迁移成功!")
        print("💡 如需回滚，请使用备份目录中的文件")


def restore_from_backup():
    """从备份恢复文件"""
    print("🔄 备份恢复工具")
    print("=" * 40)

    script_dir = Path(__file__).parent
    locales_dir = script_dir / "i18n_configs" / "locales"

    # 查找备份目录
    backup_dirs = [d for d in script_dir.iterdir() if d.is_dir() and d.name.startswith("backup_")]

    if not backup_dirs:
        print("❌ 未找到任何备份目录")
        return

    # 按时间排序，最新的在前
    backup_dirs.sort(key=lambda x: x.name, reverse=True)

    print("📦 可用的备份:")
    for i, backup_dir in enumerate(backup_dirs, 1):
        backup_files = list(backup_dir.glob("*.json"))
        print(f"  {i}. {backup_dir.name} ({len(backup_files)} 个文件)")

    print("  0. 取消")
    print()

    while True:
        try:
            choice = int(input("请选择要恢复的备份 (输入数字): "))
            if choice == 0:
                print("❌ 用户取消操作")
                return
            elif 1 <= choice <= len(backup_dirs):
                selected_backup = backup_dirs[choice - 1]
                break
            else:
                print(f"请输入 0-{len(backup_dirs)} 之间的数字")
        except ValueError:
            print("请输入有效的数字")

    print(f"📦 选择的备份: {selected_backup.name}")

    # 确认恢复
    response = input("⚠️  确定要恢复此备份吗? 这将覆盖当前文件 (y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 用户取消操作")
        return

    # 执行恢复
    backup_files = list(selected_backup.glob("*.json"))
    success_count = 0

    print("🚀 开始恢复文件...")
    for backup_file in backup_files:
        target_file = locales_dir / backup_file.name
        try:
            shutil.copy2(backup_file, target_file)
            print(f"  ✅ 已恢复: {backup_file.name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ 恢复失败: {backup_file.name} - {e}")

    print(f"🎉 恢复完成! 成功恢复 {success_count}/{len(backup_files)} 个文件")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--restore":
        restore_from_backup()
    else:
        main()
