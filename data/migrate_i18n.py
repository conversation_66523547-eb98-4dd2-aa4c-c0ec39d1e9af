#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
i18n 配置迁移脚本
将旧的 i18n 配置格式迁移到新的格式

使用方法:
1. 将此脚本放到生产环境的 data 目录下
2. 运行: python3 migrate_i18n.py
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, Any
import shutil
from datetime import datetime

# 旧的配置数据 (您提供的格式)
OLD_CONFIG = {
    "errors": {
        "invalid_command": "无效的命令",
        "permission_denied": "权限不足，无法执行此操作",
        "vip_required": "此功能需要VIP会员才能使用",
        "general_error": "发生错误，请稍后重试",
        "command_failed": "命令执行失败，请重试"
    },
    "invite": {
        "start": {
            "link": "您的邀请链接是：{link}",
            "link_photo_id": "AgACAgUAAxkBAAJF92doM4-4B8Ppug_LYBMnrH9bnYhTAALZwjEbDDNJV7Ky1f3LJkmVAQADAgADeQADNgQ",
            "success": "邀请成功！\n您已邀请用户：{user_id}，获得200算力！",
            "fail": "您邀请的用户 {user_id} 邀请失败！\n错误信息：{error}\n您的邀请链接：{link}",
            "invited_by": "您已被用户ID {user_id} 邀请, 你的邀请码是：{link}"
        },
        "error": {
            "self_invite": "您不能邀请自己！",
            "already_invited": "该用户已被用户ID {invited_by} 邀请",
            "time_limit": "该用户注册时间超过24小时，无法被邀请"
        }
    },
    "keyboard": {
        "language": "language/语言",
        "instructions": "使用说明",
        "channel_notifications": "频道通知",
        "get_invite_code": "获取邀请码",
        "group_management": "群组管理"
    },
    "instructions": {
        "content": "🤖TGAI使用功能大全🤖\n\n🌐联网版GPT4.0\n命令：net\n操作：net 帮我分析 Centrica 这支股票/net 美国洛杉矶今天天气如何？\n\n🔍GPT4mini：\n命令：ask\n简约命令：a\n操作：ask 美国有哪些区块链交易所（ask后面需要添加空格）\n功能：聊天，帮助解答，多图识别，带数据记忆跟踪（一直引用上一条信息回复，适用于发展型聊天跟进）\n\n🧩GPT4o：\n命令：4o\n操作：4o 帮我分析Xxx这支股票未来的走势如何？（4o后面需要添加空格）\n功能：全方位解析股票数据，加密货币数据，高情商对话解答，识图解析数据。带数据记忆功能（一直引用上一条信息回复，适用于发展型聊天跟进）支持多图识图功能\n\n🍻Claude-h：\n命令：ck\n操作：ck xxxxxxxx我将如何高情商回复这句话？\n功能：高情商AI模型，反应速度比cs更快！如果是解决聊天困扰，请用ck解答。带数据记忆功能（一直引用上一条信息回复，适用于发展型聊天跟进）支持多图识图功能。\n\n🤡换脸AI：\n命令：fc\n操作：上传两张照片➕fc（换脸结果为前脸换后脸）\n解答：换脸AI是结合人体五官各种体征合成更自然的表情效果。\n\n🎨生图AI：\n命令：draw\n操作：draw 早上在纽约时代广场吃汉堡和可乐\n解答：这次换成拟真照片生成模型，只要你的指令输入到位，照片就会更加真实！\n\n🧑‍🔬OpenAI-o1（最新AI）\n命令：o1\n操作：o1 帮我解答这句话如何回答？帮我有逻辑的分析这段话！\n\n🎐AI换背景（最新AI）\n命令：br\n操作：br （人物➕背景照片一起发送）\n换背景的照片和背景照片大小要一致！不然会代码错乱！",
        "photo_id": "AgACAgUAAxkBA84OLmdoQ9JkPSubO9gQC4PmnxTCjtw_AALZwjEbDDNJV5xGJP9MOwybAQADAgADeQADNgQ"
    },
    "vip": {
        "status": {
            "active": "已激活",
            "inactive": "未激活",
            "expired": "已过期"
        },
        "expires_at": "到期时间：{date}"
    },
    "channel": {
        "notification": "加入频道时刻关注TGAI更新进度\n\n第一时间掌握AI新资讯❕❕❕\n\n按钮：TGAI官方频道 - @ChatGPT_TGAI",
        "notification_photo_id": "AgACAgUAAxkBA84PCmdoRAdjkmGcX2s3TodvIh8VmrDPAAIQwjEbaJ5AVwtTfQLeZZfCAQADAgADeQADNgQ"
    },
    "start": {
        "welcome": {
            "message": "欢迎使用全网最强免费\nTGAI-ChatGPT\n使用说明:\nask(a) + 空格 + 提示词 = 提问\nvision(v) 识图（无限使用）\nnet(n) 官网联网模型（无限使用）\ndraw(d) 生照片模型（无限使用）\nsd 换图模型（消耗40算力）\nfc: 换脸模型（消耗20算力）\n4o(o) 为gpt-4o模型（消耗10算力）\ncs: claude-sonnet模型（消耗10算力）\nck: claude-haiku模型（无限使用）\no1: openai最新o1模型(alpha测试版 正在为稳定性努力中)\n\n高级特性, 全网唯一! : \n1. 所有命令均支持识图, 只需发送图片即可(并且支持多图识别)\n2. 支持和官网一样的上下文提问, 只需引用上文即可\n为防止滥用, 每人每天200算力, 约可以使用40次gpt-4o或者5次sd\n\n如算力不够用, 邀请一位用户使用TGAI可以免费获得200算力(24h有效)!",
            "message_photo_id": ""
        }
    },
    "group": {
        "management": {
            "tutorial": "添加群组只需两步：（或点击下方按钮）\n1. 将机器人添加到群组并设置为管理员\n2. 确保您是群组管理员\n3. 如果完成前两步仍然找不到群组，请在群组中执行 /update 命令\n4. 点击群组名称按钮管理您的群组",
            "add_bot": "【点击这里将机器人添加到群组】",
            "prev_page": "◀️上一页",
            "next_page": "下一页▶️",
            "refresh": "🔄刷新",
            "help": "❓帮助",
            "group_info": "📑 当前选择的群组：\n群组名称：{title}\n群组ID：{chat_id}\n\n请选择操作：",
            "delete_binding": "❌ 解除群组绑定",
            "back_to_list": "⬅️ 返回列表",
            "unbind_success": "群组解绑成功！",
            "unbind_failed": "解绑失败，请稍后重试",
            "group_not_found": "未找到群组信息"
        },
        "help": {
            "title": "帮助中心：",
            "content": "1. 如果我不小心解绑了群组怎么办？\n答：将机器人从群组移除后重新邀请即可。\n\n2. 如何正确添加机器人到群组？\n答：点击【点击这里将机器人添加到群组】按钮，选择目标群组。\n\n3. 为什么我看不到我的群组？\n答：请确保：\n   - 机器人被设置为群组管理员\n   - 您是群组管理员\n   - 您已点击刷新按钮",
            "back": "返回"
        },
        "binding": {
            "success": "感谢将我添加到群组！\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}",
            "failed": "绑定失败！邀请机器人时请将其设置为管理员以验证您的群组管理员身份。请将机器人移除后重新邀请到群组。\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}",
            "success_user": "感谢将我添加到群组！\n群组ID：{chat_id}\n群组名称：{title}\n绑定用户：{user_id}\n绑定用户名：@{username}"
        }
    },
    "chat_ads": {
        "commands": {
            "remove_chat_ads": "去广告",
            "generate_remove_ads_code": "生成去广告"
        },
        "invalid_params": "参数无效。请检查格式。\n正确格式：{command} <验证码>",
        "invalid_code": "验证码无效或已使用！请检查验证码是否正确。\n正确格式：{command} <验证码>",
        "remove_success": "广告移除成功",
        "remove_failed": "移除广告失败。错误：{error}",
        "generate_success": "广告移除码已生成，请查看附件",
        "error": "移除广告失败。请联系管理员。错误：{error}"
    },
    "language": {
        "choose": "请选择您的语言：\n\n当前支持的语言：",
        "changed": "✅ 您的语言已更改为中文",
        "error": "更改语言失败，请稍后重试",
        "current": "当前语言：中文"
    }
}


def print_banner():
    """打印脚本横幅"""
    print("=" * 60)
    print("🔄 i18n 配置迁移脚本")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()


def backup_existing_files(locales_dir: Path) -> Path:
    """备份现有的locale文件"""
    print("📦 开始备份现有文件...")
    
    backup_dir = locales_dir.parent / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    backup_dir.mkdir(exist_ok=True)
    
    backed_up_files = []
    for locale_file in locales_dir.glob("*.json"):
        backup_file = backup_dir / locale_file.name
        shutil.copy2(locale_file, backup_file)
        backed_up_files.append(locale_file.name)
        print(f"  ✅ 已备份: {locale_file.name} -> {backup_file}")
    
    print(f"📦 备份完成! 备份目录: {backup_dir}")
    print(f"📦 共备份 {len(backed_up_files)} 个文件: {', '.join(backed_up_files)}")
    print()
    
    return backup_dir


def merge_configs(existing_config: Dict[str, Any], old_config: Dict[str, Any]) -> Dict[str, Any]:
    """合并配置，保留现有配置并添加旧配置中的新内容"""
    print("🔄 开始合并配置...")
    
    def deep_merge(target: Dict[str, Any], source: Dict[str, Any], path: str = "") -> None:
        """深度合并两个字典"""
        for key, value in source.items():
            current_path = f"{path}.{key}" if path else key
            
            if key in target:
                if isinstance(target[key], dict) and isinstance(value, dict):
                    # 递归合并嵌套字典
                    deep_merge(target[key], value, current_path)
                else:
                    # 如果值不同，保留现有值但记录差异
                    if target[key] != value:
                        print(f"  ⚠️  保留现有值: {current_path}")
                        print(f"      现有: {repr(target[key])}")
                        print(f"      旧值: {repr(value)}")
            else:
                # 添加新的键值对
                target[key] = value
                print(f"  ➕ 添加新配置: {current_path} = {repr(value)}")
    
    # 创建现有配置的副本
    merged_config = existing_config.copy()
    
    # 深度合并
    deep_merge(merged_config, old_config)
    
    print("🔄 配置合并完成!")
    print()
    
    return merged_config


def update_locale_file(locale_file: Path, old_config: Dict[str, Any]) -> bool:
    """更新单个locale文件"""
    print(f"📝 处理文件: {locale_file.name}")
    
    try:
        # 读取现有配置
        if locale_file.exists():
            with open(locale_file, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
            print(f"  📖 已读取现有配置，包含 {len(existing_config)} 个顶级键")
        else:
            existing_config = {}
            print(f"  📝 文件不存在，将创建新文件")
        
        # 合并配置
        merged_config = merge_configs(existing_config, old_config)
        
        # 写入更新后的配置
        with open(locale_file, 'w', encoding='utf-8') as f:
            json.dump(merged_config, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ 文件更新成功: {locale_file.name}")
        print(f"  📊 最终配置包含 {len(merged_config)} 个顶级键")
        print()
        
        return True
        
    except Exception as e:
        print(f"  ❌ 更新文件失败: {locale_file.name}")
        print(f"  错误: {str(e)}")
        print()
        return False


def validate_json_file(file_path: Path) -> bool:
    """验证JSON文件格式是否正确"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        return True
    except json.JSONDecodeError as e:
        print(f"  ❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 文件读取错误: {e}")
        return False


def show_config_diff(old_config: Dict[str, Any], new_config: Dict[str, Any]) -> None:
    """显示配置差异"""
    print("📋 配置变更摘要:")

    def count_keys(config: Dict[str, Any], prefix: str = "") -> int:
        """递归计算配置中的键数量"""
        count = 0
        for key, value in config.items():
            current_path = f"{prefix}.{key}" if prefix else key
            count += 1
            if isinstance(value, dict):
                count += count_keys(value, current_path)
        return count

    old_keys = count_keys(old_config)
    new_keys = count_keys(new_config)

    print(f"  📊 原有配置项: {old_keys}")
    print(f"  📊 新增配置项: {new_keys - old_keys}")
    print(f"  📊 总配置项: {new_keys}")
    print()


def confirm_migration() -> bool:
    """确认是否继续迁移"""
    print("⚠️  重要提示:")
    print("  - 此操作将修改现有的locale文件")
    print("  - 已自动创建备份，可以随时恢复")
    print("  - 建议在测试环境先验证结果")
    print()

    while True:
        response = input("🤔 是否继续执行迁移? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes/是 或 n/no/否")


def main():
    """主函数"""
    print_banner()

    # 确定工作目录
    script_dir = Path(__file__).parent
    locales_dir = script_dir / "i18n_configs" / "locales"

    print(f"📂 工作目录: {script_dir}")
    print(f"📂 Locales目录: {locales_dir}")
    print()

    # 检查目录是否存在
    if not locales_dir.exists():
        print(f"❌ Locales目录不存在: {locales_dir}")
        print("请确保脚本在正确的data目录下运行")
        sys.exit(1)

    # 获取所有locale文件
    locale_files = list(locales_dir.glob("*.json"))
    if not locale_files:
        print(f"❌ 在 {locales_dir} 中未找到任何JSON文件")
        sys.exit(1)

    print(f"🔍 找到 {len(locale_files)} 个locale文件:")
    for f in locale_files:
        print(f"  - {f.name}")
    print()

    # 验证现有文件格式
    print("🔍 验证现有文件格式...")
    invalid_files = []
    for locale_file in locale_files:
        print(f"  检查: {locale_file.name}")
        if not validate_json_file(locale_file):
            invalid_files.append(locale_file.name)

    if invalid_files:
        print(f"❌ 发现 {len(invalid_files)} 个无效的JSON文件:")
        for f in invalid_files:
            print(f"  - {f}")
        print("请修复这些文件后重新运行脚本")
        sys.exit(1)

    print("✅ 所有文件格式验证通过!")
    print()

    # 显示配置预览
    sample_file = locale_files[0]  # 使用第一个文件作为示例
    with open(sample_file, 'r', encoding='utf-8') as f:
        existing_config = json.load(f)

    merged_sample = merge_configs(existing_config.copy(), OLD_CONFIG)
    show_config_diff(existing_config, merged_sample)

    # 确认是否继续
    if not confirm_migration():
        print("❌ 用户取消操作")
        sys.exit(0)

    # 备份现有文件
    backup_dir = backup_existing_files(locales_dir)

    # 更新每个locale文件
    success_count = 0
    failed_files = []

    print("🚀 开始更新locale文件...")
    print()

    for locale_file in locale_files:
        if update_locale_file(locale_file, OLD_CONFIG):
            success_count += 1
            # 验证更新后的文件
            if validate_json_file(locale_file):
                print(f"  ✅ 文件验证通过: {locale_file.name}")
            else:
                print(f"  ⚠️  文件验证失败: {locale_file.name}")
        else:
            failed_files.append(locale_file.name)

    # 打印总结
    print("=" * 60)
    print("📊 迁移完成总结")
    print("=" * 60)
    print(f"✅ 成功更新: {success_count} 个文件")
    if failed_files:
        print(f"❌ 失败文件: {len(failed_files)} 个")
        for f in failed_files:
            print(f"  - {f}")
    print(f"📦 备份目录: {backup_dir}")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    if failed_files:
        print("⚠️  部分文件更新失败，请检查错误信息并手动处理")
        print(f"💡 可以从备份目录恢复: {backup_dir}")
        sys.exit(1)
    else:
        print("🎉 所有文件迁移成功!")
        print("💡 如需回滚，请使用备份目录中的文件")


def restore_from_backup():
    """从备份恢复文件"""
    print("🔄 备份恢复工具")
    print("=" * 40)

    script_dir = Path(__file__).parent
    locales_dir = script_dir / "i18n_configs" / "locales"

    # 查找备份目录
    backup_dirs = [d for d in script_dir.iterdir() if d.is_dir() and d.name.startswith("backup_")]

    if not backup_dirs:
        print("❌ 未找到任何备份目录")
        return

    # 按时间排序，最新的在前
    backup_dirs.sort(key=lambda x: x.name, reverse=True)

    print("📦 可用的备份:")
    for i, backup_dir in enumerate(backup_dirs, 1):
        backup_files = list(backup_dir.glob("*.json"))
        print(f"  {i}. {backup_dir.name} ({len(backup_files)} 个文件)")

    print("  0. 取消")
    print()

    while True:
        try:
            choice = int(input("请选择要恢复的备份 (输入数字): "))
            if choice == 0:
                print("❌ 用户取消操作")
                return
            elif 1 <= choice <= len(backup_dirs):
                selected_backup = backup_dirs[choice - 1]
                break
            else:
                print(f"请输入 0-{len(backup_dirs)} 之间的数字")
        except ValueError:
            print("请输入有效的数字")

    print(f"📦 选择的备份: {selected_backup.name}")

    # 确认恢复
    response = input("⚠️  确定要恢复此备份吗? 这将覆盖当前文件 (y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 用户取消操作")
        return

    # 执行恢复
    backup_files = list(selected_backup.glob("*.json"))
    success_count = 0

    print("🚀 开始恢复文件...")
    for backup_file in backup_files:
        target_file = locales_dir / backup_file.name
        try:
            shutil.copy2(backup_file, target_file)
            print(f"  ✅ 已恢复: {backup_file.name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ 恢复失败: {backup_file.name} - {e}")

    print(f"🎉 恢复完成! 成功恢复 {success_count}/{len(backup_files)} 个文件")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--restore":
        restore_from_backup()
    else:
        main()
