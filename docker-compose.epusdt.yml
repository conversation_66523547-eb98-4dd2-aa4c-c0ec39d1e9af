version: "3.8"

services:
  # 主数据库 - Bot使用

  # EPUSDT数据库
  epusdt-mysql:
    image: mysql:8.2.0
    container_name: epusdt-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: epusdt_password_123
      MYSQL_DATABASE: epusdt
      MYSQL_USER: epusdt
      MYSQL_PASSWORD: epusdt_password_123
    ports:
      - "3308:3306"
    volumes:
      - epusdt_mysql_data:/var/lib/mysql
      - ./epusdt/sql/init.sql:/docker-entrypoint-initdb.d/init.sql

  # Redis for EPUSDT
  redis:
    image: redis:7-alpine
    container_name: epusdt-redis
    restart: unless-stopped
    ports:
      - "6379:6379"

  # EPUSDT支付服务
  epusdt:
    image: stilleshan/epusdt
    container_name: epusdt
    restart: unless-stopped
    depends_on:
      - epusdt-mysql
      - redis
    ports:
      - "8001:8000"
    volumes:
      - ./epusdt/config/.env:/app/.env
      - ./epusdt/logs:/app/logs
      - ./epusdt/runtime:/app/runtime
    environment:
      - TZ=Asia/Shanghai

volumes:
  epusdt_mysql_data:
