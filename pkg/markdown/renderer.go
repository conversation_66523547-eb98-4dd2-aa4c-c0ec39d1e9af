package markdown

import (
	"bytes"
	"errors"
	"sync"

	"github.com/yuin/goldmark"
	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/extension"
	"github.com/yuin/goldmark/renderer"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"
)

// Renderer Telegram专用的Markdown渲染器
type Renderer struct {
	md   goldmark.Markdown
	pool *sync.Pool
}

// NewRenderer 创建新的Markdown渲染器实例
func NewRenderer() *Renderer {
	return &Renderer{
		md: goldmark.New(
			goldmark.WithExtensions(extension.Linkify, extension.Table, extension.Strikethrough),
			goldmark.WithRenderer(NewTelegramRenderer()),
		),
		pool: &sync.Pool{
			New: func() interface{} {
				return &bytes.Buffer{}
			},
		},
	}
}

// ToHTML 将Markdown转换为Telegram HTML格式
func (mr *Renderer) ToHTML(src []byte) (string, error) {
	buf := mr.pool.Get().(*bytes.Buffer)
	buf.Reset()
	defer mr.pool.Put(buf)

	err := mr.md.Convert(src, buf)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

// TelegramRenderer Telegram专用的AST渲染器
type TelegramRenderer struct{}

// NewTelegramRenderer 创建Telegram渲染器
func NewTelegramRenderer() renderer.Renderer {
	return renderer.NewRenderer(renderer.WithNodeRenderers(util.Prioritized(&TelegramRenderer{}, 0)))
}

// RegisterFuncs 注册渲染函数
func (r *TelegramRenderer) RegisterFuncs(reg renderer.NodeRendererFuncRegisterer) {
	// blocks
	reg.Register(ast.KindDocument, r.renderDocument)
	reg.Register(ast.KindHeading, r.renderHeading)
	reg.Register(ast.KindBlockquote, r.renderBlockquote)
	reg.Register(ast.KindCodeBlock, r.renderCodeBlock)
	reg.Register(ast.KindFencedCodeBlock, r.renderFencedCodeBlock)
	reg.Register(ast.KindHTMLBlock, r.renderHTMLBlock)
	reg.Register(ast.KindList, r.renderList)
	reg.Register(ast.KindListItem, r.renderListItem)
	reg.Register(ast.KindParagraph, r.renderParagraph)
	reg.Register(ast.KindTextBlock, r.renderTextBlock)
	reg.Register(ast.KindThematicBreak, r.renderThematicBreak)

	// table elements - using correct ast.NodeKind values
	reg.Register(ast.NodeKind(28), r.renderTable)       // Table
	reg.Register(ast.NodeKind(30), r.renderTableHeader) // TableHeader
	reg.Register(ast.NodeKind(29), r.renderTableRow)    // TableRow
	reg.Register(ast.NodeKind(31), r.renderTableCell)   // TableCell

	// inlines
	reg.Register(ast.KindAutoLink, r.renderAutoLink)
	reg.Register(ast.KindCodeSpan, r.renderCodeSpan)
	reg.Register(ast.KindEmphasis, r.renderEmphasis)
	reg.Register(ast.KindImage, r.renderImage)
	reg.Register(ast.KindLink, r.renderLink)
	reg.Register(ast.KindRawHTML, r.renderRawHTML)
	reg.Register(ast.KindText, r.renderText)
	reg.Register(ast.KindString, r.renderString)
}

// Telegram HTML 标签常量
const (
	headingEntry = "<b>"
	headingExit  = "</b>\n"
	listMarkup   = "- "
	hrMarkup     = "━━━━━━━━━━━━━━━━\n"
	
	// HTML 标签
	codeStart    = "<code>"
	codeEnd      = "</code>"
	preStart     = "<pre>"
	preEnd       = "</pre>"
	preCodeStart = "<pre><code>"
	preCodeEnd   = "</code></pre>\n"
	linkStart    = "<a href=\""
	linkMid      = "\">"
	linkEnd      = "</a>"
)

// 错误定义
var (
	errNoAttributesAllowed = errors.New("telegram does not allow attributes here")
	errTagNotAllowed       = errors.New("telegram does not allow such tag")
)

func (r *TelegramRenderer) writeLines(w util.BufWriter, source []byte, n ast.Node) {
	l := n.Lines().Len()
	for i := 0; i < l; i++ {
		line := n.Lines().At(i)
		html.DefaultWriter.RawWrite(w, line.Value(source))
	}
}

func (r *TelegramRenderer) renderDocument(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	// nothing to do
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderHeading(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.Heading)
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}
		_, _ = w.WriteString(headingEntry)
	} else {
		_, _ = w.WriteString(headingExit)
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderBlockquote(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}

		// TODO needs testing
		_, _ = w.WriteString("&gt; ")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderCodeBlock(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		_, _ = w.WriteString(preCodeStart)
		r.writeLines(w, source, n)
	} else {
		_, _ = w.WriteString(preCodeEnd)
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderFencedCodeBlock(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.FencedCodeBlock)
	if entering {
		_, _ = w.WriteString(preCodeStart[:10]) // "<pre><code"
		language := n.Language(source)
		if language != nil {
			_, _ = w.WriteString(" class=\"language-")
			html.DefaultWriter.Write(w, language)
			_, _ = w.WriteString("\"")
		}
		_ = w.WriteByte('>')
		r.writeLines(w, source, n)
	} else {
		_, _ = w.WriteString(preCodeEnd)
	}
	return ast.WalkContinue, nil
}
