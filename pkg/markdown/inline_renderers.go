package markdown

import (
	"bytes"

	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/renderer/html"
	"github.com/yuin/goldmark/util"
)

// 内联元素渲染函数

func (r *TelegramRenderer) renderAutoLink(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.AutoLink)
	if !entering {
		return ast.WalkContinue, nil
	}

	if n.Attributes() != nil {
		return ast.WalkStop, errNoAttributesAllowed
	}

	_, _ = w.WriteString(linkStart)
	url := n.URL(source)
	label := n.Label(source)
	if n.AutoLinkType == ast.AutoLinkEmail && !bytes.HasPrefix(bytes.ToLower(url), []byte("mailto:")) {
		_, _ = w.WriteString("mailto:")
	}
	_, _ = w.Write(util.EscapeHTML(util.URLEscape(url, false)))
	_, _ = w.WriteString(linkMid)

	_, _ = w.Write(util.EscapeHTML(label))
	_, _ = w.WriteString(linkEnd)

	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderCodeSpan(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}

		_, _ = w.WriteString(codeStart)

		for c := n.FirstChild(); c != nil; c = c.NextSibling() {
			segment := c.(*ast.Text).Segment
			value := segment.Value(source)
			if bytes.HasSuffix(value, []byte("\n")) {
				html.DefaultWriter.RawWrite(w, value[:len(value)-1])
				html.DefaultWriter.RawWrite(w, []byte(" "))
			} else {
				html.DefaultWriter.RawWrite(w, value)
			}
		}
		return ast.WalkSkipChildren, nil
	}
	_, _ = w.WriteString(codeEnd)
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderEmphasis(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.Emphasis)
	if n.Attributes() != nil {
		return ast.WalkStop, errNoAttributesAllowed
	}

	tag := "i"
	if n.Level == 2 {
		tag = "b"
	}
	if entering {
		_ = w.WriteByte('<')
		_, _ = w.WriteString(tag)
		_ = w.WriteByte('>')
	} else {
		_, _ = w.WriteString("</")
		_, _ = w.WriteString(tag)
		_ = w.WriteByte('>')
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderLink(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.Link)
	if n.Title != nil {
		return ast.WalkStop, errNoAttributesAllowed
	}
	if n.Attributes() != nil {
		return ast.WalkStop, errNoAttributesAllowed
	}
	if entering {
		_, _ = w.WriteString(linkStart)
		if !IsDangerousURL(n.Destination) {
			_, _ = w.Write(util.EscapeHTML(util.URLEscape(n.Destination, true)))
		}
		_, _ = w.WriteString(linkMid)
	} else {
		_, _ = w.WriteString(linkEnd)
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderImage(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*ast.Image)

	// 原样显示图片的 markdown 语法，包含 alt text
	_, _ = w.WriteString("![")
	// 渲染 alt text (子节点)
	for c := n.FirstChild(); c != nil; c = c.NextSibling() {
		if text, ok := c.(*ast.Text); ok {
			_, _ = w.Write(util.EscapeHTML(text.Segment.Value(source)))
		}
	}
	_, _ = w.WriteString("](")
	_, _ = w.Write(util.EscapeHTML(util.URLEscape(n.Destination, true)))
	if n.Title != nil {
		_, _ = w.WriteString(" \"")
		_, _ = w.Write(util.EscapeHTML(n.Title))
		_, _ = w.WriteString("\"")
	}
	_, _ = w.WriteString(")")

	return ast.WalkSkipChildren, nil
}

func (r *TelegramRenderer) renderRawHTML(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*ast.RawHTML)
	segment := n.Segments.At(0)
	htmlContent := string(segment.Value(source))

	// 智能处理 AI 可能发送的 HTML 标签
	switch {
	case htmlContent == preCodeStart[:11] || bytes.HasPrefix(segment.Value(source), []byte(preCodeStart[:10])):
		// AI 发送的代码块开始标签 -> 转换为 Telegram 支持的格式
		_, _ = w.WriteString(preCodeStart)
	case htmlContent == preCodeEnd[:12]:
		// AI 发送的代码块结束标签 -> 转换为 Telegram 支持的格式
		_, _ = w.WriteString(preCodeEnd)
	case htmlContent == codeStart:
		// AI 发送的内联代码开始标签
		_, _ = w.WriteString(codeStart)
	case htmlContent == codeEnd:
		// AI 发送的内联代码结束标签
		_, _ = w.WriteString(codeEnd)
	case htmlContent == preStart:
		// AI 发送的预格式化文本开始标签
		_, _ = w.WriteString(preStart)
	case htmlContent == preEnd:
		// AI 发送的预格式化文本结束标签
		_, _ = w.WriteString(preEnd)
	default:
		// 其他 HTML 标签转义为纯文本
		html.DefaultWriter.Write(w, segment.Value(source))
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderText(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*ast.Text)
	segment := n.Segment
	if n.IsRaw() {
		html.DefaultWriter.RawWrite(w, segment.Value(source))
	} else {
		html.DefaultWriter.Write(w, segment.Value(source))
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderString(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	n := node.(*ast.String)
	if n.IsCode() {
		_, _ = w.Write(n.Value)
	} else {
		if n.IsRaw() {
			html.DefaultWriter.RawWrite(w, n.Value)
		} else {
			html.DefaultWriter.Write(w, n.Value)
		}
	}
	return ast.WalkContinue, nil
}

// URL 安全检查的字节切片常量
var (
	bDataImage = []byte("data:image/")
	bPng       = []byte("png;")
	bGif       = []byte("gif;")
	bJpeg      = []byte("jpeg;")
	bWebp      = []byte("webp;")
	bSvg       = []byte("svg+xml;")
	bJs        = []byte("javascript:")
	bVb        = []byte("vbscript:")
	bFile      = []byte("file:")
	bData      = []byte("data:")
)

// IsDangerousURL returns true if the given url seems a potentially dangerous url,
// otherwise false.
func IsDangerousURL(url []byte) bool {
	if bytes.HasPrefix(url, bDataImage) && len(url) >= 11 {
		v := url[11:]
		if bytes.HasPrefix(v, bPng) || bytes.HasPrefix(v, bGif) ||
			bytes.HasPrefix(v, bJpeg) || bytes.HasPrefix(v, bWebp) ||
			bytes.HasPrefix(v, bSvg) {
			return false
		}
		return true
	}
	return bytes.HasPrefix(url, bJs) || bytes.HasPrefix(url, bVb) ||
		bytes.HasPrefix(url, bFile) || bytes.HasPrefix(url, bData)
}
