package markdown

import (
	"bytes"

	"github.com/yuin/goldmark/ast"
	"github.com/yuin/goldmark/util"
)

// 块级元素渲染函数

func (r *TelegramRenderer) renderHTMLBlock(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		// 智能处理 AI 发送的 HTML 块
		lines := node.Lines()
		if lines.Len() > 0 {
			// 获取第一行内容来判断是否是代码块
			firstLine := lines.At(0)

			// 检查是否是 AI 发送的代码块
			if bytes.Contains(firstLine.Value(source), []byte(preCodeStart[:10])) {
				// 这是 AI 发送的代码块，转换为 Telegram 支持的格式
				_, _ = w.WriteString(preCodeStart)

				// 处理代码内容（跳过第一行的 <pre><code> 标签）
				for i := 0; i < lines.Len(); i++ {
					line := lines.At(i)
					lineContent := line.Value(source)

					// 跳过开始和结束标签行
					if bytes.Contains(lineContent, []byte(preCodeStart[:10])) ||
						bytes.Contains(lineContent, []byte(preCodeEnd[:12])) {
						continue
					}

					// 输出代码内容（不转义）
					_, _ = w.Write(lineContent)
				}

				_, _ = w.WriteString(preCodeEnd)
			} else {
				// 其他 HTML 块转义为纯文本
				r.writeLines(w, source, node)
			}
		}
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderList(w util.BufWriter, source []byte, node ast.Node, entering bool) (ast.WalkStatus, error) {
	n := node.(*ast.List)
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}
	} else {
		// extra line break after list
		// _, _ = w.WriteString("\n")
	}
	// markup is within list items
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderListItem(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}

		_, _ = w.WriteString(listMarkup)

		// TODO indent all children?
	} else {
		_, _ = w.WriteString("\n")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderParagraph(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		if n.Attributes() != nil {
			return ast.WalkStop, errNoAttributesAllowed
		}
	} else {
		// insert line break after paragraph
		_, _ = w.WriteString("\n\n")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderTextBlock(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		if _, ok := n.NextSibling().(ast.Node); ok && n.FirstChild() != nil {
			_ = w.WriteByte('\n')
		}
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderThematicBreak(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		return ast.WalkContinue, nil
	}
	if n.Attributes() != nil {
		return ast.WalkStop, errNoAttributesAllowed
	}
	// 使用简单的分割线，因为 Telegram 不支持 <hr> 标签
	_, _ = w.WriteString(hrMarkup)

	return ast.WalkContinue, nil
}

// Table rendering functions for Telegram
func (r *TelegramRenderer) renderTable(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		_, _ = w.WriteString("<pre>")
		// 添加表格顶部边框
		_, _ = w.WriteString("┌─────────────────────────────────────┐\n")
	} else {
		// 添加表格底部边框
		_, _ = w.WriteString("└─────────────────────────────────────┘")
		_, _ = w.WriteString("</pre>\n")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderTableHeader(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		_, _ = w.WriteString("│ ")
	} else {
		// 表头下方添加分隔线
		_, _ = w.WriteString(" │\n├─────────────────────────────────────┤\n")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderTableRow(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if entering {
		_, _ = w.WriteString("│ ")
	} else {
		_, _ = w.WriteString(" │\n")
	}
	return ast.WalkContinue, nil
}

func (r *TelegramRenderer) renderTableCell(w util.BufWriter, source []byte, n ast.Node, entering bool) (ast.WalkStatus, error) {
	if !entering {
		// 在单元格之间添加分隔符
		if n.NextSibling() != nil {
			_, _ = w.WriteString(" │ ")
		}
	}
	return ast.WalkContinue, nil
}
