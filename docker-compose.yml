version: "3.8"

services:
  # 主数据库 - Bot使用
  db:
    image: mysql:8.2.0
    container_name: tg-bot-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ShellAPI!akl7777777
      MYSQL_DATABASE: test2
      MYSQL_USER: bot_user
      MYSQL_PASSWORD: ShellAPI!akl7777777
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - bot-network

  # Telegram Bot
  bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tg-bot
    restart: unless-stopped
    depends_on:
      - db
    ports:
      - "8080:8080"
    volumes:
      - ./data/config/config.docker.json:/app/data/config/config.json:ro
      - ./data:/app/data
    networks:
      - bot-network
    environment:
      - TZ=Asia/Shanghai

volumes:
  mysql_data:

networks:
  bot-network:
    driver: bridge
