-- EPUSDT 数据库初始化脚本
USE epusdt;

-- auto-generated definition
create table orders (
    id int auto_increment primary key,
    trade_id varchar(32) not null comment 'epusdt订单号',
    order_id varchar(32) not null comment '客户交易id',
    block_transaction_id varchar(128) null comment '区块唯一编号',
    actual_amount decimal(19, 4) not null comment '订单实际需要支付的金额，保留4位小数',
    amount decimal(19, 4) not null comment '订单金额，保留4位小数',
    token varchar(50) not null comment '所属钱包地址',
    status int default 1 not null comment '1：等待支付，2：支付成功，3：已过期',
    notify_url varchar(128) not null comment '异步回调地址',
    redirect_url varchar(128) null comment '同步回调地址',
    callback_num int default 0 null comment '回调次数',
    callback_confirm int default 2 null comment '回调是否已确认？ 1是 2否',
    created_at timestamp null,
    updated_at timestamp null,
    deleted_at timestamp null,
    constraint orders_order_id_uindex unique (order_id),
    constraint orders_trade_id_uindex unique (trade_id)
);

create index orders_block_transaction_id_index on orders (block_transaction_id);

-- auto-generated definition
create table wallet_address (
    id int auto_increment primary key,
    token varchar(50) not null comment '钱包token',
    status int default 1 not null comment '1:启用 2:禁用',
    created_at timestamp null,
    updated_at timestamp null,
    deleted_at timestamp null
) comment '钱包表';

create index wallet_address_token_index on wallet_address (token);
