package handler

import (
	"telegram-chatbot-go/internal/service/affiliate"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

type EventHandler struct {
	svcCtx *svc.ServiceContext
}

func NewEventHandler(svcCtx *svc.ServiceContext) *EventHandler {
	return &EventHandler{
		svcCtx: svcCtx,
	}
}

func (h *EventHandler) HandleBotAddedToGroup(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)
	return affiliateService.HandleBotAddedToGroup(c)
}
