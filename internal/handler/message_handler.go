package handler

import (
	"strings"
	"telegram-chatbot-go/internal/router"
	"telegram-chatbot-go/internal/svc"

	tele "gopkg.in/telebot.v4"
)

// MessageHandler 消息处理器 - 专注于消息分发
type MessageHandler struct {
	svcCtx *svc.ServiceContext
	router *router.Router
}

// NewMessageHandler 创建消息处理器
func NewMessageHandler(svcCtx *svc.ServiceContext, r *router.Router) *MessageHandler {
	return &MessageHandler{
		svcCtx: svcCtx,
		router: r,
	}
}

// HandleTextMessage 处理文本消息
func (h *MessageHandler) HandleTextMessage(c tele.Context) error {
	text := strings.TrimSpace(c.Text())
	if text == "" {
		return nil
	}

	// 直接调用路由器处理，内部已包含完整的匹配和回退逻辑
	return h.router.HandleTextMessage(c)
}

// HandleMediaMessage 处理媒体消息
func (h *MessageHandler) HandleMediaMessage(c tele.Context) error {
	caption := strings.TrimSpace(c.Message().Caption)
	if caption == "" {
		return nil
	}

	// 直接调用路由器处理，内部已包含完整的匹配和回退逻辑
	return h.router.HandleMediaMessage(c)
}
