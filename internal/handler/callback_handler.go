package handler

import (
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/service/affiliate"
	"telegram-chatbot-go/internal/service/user"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// CallbackHandler 回调处理器 - 专注于回调分发
type CallbackHandler struct {
	svcCtx *svc.ServiceContext
}

// NewCallbackHandler 创建回调处理器
func NewCallbackHandler(svcCtx *svc.ServiceContext) *CallbackHandler {
	return &CallbackHandler{
		svcCtx: svcCtx,
	}
}

// HandleLanguageCallback 处理语言选择回调（公有方法，用于新路由系统）
func (h *CallbackHandler) HandleLanguageCallback(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)

	// 提取语言代码
	langCode := strings.TrimPrefix(c.Callback().Unique, "lang_")

	// 从回调数据中提取实际的语言代码，格式: userID|langCode
	callback := c.Callback()
	if callback != nil && callback.Data != "" {
		parts := strings.Split(callback.Data, "|")
		if len(parts) >= 2 {
			// 使用回调数据中的语言代码，跳过第一段用户ID
			langCode = parts[1]
		}
	}

	// 调用系统服务处理语言切换
	return userService.HandleLanguageCallback(c, langCode)
}

// HandleGroupPageCallback 处理群组分页回调
func (h *CallbackHandler) HandleGroupPageCallback(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)

	// 从回调数据中提取页码，格式: userID|page 或 userID|page|refresh
	callback := c.Callback()
	if callback == nil {
		return affiliateService.ShowGroups(c, 1)
	}

	// 解析页码和刷新标识，跳过第一段用户ID
	parts := strings.Split(callback.Data, "|")
	page := 1
	isRefresh := false

	if len(parts) >= 2 {
		if p, err := strconv.Atoi(parts[1]); err == nil {
			page = p
		}
	}

	// 检查是否是刷新操作
	if len(parts) >= 3 && parts[2] == "refresh" {
		isRefresh = true
	}

	if isRefresh {
		return affiliateService.ShowGroupsRefresh(c, page)
	} else {
		return affiliateService.ShowGroups(c, page)
	}
}

// HandleGroupSelectCallback 处理群组选择回调
func (h *CallbackHandler) HandleGroupSelectCallback(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)

	// 从回调数据中提取群组ID，格式: userID|groupID
	callback := c.Callback()
	if callback == nil {
		return nil
	}

	parts := strings.Split(callback.Data, "|")
	if len(parts) < 2 {
		return nil
	}

	// 跳过第一段用户ID，使用第二段群组ID
	return affiliateService.HandleGroupSelect(c, parts[1])
}

// HandleGroupUnbindCallback 处理群组解绑回调
func (h *CallbackHandler) HandleGroupUnbindCallback(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)

	// 从回调数据中提取群组ID，格式: userID|groupID
	callback := c.Callback()
	if callback == nil {
		return nil
	}

	parts := strings.Split(callback.Data, "|")
	if len(parts) < 2 {
		return nil
	}

	// 跳过第一段用户ID，使用第二段群组ID
	return affiliateService.HandleGroupUnbind(c, parts[1])
}

// HandleGroupHelpCallback 处理群组帮助回调
func (h *CallbackHandler) HandleGroupHelpCallback(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)

	return affiliateService.ShowHelp(c)
}
