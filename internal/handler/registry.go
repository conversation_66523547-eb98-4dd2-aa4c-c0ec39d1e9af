package handler

import (
	"log"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/middleware"
	"telegram-chatbot-go/internal/router"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// registerAICommands 动态注册AI命令
func (h *Handler) registerAICommands(group *router.Group) {
	log.Printf("开始动态注册AI命令...")

	// 解析所有AI命令
	allCommands := utils.ParseAICommands(h.svcCtx.Config.AICommands)

	if len(allCommands) > 0 {
		// 批量注册到统一的AI命令处理器
		group.AddTextCommand(allCommands, h.commandHandler.AIHandler)
		log.Printf("已注册 %d 个AI命令: %v", len(allCommands), allCommands)
	} else {
		log.Printf("没有找到启用的AI命令")
	}
}

// registerMessages 注册消息路由
func (h *Handler) registerMessages() {
	log.Printf("开始注册消息处理器...")

	// 文本消息 - 统一处理命令和普通文本
	h.AddTextMessage(h.messageHandler.HandleTextMessage)
	h.AddMediaMessage(h.messageHandler.HandleMediaMessage)

	log.Printf("消息处理器注册完成")
}

// registerCallbacks 注册回调路由
func (h *Handler) registerCallbacks() {
	log.Printf("开始注册回调处理器...")

	// 基础回调组 - 所有用户都可以使用
	basicCallbackGroup := h.Group().Use(middleware.CallbackAuthMiddleware)
	{
		// 语言选择回调
		basicCallbackGroup.AddCallback("lang_*", h.callbackHandler.HandleLanguageCallback)

		// 群组管理回调
		basicCallbackGroup.AddCallback("group_page*", h.callbackHandler.HandleGroupPageCallback)
		basicCallbackGroup.AddCallback("group_select*", h.callbackHandler.HandleGroupSelectCallback)
		basicCallbackGroup.AddCallback("group_unbind*", h.callbackHandler.HandleGroupUnbindCallback)
		basicCallbackGroup.AddCallback("group_help*", h.callbackHandler.HandleGroupHelpCallback)

	}

	log.Printf("回调处理器注册完成")
}

// registerEvents 注册事件处理器
func (h *Handler) registerEvents() {
	log.Printf("开始注册群组事件处理器...")

	// 注册被加入群组事件
	h.svcCtx.Bot.Handle(tele.OnAddedToGroup, h.eventHandler.HandleBotAddedToGroup)

	log.Printf("群组事件处理器注册完成")
}

// registerCommands 注册命令路由
func (h *Handler) registerCommands() {
	// 代表使用了机器人，走AIUsedMiddleware中间件
	basicGroup := h.Group().Use(middleware.AIUsedMiddleware)
	if !h.svcCtx.Config.Operation.EnableEavesdropMode {
		basicGroup = basicGroup.Use(middleware.MessageRecordMiddleware)
	}
	{
		// 普通命令
		basicGroup.AddTextCommand([]string{"start", "开始"}, h.commandHandler.Start)
		basicGroup.AddTextCommand([]string{"sk"}, h.commandHandler.Sk)
		basicGroup.AddTextCommand([]string{"get_photo_id"}, h.commandHandler.GetPhotoID)
		basicGroup.AddTextCommand([]string{"info"}, h.commandHandler.Info)
		basicGroup.AddTextCommand([]string{"language", "lang"}, h.commandHandler.Language)
		basicGroup.AddTextCommand([]string{"instructions", "help"}, h.commandHandler.Instructions)
		basicGroup.AddTextCommand([]string{"sign_in", "sg", "签到", "signin"}, h.commandHandler.SignIn)
		basicGroup.AddTextCommand([]string{"packages", "package", "套餐"}, h.commandHandler.Packages)
		basicGroup.AddTextCommand([]string{"buy", "购买"}, h.commandHandler.Buy)

		// 动态注册AI命令
		h.registerAICommands(basicGroup)

		// 管理员命令组
		adminGroup := basicGroup.Group().Use(middleware.AdminOnly(h.svcCtx.Config.Admin.UserIDs...))
		{
			// 兑换码管理命令
			adminGroup.AddTextCommand([]string{"admin_create_code"}, h.commandHandler.AdminCreateCode)
			adminGroup.AddTextCommand([]string{"admin_create_many"}, h.commandHandler.AdminCreateMany)
			adminGroup.AddTextCommand([]string{"admin_delete_code"}, h.commandHandler.AdminDeleteCode)
			adminGroup.AddTextCommand([]string{"admin_query_code"}, h.commandHandler.AdminQueryCode)

			// 套餐管理命令
			adminGroup.AddTextCommand([]string{"admin_create_package"}, h.commandHandler.AdminCreatePackage)
			adminGroup.AddTextCommand([]string{"admin_list_packages"}, h.commandHandler.AdminListPackages)
			adminGroup.AddTextCommand([]string{"admin_delete_package"}, h.commandHandler.AdminDeletePackage)
			adminGroup.AddTextCommand([]string{"admin_toggle_package"}, h.commandHandler.AdminTogglePackage)

		}

		// 菜单命令（多语言）
		basicGroup.AddTextCommand(h.svcCtx.I18n.GetI18nCommands(i18n.Keys.Keyboard.Language),
			h.commandHandler.Language)
		basicGroup.AddTextCommand(h.svcCtx.I18n.GetI18nCommands(i18n.Keys.Keyboard.Instructions),
			h.commandHandler.Instructions)
		basicGroup.AddTextCommand(h.svcCtx.I18n.GetI18nCommands(i18n.Keys.Keyboard.ChannelNotifications),
			h.commandHandler.ChannelNotification)
		basicGroup.AddTextCommand(h.svcCtx.I18n.GetI18nCommands(i18n.Keys.Keyboard.GetInviteCode),
			h.commandHandler.GetInviteCode)
		basicGroup.AddTextCommand(h.svcCtx.I18n.GetI18nCommands(i18n.Keys.Keyboard.GroupManagement),
			h.commandHandler.GroupManagement)
	}
}
