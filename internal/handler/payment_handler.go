package handler

import (
	"encoding/json"
	"net/http"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/internal/service/pay"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	svcCtx *svc.ServiceContext
}

// NewPaymentHandler 创建支付处理器
func NewPaymentHandler(svcCtx *svc.ServiceContext) *PaymentHandler {
	return &PaymentHandler{
		svcCtx: svcCtx,
	}
}

// HandleCallback 处理EPUSDT支付回调
func (h *PaymentHandler) HandleCallback(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var callback request.PaymentCallback
	if err := json.NewDecoder(r.Body).Decode(&callback); err != nil {
		logx.Errorf("❌ 解析回调数据失败: %v\n", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// 创建支付服务
	ctx := utils.GetTimeoutContext(nil)
	payService := pay.NewPayService(h.svcCtx, ctx)

	// 处理回调
	if err := payService.HandlePaymentCallback(&callback); err != nil {
		logx.Errorf("❌ 处理支付回调失败: %v\n", err)
		http.Error(w, "Callback processing failed", http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}
