package handler

import (
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/service/affiliate"
	"telegram-chatbot-go/internal/service/ai"
	"telegram-chatbot-go/internal/service/pay"
	"telegram-chatbot-go/internal/service/system"
	"telegram-chatbot-go/internal/service/user"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// CommandHandler 命令处理器 - 专注于命令分发
type CommandHandler struct {
	svcCtx *svc.ServiceContext
}

// NewCommandHandler 创建命令处理器
func NewCommandHandler(svcCtx *svc.ServiceContext) *CommandHandler {
	return &CommandHandler{
		svcCtx: svcCtx,
	}
}

// Start 处理开始命令
func (h *CommandHandler) Start(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	systemService := system.NewSystemService(h.svcCtx, ctx)
	return systemService.Start(c)
}

// AICommand 统一的AI命令处理器入口，根据type分发
func (h *CommandHandler) AIHandler(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	aiService := ai.NewAIService(h.svcCtx, ctx)
	// 获取命令文本
	text := c.Text()
	if text == "" && c.Message() != nil && c.Message().Caption != "" {
		text = c.Message().Caption
	}

	// 获取对应的AI命令配置
	commandConfig, _ := utils.GetAICommandConfig(text, h.svcCtx.Config.AICommands)
	if commandConfig == nil {
		// 如果没有找到配置，可能是空字符串匹配（默认聊天）
		// 尝试使用ask作为默认处理
		if defaultConfig, exists := h.svcCtx.Config.AICommands["ask"]; exists && defaultConfig.Enable {
			commandConfig = &defaultConfig
		} else {
			return c.Reply(utils.GetI18nText(c, i18n.Keys.Common.Errors.InvalidCommand))
		}
	}

	// 根据命令类型分发到不同的处理方法
	switch commandConfig.Type {
	case "chat":
		return aiService.AskWithConfig(c, commandConfig)
	case "sd":
		return aiService.SDWithConfig(c, commandConfig)
	case "fc":
		return aiService.FCWithConfig(c, commandConfig)
	case "be":
		return aiService.BEWithConfig(c, commandConfig)
	case "br":
		return aiService.BRWithConfig(c, commandConfig)
	default:
		return c.Reply(utils.GetI18nTextWithArgs(c, i18n.Keys.Common.Errors.CommandFailed, map[string]interface{}{
			"error": "该AI命令类型暂未实现: " + commandConfig.Type,
		}))
	}
}

// Language 处理语言选择命令
func (h *CommandHandler) Language(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.Language(c)
}

// GroupManagement 处理群组管理命令
func (h *CommandHandler) GroupManagement(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	affiliateService := affiliate.NewAffiliateService(h.svcCtx, ctx)
	return affiliateService.GroupManagement(c)
}

// Info 处理用户信息命令
func (h *CommandHandler) Info(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.Info(c)
}

// Instructions 处理使用说明命令
func (h *CommandHandler) Instructions(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	systemService := system.NewSystemService(h.svcCtx, ctx)
	return systemService.Instructions(c)
}

// GetPhotoID 获取图片ID命令
func (h *CommandHandler) GetPhotoID(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	systemService := system.NewSystemService(h.svcCtx, ctx)
	return systemService.GetPhotoID(c)
}

// Sk 处理兑换码命令
func (h *CommandHandler) Sk(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.Sk(c)
}

// AdminCreateCode 管理员创建兑换码命令
func (h *CommandHandler) AdminCreateCode(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.AdminCreateCode(c)
}

// AdminCreateMany 管理员批量创建兑换码命令
func (h *CommandHandler) AdminCreateMany(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.AdminCreateMany(c)
}

// AdminDeleteCode 管理员删除兑换码命令
func (h *CommandHandler) AdminDeleteCode(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.AdminDeleteCode(c)
}

// AdminQueryCode 管理员查询兑换码命令
func (h *CommandHandler) AdminQueryCode(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.AdminQueryCode(c)
}

// GetInviteCode 获取邀请码命令
func (h *CommandHandler) GetInviteCode(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	systemService := system.NewSystemService(h.svcCtx, ctx)
	return systemService.GetInviteCode(c)
}

// ChannelNotification 频道通知命令
func (h *CommandHandler) ChannelNotification(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	systemService := system.NewSystemService(h.svcCtx, ctx)
	return systemService.ChannelNotification(c)
}

func (h *CommandHandler) SignIn(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	userService := user.NewUserService(h.svcCtx, ctx)
	return userService.SignIn(c)
}

// Buy 处理购买套餐命令
func (h *CommandHandler) Buy(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)

	// 解析命令参数
	args := strings.Fields(c.Text())
	if len(args) < 2 {
		return c.Reply("🛒 使用方法: /buy <套餐ID>\n\n例如: /buy 1\n\n💡 使用 /packages 查看可购买的套餐")
	}

	// 解析套餐ID
	packageIDStr := args[1]
	packageID, err := strconv.ParseInt(packageIDStr, 10, 32)
	if err != nil {
		return c.Reply("❌ 套餐ID格式错误，请输入有效的数字")
	}

	return payService.CreatePackagePayment(c, int32(packageID))
}

// Packages 处理查看套餐列表命令
func (h *CommandHandler) Packages(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)
	return payService.ListPackages(c)
}

// AdminCreatePackage 管理员创建套餐命令
func (h *CommandHandler) AdminCreatePackage(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)
	return payService.AdminCreatePackage(c)
}

// AdminListPackages 管理员查看套餐列表命令
func (h *CommandHandler) AdminListPackages(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)
	return payService.AdminListPackages(c)
}

// AdminDeletePackage 管理员删除套餐命令
func (h *CommandHandler) AdminDeletePackage(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)
	return payService.AdminDeletePackage(c)
}

// AdminTogglePackage 管理员启用/禁用套餐命令
func (h *CommandHandler) AdminTogglePackage(c tele.Context) error {
	ctx := utils.GetTimeoutContext(c)
	payService := pay.NewPayService(h.svcCtx, ctx)
	return payService.AdminTogglePackage(c)
}
