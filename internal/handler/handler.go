package handler

import (
	"telegram-chatbot-go/internal/router"
	"telegram-chatbot-go/internal/svc"

	tele "gopkg.in/telebot.v4"
)

// Handler 主处理器结构体 - 专注于业务协调
type Handler struct {
	router          *router.Router
	svcCtx          *svc.ServiceContext
	commandHandler  *CommandHandler
	callbackHandler *CallbackHandler
	messageHandler  *MessageHandler
	eventHandler    *EventHandler
}

// NewHandler 创建新的处理器
func NewHandler(b *tele.Bot, svcCtx *svc.ServiceContext) *Handler {
	r := router.NewRouter(b)

	h := &Handler{
		router:          r,
		svcCtx:          svcCtx,
		commandHandler:  NewCommandHandler(svcCtx),
		callbackHandler: NewCallbackHandler(svcCtx),
		messageHandler:  NewMessageHandler(svcCtx, r),
		eventHandler:    NewEventHandler(svcCtx),
	}
	return h
}

// === Router 方法别名 - 让 Handler API 更简洁 ===
func (h *Handler) Group() *router.Group {
	return h.router.Group()
}

func (h *Handler) AddCommand(commands []string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	h.router.AddCommand(commands, handler, m...)
}

// AddTextCommand 添加命令和文本路由别名
func (h *Handler) AddTextCommand(commands []string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	h.router.AddTextCommand(commands, handler, m...)
}

// AddTextMessage 添加文本消息路由别名
func (h *Handler) AddTextMessage(handler func(tele.Context) error) {
	h.router.AddTextMessage(handler)
}

// AddMediaMessage 添加媒体消息路由别名
func (h *Handler) AddMediaMessage(handler func(tele.Context) error) {
	h.router.AddMediaMessage(handler)
}

// AddCallback 添加回调路由别名
func (h *Handler) AddCallback(pattern string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	h.router.AddCallback(pattern, handler, m...)
}

// AddCallbackGlobal 添加全局回调处理器别名
func (h *Handler) AddCallbackGlobal(handler func(tele.Context) error) {
	h.router.AddCallbackGlobal(handler)
}

func (h *Handler) Init() {
	// 注册所有路由 - 调用分散的注册函数
	h.registerCommands()
	// 要在注册命令后才能注册消息和回调
	h.registerMessages()
	// 注册回调
	h.registerCallbacks()
	// 注册群组事件
	h.registerEvents()
}
