package keyboard

import (
	"fmt"

	tele "gopkg.in/telebot.v4"
)

// 语言信息结构
type LanguageInfo struct {
	Flag string // 国旗emoji
	Name string // 语言名称
	Code string // 语言代码
}

// 支持的语言列表
var SupportedLanguages = []LanguageInfo{
	{"🇺🇸", "English", "en"},
	{"🇨🇳", "中文", "zh_CN"},
	{"🇯🇵", "日本語", "ja"},
	{"🇰🇷", "한국어", "ko"},
	{"🇷🇺", "Русский", "ru"},
	{"🇪🇸", "Español", "es"},
	{"🇫🇷", "Français", "fr"},
	{"🇩🇪", "Deutsch", "de"},
}

// GetLanguageSelectWithAuth 获取带用户ID鉴权的语言选择键盘
// 约定：.Data 字段格式为 "userID|langCode"，第一段为用户ID用于鉴权
func GetLanguageSelectWithAuth(currentLang string, userID int64) *tele.ReplyMarkup {
	selector := &tele.ReplyMarkup{}

	// 动态生成按钮
	var buttons []tele.Btn
	for _, lang := range SupportedLanguages {
		text := lang.Flag + " " + lang.Name
		// 为当前语言添加✓标识
		if currentLang == lang.Code {
			text = "✓ " + text
		}
		// 使用统一的回调数据格式：userID|langCode
		btn := tele.Btn{
			Text:   text,
			Unique: "lang_" + lang.Code,                     // 用于路由匹配
			Data:   fmt.Sprintf("%d|%s", userID, lang.Code), // 格式：userID|langCode
		}
		buttons = append(buttons, btn)
	}

	// 按顺序两两一行排列
	var rows []tele.Row
	for i := 0; i < len(buttons); i += 2 {
		if i+1 < len(buttons) {
			rows = append(rows, selector.Row(buttons[i], buttons[i+1]))
		} else {
			rows = append(rows, selector.Row(buttons[i]))
		}
	}

	selector.Inline(rows...)
	return selector
}
