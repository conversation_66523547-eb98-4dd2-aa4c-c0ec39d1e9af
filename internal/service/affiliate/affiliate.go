package affiliate

import (
	"context"
	"telegram-chatbot-go/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type AffiliateService struct {
	logx.Logger
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

func NewAffiliateService(svcCtx *svc.ServiceContext, ctx context.Context) *AffiliateService {
	return &AffiliateService{
		Logger: logx.WithContext(ctx),
		svcCtx: svcCtx,
		ctx:    ctx,
	}
}

// GroupInfo 群组信息结构
type GroupInfo struct {
	ChatID int64  `json:"chat_id"`
	Title  string `json:"title"`
}
