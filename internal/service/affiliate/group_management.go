package affiliate

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// GroupManagement 处理群组管理命令
func (s *AffiliateService) GroupManagement(c tele.Context) error {
	return s.ShowGroups(c, 1)
}

// ShowGroups 显示用户的群组列表（分页）
func (s *AffiliateService) ShowGroups(c tele.Context, page int) error {
	return s.showGroupsInternal(c, page, false)
}

// ShowGroupsRefresh 刷新群组列表，检测内容变化
func (s *AffiliateService) ShowGroupsRefresh(c tele.Context, page int) error {
	return s.showGroupsInternal(c, page, true)
}

// showGroupsInternal 内部实现，支持刷新检测
func (s *AffiliateService) showGroupsInternal(c tele.Context, page int, isRefresh bool) error {
	if page <= 0 {
		page = 1
	}

	userID := c.Sender().ID
	lang := utils.GetUserLanguage(c)

	// 获取用户的群组列表
	groups, err := s.svcCtx.ChatModel.FindOwnerGroups(s.ctx, userID)
	if err != nil {
		return c.Reply(utils.GetI18nText(c, i18n.Keys.Common.Errors.General))
	}

	// 分页设置
	perPage := 5
	totalPages := (len(groups) + perPage - 1) / perPage
	if totalPages == 0 {
		totalPages = 1
	}

	startIdx := (page - 1) * perPage
	endIdx := startIdx + perPage
	if endIdx > len(groups) {
		endIdx = len(groups)
	}

	var currentGroups []*model.Chat
	if startIdx < len(groups) {
		currentGroups = groups[startIdx:endIdx]
	}

	// 构建键盘
	keyboard := &tele.ReplyMarkup{}
	var rows []tele.Row

	// 添加群组按钮
	for _, group := range currentGroups {
		title := "未知群组"
		if group.Title != nil {
			title = *group.Title
		}

		// 格式：userID|groupID
		callbackData := fmt.Sprintf("%d|%d", userID, group.ChatID)
		btn := keyboard.Data(title, "group_select", callbackData)
		rows = append(rows, keyboard.Row(btn))
	}

	// 添加"添加到群组"按钮
	botInfo := s.svcCtx.Bot.Me
	if botInfo != nil {
		addBotBtn := keyboard.URL(
			s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.AddBot, lang),
			fmt.Sprintf("https://t.me/%s?startgroup=true", botInfo.Username),
		)
		rows = append(rows, keyboard.Row(addBotBtn))
	}

	// 添加导航按钮
	var navButtons []tele.Btn
	if page > 1 {
		// 格式：userID|page
		prevCallbackData := fmt.Sprintf("%d|%d", userID, page-1)
		prevBtn := keyboard.Data(
			s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.PrevPage, lang),
			"group_page", prevCallbackData,
		)
		navButtons = append(navButtons, prevBtn)
	}

	// 格式：userID|page|refresh
	refreshCallbackData := fmt.Sprintf("%d|%d|refresh", userID, page)
	refreshBtn := keyboard.Data(
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.Refresh, lang),
		"group_page", refreshCallbackData,
	)
	navButtons = append(navButtons, refreshBtn)

	// 帮助按钮不需要鉴权，使用空字符串
	helpBtn := keyboard.Data(
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.Help, lang),
		"group_help", "",
	)
	navButtons = append(navButtons, helpBtn)

	if page < totalPages {
		// 格式：userID|page
		nextCallbackData := fmt.Sprintf("%d|%d", userID, page+1)
		nextBtn := keyboard.Data(
			s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.NextPage, lang),
			"group_page", nextCallbackData,
		)
		navButtons = append(navButtons, nextBtn)
	}

	rows = append(rows, keyboard.Row(navButtons...))
	keyboard.Inline(rows...)

	// 创建消息文本
	text := s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.Tutorial, lang)

	// 发送或编辑消息
	if c.Callback() != nil {
		// 如果是刷新操作，检查内容是否有变化
		if isRefresh {
			// 获取当前消息内容
			currentMsg := c.Callback().Message
			if currentMsg != nil {
				// 比较文本和键盘是否都相同
				// 群组列表的变化主要体现在键盘上（群组增减、标题变化等）
				textUnchanged := currentMsg.Text == text
				keyboardUnchanged := s.compareKeyboards(currentMsg.ReplyMarkup, keyboard)

				if textUnchanged && keyboardUnchanged {
					// 内容没有变化，只弹出提示窗，不调用Edit避免Telegram报错
					return c.Respond(&tele.CallbackResponse{
						Text:      "✅ Group list is already up to date",
						ShowAlert: false,
					})
				}
			}
		}

		// 使用 defer + recover 来捕获 Telegram 的 "message is not modified" 错误
		defer func() {
			if r := recover(); r != nil {
				// 如果是刷新操作且发生错误，尝试弹出提示窗
				if isRefresh {
					c.Respond(&tele.CallbackResponse{
						Text:      "✅ Group list is already up to date",
						ShowAlert: false,
					})
				}
			}
		}()

		err := c.Edit(text, keyboard)
		if err != nil && isRefresh {
			// 如果是刷新操作且编辑失败（可能是内容相同），弹出提示窗
			if strings.Contains(err.Error(), "message is not modified") {
				return c.Respond(&tele.CallbackResponse{
					Text:      "✅ Group list is already up to date",
					ShowAlert: false,
				})
			}
		}
		return err
	} else {
		return c.Reply(text, keyboard)
	}
}

// HandleGroupSelect 处理群组选择
func (s *AffiliateService) HandleGroupSelect(c tele.Context, groupID string) error {
	userID := c.Sender().ID
	lang := utils.GetUserLanguage(c)

	chatID, err := strconv.ParseInt(groupID, 10, 64)
	if err != nil {
		return c.Respond(&tele.CallbackResponse{
			Text: s.svcCtx.I18n.GetText(i18n.Keys.Common.Errors.General, lang),
		})
	}

	// 验证群组是否属于该用户
	groups, err := s.svcCtx.ChatModel.FindOwnerGroups(s.ctx, userID)
	if err != nil {
		return c.Respond(&tele.CallbackResponse{
			Text: s.svcCtx.I18n.GetText(i18n.Keys.Common.Errors.General, lang),
		})
	}

	var selectedGroup *model.Chat
	for _, group := range groups {
		if group.ChatID == chatID {
			selectedGroup = group
			break
		}
	}

	if selectedGroup == nil {
		return c.Respond(&tele.CallbackResponse{
			Text: s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.GroupNotFound, lang),
		})
	}

	// 构建群组管理键盘
	keyboard := &tele.ReplyMarkup{}

	// 格式：userID|groupID
	unbindCallbackData := fmt.Sprintf("%d|%s", userID, groupID)
	unbindBtn := keyboard.Data(
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.DeleteBinding, lang),
		"group_unbind", unbindCallbackData,
	)

	// 格式：userID|page
	backCallbackData := fmt.Sprintf("%d|1", userID)
	backBtn := keyboard.Data(
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.BackToList, lang),
		"group_page", backCallbackData,
	)

	keyboard.Inline(
		keyboard.Row(unbindBtn),
		keyboard.Row(backBtn),
	)

	// 构建群组信息文本
	title := "未知群组"
	if selectedGroup.Title != nil {
		title = *selectedGroup.Title
	}

	text := s.svcCtx.I18n.GetTextWithArgs(i18n.Keys.Group.Management.GroupInfo, map[string]interface{}{
		"title":   title,
		"chat_id": chatID,
	}, lang)

	return c.Edit(text, keyboard)
}

// HandleGroupUnbind 处理群组解绑
func (s *AffiliateService) HandleGroupUnbind(c tele.Context, groupID string) error {
	lang := utils.GetUserLanguage(c)

	chatID, err := strconv.ParseInt(groupID, 10, 64)
	if err != nil {
		return c.Respond(&tele.CallbackResponse{
			Text: s.svcCtx.I18n.GetText(i18n.Keys.Common.Errors.General, lang),
		})
	}

	// 获取群组信息用于日志记录
	groupInfo, _ := s.svcCtx.ChatModel.FindOneByChatID(s.ctx, chatID)

	// 尝试让机器人退出群组
	leaveResult := s.tryLeaveGroup(chatID)

	// 执行解绑操作
	err = s.svcCtx.ChatModel.UnbindGroupFromUser(s.ctx, nil, chatID)
	if err != nil {
		return c.Respond(&tele.CallbackResponse{
			Text: s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.UnbindFailed, lang),
		})
	}

	// 记录群组解绑日志
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 日志记录失败不影响主流程
			}
		}()

		logCtx := utils.GetTimeoutContext(c)

		// 获取 TraceID 作为 SessionID
		var sessionID *string
		if traceID := utils.GetTraceID(c); traceID != "" {
			sessionID = &traceID
		}

		var groupTitle *string
		var groupType *string

		if groupInfo != nil {
			if groupInfo.Title != nil {
				groupTitle = groupInfo.Title
			}
			groupType = &groupInfo.ChatType
		}

		s.svcCtx.UserConsumptionLogModel.LogGroupBinding(logCtx, nil, &model.GroupBindingLogParams{
			SessionID:   sessionID,
			UserID:      c.Sender().ID,
			ChatID:      &chatID,
			Operation:   "unbind",
			GroupTitle:  groupTitle,
			GroupType:   groupType,
			LeaveResult: &leaveResult,
		})
	}()

	// 构建响应消息
	successMsg := s.svcCtx.I18n.GetText(i18n.Keys.Group.Management.UnbindSuccess, lang)
	if leaveResult != "" {
		successMsg += "\n" + leaveResult
	}

	// 响应成功消息
	err = c.Respond(&tele.CallbackResponse{
		Text: successMsg,
	})
	if err != nil {
		return err
	}

	// 返回到群组列表
	return s.ShowGroups(c, 1)
}

// ShowHelp 显示帮助信息
func (s *AffiliateService) ShowHelp(c tele.Context) error {
	userID := c.Sender().ID
	lang := utils.GetUserLanguage(c)

	keyboard := &tele.ReplyMarkup{}
	// 格式：userID|page
	backCallbackData := fmt.Sprintf("%d|1", userID)
	backBtn := keyboard.Data(
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Help.Back, lang),
		"group_page", backCallbackData,
	)
	keyboard.Inline(keyboard.Row(backBtn))

	helpText := fmt.Sprintf("%s\n\n%s",
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Help.Title, lang),
		s.svcCtx.I18n.GetText(i18n.Keys.Group.Help.Content, lang),
	)

	return c.Edit(helpText, keyboard)
}

// HandleBotAddedToGroup 处理机器人被添加到群组的事件
func (s *AffiliateService) HandleBotAddedToGroup(c tele.Context) error {
	if c.Message() == nil || len(c.Message().UsersJoined) == 0 {
		return nil
	}

	botInfo := s.svcCtx.Bot.Me
	if botInfo == nil {
		return nil
	}

	// 检查是否是本机器人被添加
	var botAdded bool
	for _, user := range c.Message().UsersJoined {
		if user.ID == botInfo.ID {
			botAdded = true
			break
		}
	}

	if !botAdded {
		return nil
	}

	// 只有超级群组才能绑定
	if c.Chat().Type != tele.ChatSuperGroup {
		lang := utils.GetUserLanguage(c)

		failedText := s.svcCtx.I18n.GetTextWithArgs(i18n.Keys.Group.Binding.Failed, map[string]interface{}{
			"chat_id":  c.Chat().ID,
			"title":    c.Chat().Title,
			"user_id":  c.Sender().ID,
			"username": c.Sender().Username,
		}, lang)

		// 发送失败消息给用户
		_, err := c.Bot().Send(&tele.User{ID: c.Sender().ID}, failedText)
		return err
	}

	// 绑定群组到用户
	err := s.BindGroupToUser(c.Chat().ID, c.Sender().ID, c.Chat().Title)
	if err != nil {
		return err
	}

	// 记录群组绑定日志
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 日志记录失败不影响主流程
			}
		}()

		logCtx := utils.GetTimeoutContext(c)
		chatID := c.Chat().ID
		groupTitle := c.Chat().Title
		groupType := "supergroup"
		// 获取 TraceID 作为 SessionID
		var sessionID *string
		if traceID := utils.GetTraceID(c); traceID != "" {
			sessionID = &traceID
		}

		s.svcCtx.UserConsumptionLogModel.LogGroupBinding(logCtx, nil, &model.GroupBindingLogParams{
			SessionID:  sessionID,
			UserID:     c.Sender().ID,
			ChatID:     &chatID,
			Operation:  "bind",
			GroupTitle: &groupTitle,
			GroupType:  &groupType,
		})
	}()

	lang := utils.GetUserLanguage(c)

	// 发送成功消息到群组
	successText := s.svcCtx.I18n.GetTextWithArgs(i18n.Keys.Group.Binding.Success, map[string]interface{}{
		"chat_id":  c.Chat().ID,
		"title":    c.Chat().Title,
		"user_id":  c.Sender().ID,
		"username": c.Sender().Username,
	}, lang)

	err = c.Reply(successText)
	if err != nil {
		return err
	}

	// 发送成功消息给用户
	successUserText := s.svcCtx.I18n.GetTextWithArgs(i18n.Keys.Group.Binding.SuccessUser, map[string]interface{}{
		"chat_id":  c.Chat().ID,
		"title":    c.Chat().Title,
		"user_id":  c.Sender().ID,
		"username": c.Sender().Username,
	}, lang)

	_, err = c.Bot().Send(&tele.User{ID: c.Sender().ID}, successUserText)
	return err
}

// BindGroupToUser 绑定群组到用户
func (s *AffiliateService) BindGroupToUser(chatID, userID int64, title string) error {
	// 首先尝试查找现有记录
	existingChat, err := s.svcCtx.ChatModel.FindOneByChatID(s.ctx, chatID)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果不存在，创建新记录
		newChat := &model.Chat{
			ChatID:   chatID,
			ChatType: "supergroup",
			Title:    &title,
			OwnerID:  &userID,
		}
		return s.svcCtx.ChatModel.Insert(s.ctx, nil, newChat)
	} else if err != nil {
		return err
	}

	// 如果存在但没有绑定用户，或者需要更新绑定关系
	if existingChat.OwnerID == nil || *existingChat.OwnerID != userID {
		return s.svcCtx.ChatModel.BindGroupToUser(s.ctx, nil, chatID, userID)
	}

	// 已经绑定到该用户，无需操作
	return nil
}

// tryLeaveGroup 尝试让机器人退出群组
func (s *AffiliateService) tryLeaveGroup(chatID int64) string {
	// 尝试让机器人退出群组
	chat := &tele.Chat{ID: chatID}
	err := s.svcCtx.Bot.Leave(chat)

	if err != nil {
		// 退群失败，返回英文提示
		return "⚠️ Bot failed to leave the group automatically. Please remove the bot manually if needed."
	}

	// 退群成功
	return "✅ Bot has automatically left the group."
}

// compareKeyboards 比较两个键盘是否相同
func (s *AffiliateService) compareKeyboards(oldMarkup *tele.ReplyMarkup, newMarkup *tele.ReplyMarkup) bool {
	// 如果其中一个为空，检查另一个是否也为空
	if oldMarkup == nil || newMarkup == nil {
		return oldMarkup == newMarkup
	}

	// 比较内联键盘
	oldInline := oldMarkup.InlineKeyboard
	newInline := newMarkup.InlineKeyboard

	// 检查行数是否相同
	if len(oldInline) != len(newInline) {
		return false
	}

	// 逐行比较
	for i, oldRow := range oldInline {
		newRow := newInline[i]

		// 检查每行的按钮数是否相同
		if len(oldRow) != len(newRow) {
			return false
		}

		// 逐个比较按钮
		for j, oldBtn := range oldRow {
			newBtn := newRow[j]

			// 比较按钮文本和URL（URL按钮不会变化）
			if oldBtn.Text != newBtn.Text || oldBtn.URL != newBtn.URL {
				return false
			}

			// 对于回调按钮，需要特殊处理数据比较
			if oldBtn.Data != "" || newBtn.Data != "" {
				// 提取并比较实际的业务数据（去除用户ID部分）
				if !s.compareCallbackData(oldBtn.Data, newBtn.Data) {
					return false
				}
			}
		}
	}

	return true
}

// compareCallbackData 比较回调数据，忽略用户ID部分
func (s *AffiliateService) compareCallbackData(oldData, newData string) bool {
	// 如果都为空，相同
	if oldData == "" && newData == "" {
		return true
	}

	// 如果一个为空一个不为空，不同
	if oldData == "" || newData == "" {
		return false
	}

	// 解析回调数据，格式：userID|data1|data2...
	oldParts := strings.Split(oldData, "|")
	newParts := strings.Split(newData, "|")

	// 检查段数是否相同
	if len(oldParts) != len(newParts) {
		return false
	}

	// 跳过第一段（用户ID），比较后续的业务数据
	for i := 1; i < len(oldParts); i++ {
		if oldParts[i] != newParts[i] {
			return false
		}
	}

	return true
}
