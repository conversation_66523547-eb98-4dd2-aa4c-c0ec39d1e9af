package user

import (
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// === 管理员命令实现 ===

// AdminCreateCode 管理员创建兑换码命令
// 用法: /admin_create_code <vip_days> <vip_points> <permanent_points> [caption]
// 例如: /admin_create_code 30 100 50 新用户礼包
func (s *UserService) AdminCreateCode(c tele.Context) error {
	args := strings.Fields(utils.ExtractArgs(c.Text()))
	if len(args) < 3 {
		return c.Send("用法: /admin_create_code <vip_days> <vip_points> <permanent_points> [caption]\n例如: /admin_create_code 30 100 50 新用户礼包")
	}

	// 解析参数
	vipDays, err := strconv.ParseInt(args[0], 10, 32)
	if err != nil {
		return c.Send("VIP天数必须是数字")
	}

	vipPoints, err := strconv.ParseInt(args[1], 10, 32)
	if err != nil {
		return c.Send("VIP积分必须是数字")
	}

	permanentPoints, err := strconv.ParseInt(args[2], 10, 32)
	if err != nil {
		return c.Send("永久积分必须是数字")
	}

	// 可选的备注
	var caption string
	if len(args) > 3 {
		caption = strings.Join(args[3:], " ")
	}

	// 创建兑换码
	code, err := s.CreateRedemptionCode(int32(vipDays), int32(vipPoints), int32(permanentPoints), caption)
	if err != nil {
		return c.Send(fmt.Sprintf("创建兑换码失败: %v", err))
	}

	// 构建私发消息
	message := fmt.Sprintf("✅ 兑换码创建成功!\n\n"+
		"🎫 兑换码: `%s`\n"+
		"⏰ VIP天数: %d天\n"+
		"💎 VIP积分: %d\n"+
		"🔥 永久积分: %d\n",
		code, vipDays, vipPoints, permanentPoints)

	if caption != "" {
		message += fmt.Sprintf("📝 备注: %s\n", caption)
	}

	// 尝试私发给管理员
	adminUser := &tele.User{ID: c.Sender().ID}
	_, err = c.Bot().Send(adminUser, message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
	if err != nil {
		err = c.Send("⚠️ 兑换码已创建但私发失败")
	} else {
		err = c.Send("✅ 兑换码已创建并私发给您")
	}

	// 记录系统操作日志
	// 获取 TraceID 作为 SessionID
	var sessionID *string
	if traceID := utils.GetTraceID(c); traceID != "" {
		sessionID = &traceID
	}

	var chatID *int64
	if c.Chat() != nil {
		chatID = &[]int64{c.Chat().ID}[0]
	}

	var messageID *int64
	if c.Message() != nil {
		messageID = &[]int64{int64(c.Message().ID)}[0]
	}

	_ = s.svcCtx.UserConsumptionLogModel.LogSystemOperation(s.ctx, nil, &model.SystemLogParams{
		SessionID:   sessionID,
		UserID:      c.Sender().ID,
		ChatID:      chatID,
		MessageID:   messageID,
		Operation:   "create_redemption_code",
		Description: &[]string{"管理员创建兑换码"}[0],
		ExtraData: map[string]interface{}{
			"code":             code,
			"vip_days":         vipDays,
			"vip_points":       vipPoints,
			"permanent_points": permanentPoints,
			"caption":          caption,
		},
	})

	return err
}

// AdminCreateMany 管理员批量创建兑换码命令
// 用法: /admin_create_many <number> <vip_days> <vip_points> <permanent_points> [code_length] [caption]
// 例如: /admin_create_many 10 30 100 50 12 批量礼包
func (s *UserService) AdminCreateMany(c tele.Context) error {
	args := strings.Fields(utils.ExtractArgs(c.Text()))
	if len(args) < 4 {
		return c.Send("用法: /admin_create_many <number> <vip_days> <vip_points> <permanent_points> [code_length] [caption]\n例如: /admin_create_many 10 30 100 50 12 批量礼包")
	}

	// 解析参数
	number, err := strconv.Atoi(args[0])
	if err != nil || number <= 0 || number > 100 {
		return c.Send("数量必须是1-100之间的数字")
	}

	vipDays, err := strconv.ParseInt(args[1], 10, 32)
	if err != nil {
		return c.Send("VIP天数必须是数字")
	}

	vipPoints, err := strconv.ParseInt(args[2], 10, 32)
	if err != nil {
		return c.Send("VIP积分必须是数字")
	}

	permanentPoints, err := strconv.ParseInt(args[3], 10, 32)
	if err != nil {
		return c.Send("永久积分必须是数字")
	}

	// 可选的码长度
	codeLength := 10
	if len(args) > 4 {
		if length, err := strconv.Atoi(args[4]); err == nil && length >= 6 && length <= 20 {
			codeLength = length
		}
	}

	// 可选的备注
	var caption string
	if len(args) > 5 {
		caption = strings.Join(args[5:], " ")
	}

	// 发送处理中消息
	processingMsg, err := c.Bot().Send(c.Chat(), "🔄 正在批量创建兑换码，请稍候...")
	if err != nil {
		return err
	}

	// 批量创建兑换码
	codes, err := s.CreateManyRedemptionCodes(number, int32(vipDays), int32(vipPoints), int32(permanentPoints), codeLength, caption)
	if err != nil {
		_, editErr := c.Bot().Edit(processingMsg, fmt.Sprintf("批量创建兑换码失败: %v", err))
		if editErr != nil {
			return editErr
		}
		return nil
	}

	// 构建消息
	message := fmt.Sprintf("✅ 批量创建兑换码成功!\n\n"+
		"📊 数量: %d个\n"+
		"⏰ VIP天数: %d天\n"+
		"💎 VIP积分: %d\n"+
		"🔥 永久积分: %d\n"+
		"📏 码长: %d位\n",
		len(codes), vipDays, vipPoints, permanentPoints, codeLength)

	if caption != "" {
		message += fmt.Sprintf("📝 备注: %s\n", caption)
	}

	message += "\n🎫 兑换码列表:\n"
	for i, code := range codes {
		message += fmt.Sprintf("%d. `%s`\n", i+1, code)
	}

	// 尝试私发给管理员
	adminUser := &tele.User{ID: c.Sender().ID}

	// 如果消息太长，分两条私发
	if len(message) > 4000 {
		// 先发送基本信息
		basicInfo := fmt.Sprintf("✅ 批量创建兑换码成功!\n\n"+
			"📊 数量: %d个\n"+
			"⏰ VIP天数: %d天\n"+
			"💎 VIP积分: %d\n"+
			"🔥 永久积分: %d\n"+
			"📏 码长: %d位\n",
			len(codes), vipDays, vipPoints, permanentPoints, codeLength)

		if caption != "" {
			basicInfo += fmt.Sprintf("📝 备注: %s\n", caption)
		}

		_, err = c.Bot().Send(adminUser, basicInfo, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
		if err != nil {
			// 如果私发失败，在当前聊天中发送不包含兑换码的消息
			_, editErr := c.Bot().Edit(processingMsg, "✅ 兑换码已创建，但私发失败，请检查是否已开启与机器人的私聊")
			if editErr != nil {
				return editErr
			}
		} else {
			// 私发成功，在当前聊天中显示简单确认
			_, err = c.Bot().Edit(processingMsg, "✅ 兑换码已创建并私发给您")
			if err != nil {
				return err
			}
		}

		// 再发送兑换码列表
		codesList := "🎫 兑换码列表:\n"
		for i, code := range codes {
			codesList += fmt.Sprintf("%d. `%s`\n", i+1, code)
		}

		_, err = c.Bot().Send(adminUser, codesList, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
		if err != nil {
			// 如果私发失败，不在群里发送兑换码！
			return c.Send("⚠️ 兑换码列表私发失败，请检查是否已开启与机器人的私聊")
		}
	} else {
		// 消息不长，直接私发
		_, err = c.Bot().Send(adminUser, message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
		if err != nil {
			// 如果私发失败，在当前聊天中发送不包含兑换码的消息
			_, editErr := c.Bot().Edit(processingMsg, "⚠️ 兑换码已创建，但私发失败，请检查是否已开启与机器人的私聊")
			if editErr != nil {
				return editErr
			}
		} else {
			// 私发成功，在当前聊天中显示简单确认
			_, err = c.Bot().Edit(processingMsg, "✅ 兑换码已创建并私发给您")
			if err != nil {
				return err
			}
		}
	}

	// 记录系统操作日志
	// 获取 TraceID 作为 SessionID
	var sessionID *string
	if traceID := utils.GetTraceID(c); traceID != "" {
		sessionID = &traceID
	}

	var chatID *int64
	if c.Chat() != nil {
		chatID = &[]int64{c.Chat().ID}[0]
	}

	var messageID *int64
	if c.Message() != nil {
		messageID = &[]int64{int64(c.Message().ID)}[0]
	}

	logErr := s.svcCtx.UserConsumptionLogModel.LogSystemOperation(s.ctx, nil, &model.SystemLogParams{
		SessionID:   sessionID,
		UserID:      c.Sender().ID,
		ChatID:      chatID,
		MessageID:   messageID,
		Operation:   "create_many_redemption_codes",
		Description: &[]string{"管理员批量创建兑换码"}[0],
		ExtraData: map[string]interface{}{
			"count":            number,
			"codes":            codes,
			"vip_days":         vipDays,
			"vip_points":       vipPoints,
			"permanent_points": permanentPoints,
			"code_length":      codeLength,
			"caption":          caption,
		},
	})

	return logErr
}

// AdminDeleteCode 管理员删除兑换码命令
// 用法: /admin_delete_code <code>
func (s *UserService) AdminDeleteCode(c tele.Context) error {
	code := strings.TrimSpace(utils.ExtractArgs(c.Text()))
	if code == "" {
		return c.Send("用法: /admin_delete_code <code>\n例如: /admin_delete_code ABC123DEF456")
	}

	// 删除兑换码
	err := s.DeleteRedemptionCode(code)
	if err != nil {
		return c.Send(fmt.Sprintf("删除兑换码失败: %v", err))
	}

	// 构建私发消息
	message := fmt.Sprintf("✅ 兑换码删除成功!\n\n🎫 已删除兑换码: `%s`", code)

	// 尝试私发给管理员
	adminUser := &tele.User{ID: c.Sender().ID}
	_, err = c.Bot().Send(adminUser, message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
	if err != nil {
		// 如果私发失败，在当前聊天中发送不包含兑换码的消息
		err = c.Send("✅ 兑换码已成功删除，详情已私发给您")
	} else {
		// 在当前聊天中发送简单确认消息
		err = c.Send("✅ 兑换码已成功删除，详情已私发给您")
	}

	// 记录系统操作日志
	// 获取 TraceID 作为 SessionID
	var sessionID *string
	if traceID := utils.GetTraceID(c); traceID != "" {
		sessionID = &traceID
	}

	var chatID *int64
	if c.Chat() != nil {
		chatID = &[]int64{c.Chat().ID}[0]
	}

	var messageID *int64
	if c.Message() != nil {
		messageID = &[]int64{int64(c.Message().ID)}[0]
	}

	logErr := s.svcCtx.UserConsumptionLogModel.LogSystemOperation(s.ctx, nil, &model.SystemLogParams{
		SessionID:   sessionID,
		UserID:      c.Sender().ID,
		ChatID:      chatID,
		MessageID:   messageID,
		Operation:   "delete_redemption_code",
		Description: &[]string{"管理员删除兑换码"}[0],
		ExtraData: map[string]interface{}{
			"code": code,
		},
	})

	// 如果日志记录失败，返回日志错误；否则返回发送消息的错误
	if logErr != nil {
		return logErr
	}
	return err
}

// AdminQueryCode 管理员查询兑换码命令
// 用法: /admin_query_code <code>
func (s *UserService) AdminQueryCode(c tele.Context) error {
	code := strings.TrimSpace(utils.ExtractArgs(c.Text()))
	if code == "" {
		return c.Send("用法: /admin_query_code <code>\n例如: /admin_query_code ABC123DEF456")
	}

	// 查询兑换码信息
	redemptionCode, err := s.GetRedemptionCodeInfo(code)
	if err != nil {
		return c.Send("兑换码不存在")
	}

	// 构建响应消息
	status := "🟢 未使用"
	if redemptionCode.UserID != nil {
		status = fmt.Sprintf("🔴 已使用 (用户ID: %d)", *redemptionCode.UserID)
	}
	if !redemptionCode.IsActive {
		status = "⚫ 已失效"
	}

	message := fmt.Sprintf("🎫 兑换码信息\n\n"+
		"📋 兑换码: `%s`\n"+
		"📊 状态: %s\n"+
		"⏰ VIP天数: %d天\n"+
		"💎 VIP积分: %d\n"+
		"🔥 永久积分: %d\n"+
		"🕐 创建时间: %s\n",
		redemptionCode.Code,
		status,
		redemptionCode.VipDays,
		redemptionCode.VipPoints,
		redemptionCode.PermanentPointBalance,
		redemptionCode.CreatedAt.Format("2006-01-02 15:04:05"))

	if redemptionCode.UsedAt != nil {
		message += fmt.Sprintf("🕐 使用时间: %s\n", redemptionCode.UsedAt.Format("2006-01-02 15:04:05"))
	}

	if redemptionCode.Caption != nil && *redemptionCode.Caption != "" {
		message += fmt.Sprintf("📝 备注: %s\n", *redemptionCode.Caption)
	}

	// 尝试私发给管理员
	adminUser := &tele.User{ID: c.Sender().ID}
	_, err = c.Bot().Send(adminUser, message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
	if err != nil {
		// 如果私发失败，在当前聊天中发送不包含兑换码的消息
		return c.Send("⚠️ 兑换码查询完成，但私发失败，请检查是否已开启与机器人的私聊")
	} else {
		// 在当前聊天中发送简单确认消息
		return c.Send("✅ 兑换码查询完成，详情已私发给您")
	}
}

// GetRedemptionCodeInfo 获取兑换码信息
func (s *UserService) GetRedemptionCodeInfo(code string) (*model.RedemptionCode, error) {
	redemptionCode, err := s.svcCtx.RedemptionCodeModel.FindOneByCode(s.ctx, code)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrCodeNotFound
		}
		return nil, err
	}

	return redemptionCode, nil
}

// DeleteRedemptionCode 删除兑换码
func (s *UserService) DeleteRedemptionCode(code string) error {
	// 先查找兑换码是否存在
	redemptionCode, err := s.svcCtx.RedemptionCodeModel.FindOneByCode(s.ctx, code)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrCodeNotFound
		}
		return err
	}

	// 删除兑换码
	return s.svcCtx.RedemptionCodeModel.Delete(s.ctx, nil, redemptionCode.ID)
}

// CreateRedemptionCode 创建兑换码
func (s *UserService) CreateRedemptionCode(vipDays, vipPoints, permanentPoints int32, caption string) (string, error) {
	// 生成兑换码
	code := s.generateRedemptionCode(10)

	// 创建兑换码记录
	redemptionCode := &model.RedemptionCode{
		Code:                  code,
		VipDays:               vipDays,
		VipPoints:             vipPoints,
		PermanentPointBalance: permanentPoints,
		IsActive:              true,
		Caption:               &caption,
	}

	err := s.svcCtx.RedemptionCodeModel.Insert(s.ctx, nil, redemptionCode)
	if err != nil {
		return "", err
	}

	return code, nil
}

// CreateManyRedemptionCodes 批量创建兑换码
func (s *UserService) CreateManyRedemptionCodes(count int, vipDays, vipPoints, permanentPoints int32, codeLength int, caption string) ([]string, error) {
	var codes []string
	var redemptionCodes []*model.RedemptionCode

	for i := 0; i < count; i++ {
		// 生成兑换码
		code := s.generateRedemptionCode(codeLength)
		codes = append(codes, code)

		// 创建兑换码记录
		redemptionCode := &model.RedemptionCode{
			Code:                  code,
			VipDays:               vipDays,
			VipPoints:             vipPoints,
			PermanentPointBalance: permanentPoints,
			IsActive:              true,
			Caption:               &caption,
		}
		redemptionCodes = append(redemptionCodes, redemptionCode)
	}

	// 批量插入
	err := s.svcCtx.RedemptionCodeModel.BatchInsert(s.ctx, nil, redemptionCodes)
	if err != nil {
		return nil, err
	}

	return codes, nil
}

// generateRedemptionCode 生成指定长度的兑换码
func (s *UserService) generateRedemptionCode(length int) string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var result strings.Builder
	result.Grow(length)

	for i := 0; i < length; i++ {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		result.WriteByte(charset[num.Int64()])
	}

	return result.String()
}
