package user

import (
	"fmt"
	"math"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// SignIn 处理用户主动签到命令
func (s *UserService) SignIn(c tele.Context) error {
	userID := c.Sender().ID

	// 发送签到状态检查消息
	checkingMsg := utils.GetI18nText(c, i18n.Keys.SignIn.CheckStatus)
	err := c.Send(checkingMsg)
	if err != nil {
		return err
	}

	// 执行签到
	err = s.CheckAndSignIn(userID)
	if err != nil {
		switch err {
		case ErrAlreadySignedIn:
			userInfo, err := s.GetUserBalance(userID)
			if err != nil {
				return err
			}
			// 保留一位小数
			nextCheckInHours := math.Round((18-time.Now().Sub(userInfo.DailyPointLastResetAt).Hours())*10) / 10

			return c.Send(utils.GetI18nTextWithArgs(c, i18n.Keys.SignIn.AlreadySignedIn, map[string]interface{}{
				"next_check_in_hours": nextCheckInHours,
			}))
		case ErrCannotSignIn:
			return c.Send(utils.GetI18nText(c, i18n.Keys.SignIn.CannotSignIn))
		default:
			return c.Send(fmt.Sprintf("failed to sign in: %v", err))
		}
	}

	// 获取用户最新信息以显示奖励
	userInfo, err := s.GetUserBalance(userID)
	if err != nil {
		// 签到成功但获取用户信息失败，显示简单成功消息
		successMsg := utils.GetI18nText(c, i18n.Keys.SignIn.Success)
		c.Send(successMsg)
		return nil
	}

	// 构建成功消息，显示奖励和当前余额
	successMsg := utils.GetI18nTextWithArgs(c, i18n.Keys.SignIn.Success, map[string]interface{}{
		"reward":        s.svcCtx.Config.Operation.DailyPoints,
		"daily_balance": userInfo.DailyPointBalance,
		"total_balance": userInfo.DailyPointBalance + userInfo.VipPointBalance + userInfo.PermanentPointBalance,
	})

	c.Send(successMsg)
	return nil
}
