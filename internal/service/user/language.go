package user

import (
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/keyboard"
	"telegram-chatbot-go/internal/menu"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// Language 处理language命令
func (s *UserService) Language(c tele.Context) error {
	message := utils.GetI18nText(c, i18n.Keys.Language.Choose)

	userLang := utils.GetUserLanguage(c)
	userID := c.Sender().ID

	// 使用带鉴权的语言选择键盘
	keyboardMarkup := keyboard.GetLanguageSelectWithAuth(userLang, userID)
	return c.Send(message, keyboardMarkup)
}

// HandleLanguageCallback 处理语言选择回调
func (s *UserService) HandleLanguageCallback(c tele.Context, langCode string) error {
	userLang := utils.GetUserLanguage(c)

	// 如果选择的语言与当前语言相同，只响应回调但不编辑消息
	if langCode != "" && langCode == userLang {
		return c.Respond(&tele.CallbackResponse{
			Text:      "Already selected!",
			ShowAlert: false,
		})
	}

	// 如果有选择新语言且与当前不同，则更新用户语言设置
	if langCode != "" && langCode != userLang {
		// 更新数据库中的用户语言设置
		if c.Sender() == nil {
			return nil // 无发送者信息，静默失败
		}
		userID := c.Sender().ID

		// 创建用户服务并更新语言
		err := s.UpdateLanguage(userID, langCode)
		if err != nil {
			return err
		}

		utils.SetUserLanguage(c, langCode)
		userLang = langCode
	}

	// 统一使用带鉴权的语言选择键盘
	keyboardMarkup := keyboard.GetLanguageSelectWithAuth(userLang, c.Sender().ID)

	// 使用更新后的语言获取消息文本
	message := utils.GetI18nText(c, i18n.Keys.Language.Changed)

	err := c.Edit(message, keyboardMarkup)
	if err != nil {
		return err
	}

	menuKeyboard := menu.GetMainKeyboard(c)
	return c.Send(message, menuKeyboard)
}
