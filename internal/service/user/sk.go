package user

import (
	"errors"
	"fmt"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// === 用户命令实现 ===

// Sk 处理sk命令（兑换码）
func (s *UserService) Sk(c tele.Context) error {
	// 提取兑换码
	code := strings.TrimSpace(utils.ExtractArgs(c.Text()))
	if code == "" {
		return c.Send(utils.GetMissingCodeError(c))
	}

	// 发送验证中的消息
	verifyingMsg := utils.GetI18nTextWithArgs(c, i18n.Keys.Sk.Verifying, map[string]interface{}{
		"code": code,
	})

	sentMessage, err := c.Bot().Send(c.Chat(), verifyingMsg)
	if err != nil {
		return err
	}

	// 使用兑换码
	userID := c.Sender().ID
	redemptionCode, err := s.UseRedemptionCode(c, code, userID)
	if err != nil {
		// 根据错误类型发送不同的消息
		var errorMsg string
		switch err {
		case ErrCodeNotFound:
			errorMsg = utils.GetI18nText(c, i18n.Keys.Sk.Invalid)
		case ErrCodeAlreadyUsed:
			errorMsg = utils.GetI18nText(c, i18n.Keys.Sk.AlreadyUsed)
		case ErrCodeInactive:
			errorMsg = utils.GetI18nText(c, i18n.Keys.Sk.Invalid)
		default:
			errorMsg = err.Error()
		}

		_, editErr := c.Bot().Edit(sentMessage, errorMsg)
		if editErr != nil {
			return editErr
		}
		return nil
	}

	// 构建成功消息
	successMsg := utils.GetI18nTextWithArgs(c, i18n.Keys.Sk.Success, map[string]interface{}{
		"reward": redemptionCode.VipPoints + redemptionCode.PermanentPointBalance,
	})

	_, editErr := c.Bot().Edit(sentMessage, successMsg)
	return editErr
}

// UseRedemptionCode 使用兑换码
func (s *UserService) UseRedemptionCode(c tele.Context, code string, userID int64) (*model.RedemptionCode, error) {
	var redemptionCode *model.RedemptionCode

	// 使用事务确保数据一致性
	err := s.svcCtx.RedemptionCodeModel.Trans(s.ctx, func(tx *gorm.DB) error {
		// 1. 查找兑换码并加行锁
		var err error
		redemptionCode, err = s.svcCtx.RedemptionCodeModel.FindOneByCodeForUpdate(s.ctx, tx, code)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return ErrCodeNotFound
			}
			return err
		}

		// 2. 检查兑换码状态
		if !redemptionCode.IsActive {
			return ErrCodeInactive
		}

		if redemptionCode.UserID != nil {
			return ErrCodeAlreadyUsed
		}

		// 3. 标记兑换码为已使用
		now := time.Now()
		redemptionCode.UserID = &userID
		redemptionCode.UsedAt = &now
		err = s.svcCtx.RedemptionCodeModel.Update(s.ctx, tx, redemptionCode)
		if err != nil {
			return err
		}

		// 4. 给用户添加奖励（在同一事务中一步更新）
		if redemptionCode.VipDays > 0 || redemptionCode.VipPoints > 0 || redemptionCode.PermanentPointBalance > 0 {
			// 获取用户信息并加行锁
			userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
			if err != nil {
				return fmt.Errorf("获取用户信息失败: %v", err)
			}

			// 计算新的VIP过期时间
			if redemptionCode.VipDays > 0 {
				now := time.Now()
				if userInfo.VipPointExpiredDate.Before(now) {
					userInfo.VipPointExpiredDate = now.AddDate(0, 0, int(redemptionCode.VipDays))
				} else {
					userInfo.VipPointExpiredDate = userInfo.VipPointExpiredDate.AddDate(0, 0, int(redemptionCode.VipDays))
				}
			}

			// 增加积分
			userInfo.VipPointBalance += redemptionCode.VipPoints
			userInfo.PermanentPointBalance += redemptionCode.PermanentPointBalance

			// 一步更新用户信息
			err = s.svcCtx.UserModel.Update(s.ctx, tx, userInfo)
			if err != nil {
				return fmt.Errorf("更新用户奖励失败: %v", err)
			}

			// 5. 记录兑换日志
			pointChange := &model.PointChange{
				Daily:     0,
				VIP:       redemptionCode.VipPoints,
				Permanent: redemptionCode.PermanentPointBalance,
			}

			var vipDaysChange *int32
			if redemptionCode.VipDays > 0 {
				vipDaysChange = &redemptionCode.VipDays
			}

			// 获取 TraceID 作为 SessionID
			var sessionID *string
			if traceID := utils.GetTraceID(c); traceID != "" {
				sessionID = &traceID
			}

			var chatID *int64
			if c.Chat() != nil {
				chatID = &[]int64{c.Chat().ID}[0]
			}

			var messageID *int64
			if c.Message() != nil {
				messageID = &[]int64{int64(c.Message().ID)}[0]
			}

			err = s.svcCtx.UserConsumptionLogModel.LogRedemption(s.ctx, tx, &model.RedemptionLogParams{
				SessionID:      sessionID,
				UserID:         userID,
				ChatID:         chatID,
				MessageID:      messageID,
				RedemptionCode: code,
				PointChange:    pointChange,
				VipDaysChange:  vipDaysChange,
			})
			if err != nil {
				// 日志记录失败不影响主流程，只记录错误
				s.Errorf("记录兑换日志失败: %v", err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return redemptionCode, nil
}
