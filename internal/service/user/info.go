package user

import (
	"fmt"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// Info 处理info命令
func (s *UserService) Info(c tele.Context) error {
	userID := utils.GetUserID(c)
	username := utils.GetUsername(c)
	userLang := utils.GetUserLanguage(c)

	// 检查并更新过期的VIP积分
	err := s.CheckAndUpdateExpiredVip(userID)
	if err != nil {
		s.Errorf("检查VIP过期状态失败: %v", err)
	}

	// 获取用户信息（可能已更新VIP状态）
	userInfo, err := s.GetUserInfo(userID)
	if err != nil {
		return c.Send(utils.GetI18nText(c, i18n.Keys.Common.Errors.General))
	}

	vipStatusText := s.getVipStatusText(c, userInfo.VipPointExpiredDate)

	// 获取有效的积分余额（考虑签到和VIP过期）
	dailyBalance, vipBalance, permanentBalance := s.GetEffectiveBalances(userInfo)

	// 如果可以签到，执行自动签到
	canSignIn := s.CanSignInToday(userInfo)
	if canSignIn {
		err = s.CheckAndSignIn(userID)
		if err != nil {
			// 如果签到失败，记录错误但不中断流程，使用原始余额
			s.Errorf("自动签到失败: %v", err)
			dailyBalance = userInfo.DailyPointBalance
		}
	}

	totalPower := dailyBalance + vipBalance + permanentBalance

	// 构建信息消息
	title := utils.GetI18nText(c, i18n.Keys.Info.Title)
	userIDText := utils.GetI18nTextWithArgs(c, i18n.Keys.Info.UserID, map[string]interface{}{
		"user_id": userID,
	})
	usernameText := utils.GetI18nTextWithArgs(c, i18n.Keys.Info.Username, map[string]interface{}{
		"username": username,
	})
	languageText := utils.GetI18nTextWithArgs(c, i18n.Keys.Info.Language, map[string]interface{}{
		"language": userLang,
	})
	vipStatusLine := utils.GetI18nTextWithArgs(c, i18n.Keys.Info.VipStatus, map[string]interface{}{
		"status": vipStatusText,
	})
	remainingPowerText := utils.GetI18nTextWithArgs(c, i18n.Keys.Info.RemainingPower, map[string]interface{}{
		"power": totalPower,
	})

	message := fmt.Sprintf("%s\n\n%s\n%s\n%s\n%s\n%s",
		title, userIDText, usernameText, languageText, vipStatusLine, remainingPowerText)

	return c.Send(message)
}

// getVipStatusText 根据VIP过期时间获取VIP状态文本
func (s *UserService) getVipStatusText(c tele.Context, vipExpiredDate time.Time) string {
	now := time.Now()

	// 一分钟内不显示VIP状态
	if vipExpiredDate.After(now.Add(time.Minute * 1)) {
		// VIP未过期
		return utils.GetI18nText(c, i18n.Keys.Info.VipStatusVip)
	} else {
		// VIP已过期或从未激活
		return utils.GetI18nText(c, i18n.Keys.Info.VipStatusNormal)
	}
}
