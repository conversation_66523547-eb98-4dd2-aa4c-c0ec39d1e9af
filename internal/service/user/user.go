package user

import (
	"context"
	"errors"
	"fmt"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/svc"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	logx.Logger
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

// 预定义错误
var (
	ErrCodeNotFound    = errors.New("兑换码不存在")
	ErrCodeAlreadyUsed = errors.New("兑换码已被使用")
	ErrCodeInactive    = errors.New("兑换码已失效")
	ErrAlreadySignedIn = errors.New("今日已签到")
	ErrCannotSignIn    = errors.New("无法签到")
)

// NewUserService 创建用户服务
func NewUserService(svcCtx *svc.ServiceContext, ctx context.Context) *UserService {
	return &UserService{
		Logger: logx.WithContext(ctx),
		svcCtx: svcCtx,
		ctx:    ctx,
	}
}

// GetUserInfo 获取用户信息
func (s *UserService) GetUserInfo(userID int64) (*model.User, error) {
	return s.svcCtx.UserModel.FindOneByUserID(s.ctx, userID)
}

// UpdateLanguage 更新用户语言设置
func (s *UserService) UpdateLanguage(userID int64, language string) error {
	// 先获取旧值
	oldUser, err := s.svcCtx.UserModel.FindOneByUserID(s.ctx, userID)
	if err != nil {
		return err
	}

	err = s.svcCtx.UserModel.UpdateUserFields(s.ctx, nil, userID, map[string]any{
		"language": language,
	})
	if err != nil {
		return err
	}

	// 异步记录用户信息变更日志
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 日志记录失败不影响主流程
			}
		}()

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		s.svcCtx.UserConsumptionLogModel.LogUserInfoChange(ctx, nil, &model.UserInfoChangeLogParams{
			SessionID:  nil, // 语言变更通常没有具体的会话上下文
			UserID:     userID,
			ChatID:     oldUser.ChatID,
			MessageID:  nil,
			ChangeType: "language",
			OldValue:   &oldUser.Language,
			NewValue:   &language,
		})
	}()

	return nil
}

func (s *UserService) MarkInvite(userID int64, inviteUserID int64) error {
	return s.svcCtx.UserModel.UpdateUserFields(s.ctx, nil, userID, map[string]any{
		"invited_by_user_id": inviteUserID,
	})
}

// === 余额相关方法 ===
// GetUserBalance 获取用户余额信息（别名方法，保持兼容性）
func (s *UserService) GetUserBalance(userID int64) (*model.User, error) {
	return s.GetUserInfo(userID)
}

// CanSignInToday 检查用户今天是否可以签到
func (s *UserService) CanSignInToday(userInfo *model.User) bool {
	currentTime := time.Now()
	return currentTime.Sub(userInfo.DailyPointLastResetAt) >= 18*time.Hour
}

// GetEffectiveVipBalance 获取有效的VIP积分余额（考虑过期时间）
func (s *UserService) GetEffectiveVipBalance(userInfo *model.User) int32 {
	currentTime := time.Now()
	if userInfo.VipPointExpiredDate.Before(currentTime) {
		return 0
	}
	return userInfo.VipPointBalance
}

// GetEffectiveBalances 获取有效的积分余额（考虑签到和VIP过期）
func (s *UserService) GetEffectiveBalances(userInfo *model.User) (dailyBalance, vipBalance, permanentBalance int32) {
	currentTime := time.Now()

	// 检查是否需要自动签到
	dailyBalance = userInfo.DailyPointBalance
	if s.CanSignInToday(userInfo) {
		dailyBalance = s.svcCtx.Config.Operation.DailyPoints
	}

	// 检查VIP是否过期
	vipBalance = userInfo.VipPointBalance
	if userInfo.VipPointExpiredDate.Before(currentTime) {
		vipBalance = 0
	}

	permanentBalance = userInfo.PermanentPointBalance
	return
}

// CheckAndUpdateExpiredVip 检查并更新过期的VIP积分（带事务）
func (s *UserService) CheckAndUpdateExpiredVip(userID int64) error {
	return s.svcCtx.UserModel.Trans(s.ctx, func(tx *gorm.DB) error {
		// 获取用户信息并加行锁
		userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
		if err != nil {
			return fmt.Errorf("获取用户信息失败: %v", err)
		}

		currentTime := time.Now()
		// 如果VIP已过期且VIP积分不为0，则清零VIP积分
		if userInfo.VipPointExpiredDate.Before(currentTime) && userInfo.VipPointBalance > 0 {
			oldVipBalance := userInfo.VipPointBalance

			err = s.svcCtx.UserModel.UpdateUserFields(s.ctx, tx, userID, map[string]any{
				"vip_point_balance": 0,
			})
			if err != nil {
				return fmt.Errorf("更新VIP积分失败: %v", err)
			}

			// 异步记录VIP过期日志
			go func() {
				defer func() {
					if r := recover(); r != nil {
						logx.Errorf("记录VIP过期日志时发生panic: %v", r)
					}
				}()

				ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
				defer cancel()

				pointChange := &model.PointChange{
					Daily:     0,
					VIP:       -oldVipBalance, // 负数表示扣除
					Permanent: 0,
				}

				err := s.svcCtx.UserConsumptionLogModel.LogSystemOperation(ctx, nil, &model.SystemLogParams{
					SessionID:   nil,
					UserID:      userID,
					ChatID:      userInfo.ChatID,
					MessageID:   nil,
					Operation:   "vip_expired",
					Description: &[]string{"VIP积分过期自动清零"}[0],
					ExtraData: map[string]interface{}{
						"expired_vip_points": oldVipBalance,
						"expired_date":       userInfo.VipPointExpiredDate,
						"point_change":       pointChange,
					},
				})
				if err != nil {
					// 日志记录失败不影响主流程，只记录错误
					logx.Errorf("记录VIP过期日志失败: %v", err)
				}
			}()
		}

		return nil
	})
}

// CheckAndSignIn 执行签到操作（带事务）
func (s *UserService) CheckAndSignIn(userID int64) error {
	return s.svcCtx.UserModel.Trans(s.ctx, func(tx *gorm.DB) error {
		// 获取用户信息并加行锁
		userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
		if err != nil {
			return fmt.Errorf("获取用户信息失败: %v", err)
		}

		// 检查是否可以签到
		if !s.CanSignInToday(userInfo) {
			return ErrAlreadySignedIn
		}

		// 执行签到
		err = s.svcCtx.UserModel.UpdateUserFields(s.ctx, tx, userID, map[string]any{
			"daily_point_balance":       s.svcCtx.Config.Operation.DailyPoints,
			"daily_point_last_reset_at": time.Now(),
		})
		if err != nil {
			return fmt.Errorf("更新用户信息失败: %v", err)
		}

		// 记录签到日志
		pointChange := &model.PointChange{
			Daily:     s.svcCtx.Config.Operation.DailyPoints,
			VIP:       0,
			Permanent: 0,
		}

		err = s.svcCtx.UserConsumptionLogModel.LogSignIn(s.ctx, tx, &model.SignInLogParams{
			SessionID:   nil, // 签到操作通常没有具体的会话上下文
			UserID:      userID,
			ChatID:      userInfo.ChatID,
			MessageID:   nil,
			PointChange: pointChange,
		})
		if err != nil {
			// 日志记录失败不影响主流程，只记录错误
			s.Errorf("记录签到日志失败: %v", err)
		}

		return nil
	})
}

// ProcessInviteReward 处理邀请奖励（给邀请人和被邀请人发放奖励）
func (s *UserService) ProcessInviteReward(inviterID, inviteeID int64, sessionID *string, chatID *int64, messageID *int64) error {
	return s.svcCtx.UserModel.Trans(s.ctx, func(tx *gorm.DB) error {
		// 1. 给被邀请人发放奖励
		if s.hasInviteeReward() {
			err := s.giveInviteeReward(tx, inviteeID, inviterID, sessionID, chatID, messageID)
			if err != nil {
				return fmt.Errorf("给被邀请人发放奖励失败: %v", err)
			}
		}

		// 2. 给邀请人发放奖励
		if s.hasInviterReward() {
			err := s.giveInviterReward(tx, inviterID, inviteeID, sessionID, chatID, messageID)
			if err != nil {
				return fmt.Errorf("给邀请人发放奖励失败: %v", err)
			}
		}

		return nil
	})
}

// hasInviteeReward 检查是否有被邀请人奖励
func (s *UserService) hasInviteeReward() bool {
	config := s.svcCtx.Config.Operation.InviteReward
	return config.InviteeVipDays > 0 || config.InviteeVipPoints > 0 ||
		config.InviteeDailyPoints > 0 || config.InviteePermanentPoints > 0
}

// hasInviterReward 检查是否有邀请人奖励
func (s *UserService) hasInviterReward() bool {
	config := s.svcCtx.Config.Operation.InviteReward
	return config.InviterVipDays > 0 || config.InviterVipPoints > 0 ||
		config.InviterDailyPoints > 0 || config.InviterPermanentPoints > 0
}

// giveInviteeReward 给被邀请人发放奖励
func (s *UserService) giveInviteeReward(tx *gorm.DB, inviteeID, inviterID int64, sessionID *string, chatID *int64, messageID *int64) error {
	config := s.svcCtx.Config.Operation.InviteReward

	// 获取被邀请人信息并加行锁
	inviteeInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, inviteeID)
	if err != nil {
		return fmt.Errorf("获取被邀请人信息失败: %v", err)
	}

	// 计算奖励
	var vipDaysChange *int32
	if config.InviteeVipDays > 0 {
		now := time.Now()
		if inviteeInfo.VipPointExpiredDate.Before(now) {
			inviteeInfo.VipPointExpiredDate = now.AddDate(0, 0, int(config.InviteeVipDays))
		} else {
			inviteeInfo.VipPointExpiredDate = inviteeInfo.VipPointExpiredDate.AddDate(0, 0, int(config.InviteeVipDays))
		}
		vipDaysChange = &config.InviteeVipDays
	}

	// 增加积分
	inviteeInfo.DailyPointBalance += config.InviteeDailyPoints
	inviteeInfo.VipPointBalance += config.InviteeVipPoints
	inviteeInfo.PermanentPointBalance += config.InviteePermanentPoints

	// 更新被邀请人信息
	err = s.svcCtx.UserModel.Update(s.ctx, tx, inviteeInfo)
	if err != nil {
		return fmt.Errorf("更新被邀请人信息失败: %v", err)
	}

	// 记录被邀请人奖励日志
	pointChange := &model.PointChange{
		Daily:     config.InviteeDailyPoints,
		VIP:       config.InviteeVipPoints,
		Permanent: config.InviteePermanentPoints,
	}

	err = s.svcCtx.UserConsumptionLogModel.LogInviteReward(s.ctx, tx, &model.InviteRewardLogParams{
		SessionID:     sessionID,
		UserID:        inviteeID,
		ChatID:        chatID,
		MessageID:     messageID,
		InviterID:     &inviterID,
		RewardType:    "invitee_reward",
		PointChange:   pointChange,
		VipDaysChange: vipDaysChange,
		ExtraData: map[string]interface{}{
			"daily_points_reward":     config.InviteeDailyPoints,
			"vip_points_reward":       config.InviteeVipPoints,
			"permanent_points_reward": config.InviteePermanentPoints,
			"vip_days_reward":         config.InviteeVipDays,
			"event_type":              "new_user_registration_reward",
		},
	})
	if err != nil {
		// 日志记录失败不影响主流程，只记录错误
		s.Errorf("记录被邀请人奖励日志失败: %v", err)
	}

	return nil
}

// giveInviterReward 给邀请人发放奖励
func (s *UserService) giveInviterReward(tx *gorm.DB, inviterID, inviteeID int64, sessionID *string, chatID *int64, messageID *int64) error {
	config := s.svcCtx.Config.Operation.InviteReward

	// 获取邀请人信息并加行锁
	inviterInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, inviterID)
	if err != nil {
		return fmt.Errorf("获取邀请人信息失败: %v", err)
	}

	// 计算奖励
	var vipDaysChange *int32
	if config.InviterVipDays > 0 {
		now := time.Now()
		if inviterInfo.VipPointExpiredDate.Before(now) {
			inviterInfo.VipPointExpiredDate = now.AddDate(0, 0, int(config.InviterVipDays))
		} else {
			inviterInfo.VipPointExpiredDate = inviterInfo.VipPointExpiredDate.AddDate(0, 0, int(config.InviterVipDays))
		}
		vipDaysChange = &config.InviterVipDays
	}

	// 增加积分
	inviterInfo.DailyPointBalance += config.InviterDailyPoints
	inviterInfo.VipPointBalance += config.InviterVipPoints
	inviterInfo.PermanentPointBalance += config.InviterPermanentPoints

	// 更新邀请人信息
	err = s.svcCtx.UserModel.Update(s.ctx, tx, inviterInfo)
	if err != nil {
		return fmt.Errorf("更新邀请人信息失败: %v", err)
	}

	// 记录邀请人奖励日志
	pointChange := &model.PointChange{
		Daily:     config.InviterDailyPoints,
		VIP:       config.InviterVipPoints,
		Permanent: config.InviterPermanentPoints,
	}

	err = s.svcCtx.UserConsumptionLogModel.LogInviteReward(s.ctx, tx, &model.InviteRewardLogParams{
		SessionID:     sessionID,
		UserID:        inviterID,
		ChatID:        chatID,
		MessageID:     messageID,
		InviteeID:     &inviteeID,
		RewardType:    "inviter_reward",
		PointChange:   pointChange,
		VipDaysChange: vipDaysChange,
		ExtraData: map[string]interface{}{
			"daily_points_reward":     config.InviterDailyPoints,
			"vip_points_reward":       config.InviterVipPoints,
			"permanent_points_reward": config.InviterPermanentPoints,
			"vip_days_reward":         config.InviterVipDays,
			"event_type":              "successful_invitation_reward",
		},
	})
	if err != nil {
		// 日志记录失败不影响主流程，只记录错误
		s.Errorf("记录邀请人奖励日志失败: %v", err)
	}

	return nil
}
