package ai

import (
	"encoding/base64"
	"fmt"
	"strings"
	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// SDWithConfig 使用指定配置处理SD命令（图片生成）
func (s *AIService) SDWithConfig(c tele.Context, commandConfig *config.AICommand) error {
	startTime := time.Now()
	var usage *BalanceUsage
	var needCompensate = true
	var botResponse string

	// 延迟处理：记录消费日志和补偿积分
	defer func() {
		// 如果需要补偿积分（发生错误时）
		if needCompensate && usage != nil {
			if err := s.addBalance(c.Sender().ID, usage); err != nil {
				s.Errorf("Failed to compensate user balance: %v", err)
			}
		}

		// 只有成功时才记录消费日志
		if !needCompensate && usage != nil {
			chatID := utils.GetChatID(c)
			messageID := int64(c.Message().ID)

			// 获取用户输入的文本作为用户消息
			userMessage := c.Text()
			if userMessage == "" && c.Message() != nil && c.Message().Caption != "" {
				userMessage = c.Message().Caption
			}

			pointChange := &model.PointChange{
				Daily:     -usage.Daily, // 负数表示消费
				VIP:       -usage.VIP,
				Permanent: -usage.Permanent,
			}

			extraData := map[string]interface{}{
				"command_type": commandConfig.Type,
				"cost":         commandConfig.Cost,
				"success":      true,
				"process_time": time.Since(startTime).Milliseconds(),
				"prompt":       s.extractPromptFromText(userMessage), // 记录用户的提示词
			}

			params := &model.ConsumptionLogParams{
				UserID:      c.Sender().ID,
				ChatID:      &chatID,
				MessageID:   &messageID,
				UserMessage: &userMessage, // 记录用户发送的完整消息
				BotResponse: &botResponse,
				PointChange: pointChange,
				ExtraData:   extraData,
			}

			if err := s.svcCtx.UserConsumptionLogModel.LogConsumption(s.ctx, nil, params); err != nil {
				s.Errorf("Failed to log consumption: %v", err)
			}
		}
	}()

	// 1. 扣费
	var err error
	usage, err = s.useBalance(c.Sender().ID, commandConfig.Cost)
	if err != nil {
		if err == ErrInsufficientBalance {
			userInfo, err := s.svcCtx.UserModel.FindOneByUserID(s.ctx, c.Sender().ID)
			if err != nil {
				return c.Reply(utils.GetGeneralError(c))
			}

			return c.Reply(utils.GetI18nTextWithArgs(c, i18n.Keys.Common.Errors.InsufficientBalance, map[string]interface{}{
				"cost":    commandConfig.Cost,
				"balance": userInfo.DailyPointBalance + userInfo.VipPointBalance + userInfo.PermanentPointBalance,
			}))
		} else {
			s.Errorf("Failed to deduct balance: %v", err)
			return c.Reply(utils.GetGeneralError(c))
		}
	}

	// 2. 开始持续的"正在输入"状态
	stopTyping := s.startContinuousTyping(c)
	defer stopTyping()

	// 3. 提取提示词
	text := c.Text()
	if text == "" && c.Message() != nil && c.Message().Caption != "" {
		text = c.Message().Caption
	}

	// 清理命令前缀
	prompt := s.extractPromptFromText(text)
	if prompt == "" {
		return c.Reply(utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.NoPrompt, map[string]interface{}{
			"command": "/draw",
		}))
	}

	// 4. 发送处理中的消息
	processingMsg := utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.Processing, map[string]interface{}{
		"prompt": prompt,
	})
	c.Reply(processingMsg)

	// 5. 调用Segmind API生成图片
	req := &request.PlaygroundV25Request{
		Prompt:            prompt,
		NegativePrompt:    "",
		Samples:           1,
		NumInferenceSteps: 25,
		GuidanceScale:     3.0,
		Seed:              -1,
		Base64:            true,
	}

	response, err := s.svcCtx.SegmindClient.GenerateImage(s.ctx, req)
	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.Failed, map[string]interface{}{
			"error": err.Error(),
		})
		return c.Reply(botResponse)
	}

	if response.Image == "" {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.Failed, map[string]interface{}{
			"error": "get empty image response",
		})
		return c.Reply(botResponse)
	}

	// 6. 解码base64图片
	imageData, err := base64.StdEncoding.DecodeString(response.Image)
	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.Failed, map[string]interface{}{
			"error": fmt.Sprintf("failed to decode image: %v", err),
		})
		return c.Reply(botResponse)
	}

	// 7. 获取广告内容
	adText, err := GetChatAdsText(s.svcCtx, s.ctx, utils.GetChatID(c))
	if err != nil {
		s.Errorf("Failed to get ads text: %v", err)
		// 广告获取失败不影响主流程，继续执行
	}

	// 获取广告按钮
	adButtons, err := GetChatButtonsMarkup(s.svcCtx, s.ctx, utils.GetChatID(c))
	if err != nil {
		s.Errorf("Failed to get ads buttons: %v", err)
		// 广告按钮获取失败不影响主流程，继续执行
	}

	// 8. 发送图片，包含广告文本和按钮
	photo := &tele.Photo{File: tele.FromReader(strings.NewReader(string(imageData)))}

	// 设置图片标题（包含广告文本）
	if adText != "" {
		photo.Caption = adText
	}

	// 设置发送选项
	sendOptions := &tele.SendOptions{}
	if adButtons != nil && len(adButtons.InlineKeyboard) > 0 {
		sendOptions.ReplyMarkup = adButtons
	}

	err = c.Reply(photo, sendOptions)

	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Draw.Failed, map[string]interface{}{
			"error": fmt.Sprintf("failed to send image: %v", err),
		})
		return c.Reply(botResponse)
	}

	// 成功完成，设置响应内容并标记不需要补偿积分
	botResponse = utils.GetI18nText(c, i18n.Keys.AICommand.Draw.Success)
	needCompensate = false

	return nil
}

// extractPromptFromText 从文本中提取提示词
func (s *AIService) extractPromptFromText(text string) string {
	// 移除常见的命令前缀
	prefixes := []string{"/draw", "draw", "/sd", "sd", "/生图", "生图", "/d", "d"}

	for _, prefix := range prefixes {
		if strings.HasPrefix(strings.ToLower(text), strings.ToLower(prefix)) {
			// 移除前缀并清理空格
			remaining := strings.TrimSpace(text[len(prefix):])
			if remaining != "" {
				return remaining
			}
		}
	}

	// 如果没有匹配的前缀，返回原文本
	return strings.TrimSpace(text)
}
