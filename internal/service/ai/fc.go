package ai

import (
	"encoding/base64"
	"fmt"
	"strings"
	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// FCWithConfig 使用指定配置处理FC命令（换脸）
func (s *AIService) FCWithConfig(c tele.Context, commandConfig *config.AICommand) error {
	startTime := time.Now()
	var usage *BalanceUsage
	var needCompensate = true
	var botResponse string

	// 延迟处理：记录消费日志和补偿积分
	defer func() {
		// 如果需要补偿积分（发生错误时）
		if needCompensate && usage != nil {
			if err := s.addBalance(c.Sender().ID, usage); err != nil {
				s.<PERSON><PERSON>rf("Failed to compensate user balance: %v", err)
			}
		}

		// 只有成功时才记录消费日志
		if !needCompensate && usage != nil {
			chatID := utils.GetChatID(c)
			messageID := int64(c.Message().ID)

			// 获取用户输入的文本作为用户消息
			userMessage := c.Text()
			if userMessage == "" && c.Message() != nil && c.Message().Caption != "" {
				userMessage = c.Message().Caption
			}

			pointChange := &model.PointChange{
				Daily:     -usage.Daily, // 负数表示消费
				VIP:       -usage.VIP,
				Permanent: -usage.Permanent,
			}

			extraData := map[string]interface{}{
				"command_type": commandConfig.Type,
				"cost":         commandConfig.Cost,
				"success":      true,
				"process_time": time.Since(startTime).Milliseconds(),
				"operation":    "face_swap", // 记录操作类型
			}

			params := &model.ConsumptionLogParams{
				UserID:      c.Sender().ID,
				ChatID:      &chatID,
				MessageID:   &messageID,
				UserMessage: &userMessage, // 记录用户发送的完整消息
				BotResponse: &botResponse,
				PointChange: pointChange,
				ExtraData:   extraData,
			}

			if err := s.svcCtx.UserConsumptionLogModel.LogConsumption(s.ctx, nil, params); err != nil {
				s.Errorf("Failed to log consumption: %v", err)
			}
		}
	}()

	// 1. 检查是否需要审核
	if s.needsModeration(c, commandConfig) {
		moderationResult, err := s.moderateImages(c)
		if err != nil {
			botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.Moderation.Failed, map[string]interface{}{
				"error": err.Error(),
			})
			return c.Reply(botResponse)
		}
		if !moderationResult {
			botResponse = utils.GetI18nText(c, i18n.Keys.AICommand.Moderation.Rejected)
			return c.Reply(botResponse)
		}
		c.Reply(utils.GetI18nText(c, i18n.Keys.AICommand.Moderation.Passed))
	}

	// 2. 扣费
	var err error
	usage, err = s.useBalance(c.Sender().ID, commandConfig.Cost)
	if err != nil {
		if err == ErrInsufficientBalance {
			userInfo, err := s.svcCtx.UserModel.FindOneByUserID(s.ctx, c.Sender().ID)
			if err != nil {
				return c.Reply(utils.GetGeneralError(c))
			}

			return c.Reply(utils.GetI18nTextWithArgs(c, i18n.Keys.Common.Errors.InsufficientBalance, map[string]interface{}{
				"cost":    commandConfig.Cost,
				"balance": userInfo.DailyPointBalance + userInfo.VipPointBalance + userInfo.PermanentPointBalance,
			}))
		} else {
			s.Errorf("Failed to deduct balance: %v", err)
			return c.Reply(utils.GetGeneralError(c))
		}
	}

	// 3. 开始持续的"正在输入"状态
	stopTyping := s.startContinuousTyping(c)
	defer stopTyping()

	// 4. 获取图片URLs
	imageURLs, err := s.extractImageURLs(c)
	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.Failed, map[string]interface{}{
			"error": fmt.Sprintf("failed to extract image urls: %v", err),
		})
		return c.Reply(botResponse)
	}

	if len(imageURLs) < 2 {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.NeedTwoPhoto, map[string]interface{}{
			"command": "/fc",
		})
		return c.Reply(botResponse)
	}

	// 5. 发送处理中的消息
	c.Reply(utils.GetI18nText(c, i18n.Keys.AICommand.FC.Processing))

	// 6. 调用Segmind API进行换脸
	req := &request.FaceSwapRequest{
		SourceImg:        imageURLs[0], // 源脸部图片
		TargetImg:        imageURLs[1], // 目标图片
		InputFacesIndex:  0,
		SourceFacesIndex: 0,
		FaceRestore:      "codeformer-v0.1.0.pth",
		Base64:           true,
	}

	response, err := s.svcCtx.SegmindClient.FaceSwap(s.ctx, req)
	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.Failed, map[string]interface{}{
			"error": err.Error(),
		})
		return c.Reply(botResponse)
	}

	if response.Image == "" {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.Failed, map[string]interface{}{
			"error": "get empty image response",
		})
		return c.Reply(botResponse)
	}

	// 7. 解码base64图片
	imageData, err := base64.StdEncoding.DecodeString(response.Image)
	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.Failed, map[string]interface{}{
			"error": fmt.Sprintf("failed to decode image: %v", err),
		})
		return c.Reply(botResponse)
	}

	// 8. 获取广告内容
	adText, err := GetChatAdsText(s.svcCtx, s.ctx, utils.GetChatID(c))
	if err != nil {
		s.Errorf("Failed to get ads text: %v", err)
		// 广告获取失败不影响主流程，继续执行
	}

	// 获取广告按钮
	adButtons, err := GetChatButtonsMarkup(s.svcCtx, s.ctx, utils.GetChatID(c))
	if err != nil {
		s.Errorf("Failed to get ads buttons: %v", err)
		// 广告按钮获取失败不影响主流程，继续执行
	}

	// 9. 发送图片，包含广告文本和按钮
	photo := &tele.Photo{File: tele.FromReader(strings.NewReader(string(imageData)))}

	// 设置图片标题（包含广告文本）
	if adText != "" {
		photo.Caption = adText
	}

	// 设置发送选项
	sendOptions := &tele.SendOptions{}
	if adButtons != nil && len(adButtons.InlineKeyboard) > 0 {
		sendOptions.ReplyMarkup = adButtons
	}

	err = c.Reply(photo, sendOptions)

	if err != nil {
		botResponse = utils.GetI18nTextWithArgs(c, i18n.Keys.AICommand.FC.Failed, map[string]interface{}{
			"error": fmt.Sprintf("failed to send image: %v", err),
		})
		return c.Reply(botResponse)
	}

	// 成功完成，设置响应内容并标记不需要补偿积分
	botResponse = utils.GetI18nText(c, i18n.Keys.AICommand.FC.Success)
	needCompensate = false

	return nil
}

// extractImageURLs 从消息中提取图片URLs
func (s *AIService) extractImageURLs(c tele.Context) ([]string, error) {
	var imageURLs []string

	// 检查是否是媒体组消息
	if mediaGroup := utils.GetMediaGroupFromContext(c); mediaGroup != nil && len(mediaGroup.Messages) > 0 {
		for _, media := range mediaGroup.Messages {
			if media.MediaType == "photo" {
				// 创建File对象并获取URL
				file := tele.File{FileID: media.FileID}
				fileReader, err := c.Bot().File(&file)
				if err != nil {
					return nil, fmt.Errorf("failed to get image file: %v", err)
				}
				fileReader.Close()

				// 构建图片URL
				imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
				imageURLs = append(imageURLs, imageURL)
			}
		}
		return imageURLs, nil
	}

	// 检查单张图片
	if c.Message().Photo != nil {
		file := tele.File{FileID: c.Message().Photo.FileID}
		fileReader, err := c.Bot().File(&file)
		if err != nil {
			return nil, fmt.Errorf("failed to get image file: %v", err)
		}
		fileReader.Close()

		imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
		imageURLs = append(imageURLs, imageURL)
	}

	// 检查回复的消息中是否有图片
	if c.Message().ReplyTo != nil && c.Message().ReplyTo.Photo != nil {
		file := tele.File{FileID: c.Message().ReplyTo.Photo.FileID}
		fileReader, err := c.Bot().File(&file)
		if err != nil {
			return nil, fmt.Errorf("failed to get reply image file: %v", err)
		}
		fileReader.Close()

		imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
		imageURLs = append(imageURLs, imageURL)
	}

	return imageURLs, nil
}
