package ai

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"
	"sync"
	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/types"
	"telegram-chatbot-go/internal/utils"
	"time"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// AIService AI功能服务
type AIService struct {
	logx.Logger
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

// NewAIService 创建AI服务
func NewAIService(svcCtx *svc.ServiceContext, ctx context.Context) *AIService {
	return &AIService{
		Logger: logx.WithContext(ctx),
		svcCtx: svcCtx,
		ctx:    ctx,
	}
}

// BalanceUsage 余额使用详情
type BalanceUsage struct {
	Daily     int32 // 使用的每日积分
	VIP       int32 // 使用的VIP积分
	Permanent int32 // 使用的永久积分
}

var ErrInsufficientBalance = errors.New("余额不足")

// MessageContent 统一的消息内容表示
type MessageContent struct {
	Text   string   `json:"text"`
	Images []string `json:"images"` // base64 encoded images
}

// ConversationMessage 对话消息
type ConversationMessage struct {
	Role    string         `json:"role"` // "user", "assistant", "system"
	Content MessageContent `json:"content"`
}

// ConversationContext 对话上下文
type ConversationContext struct {
	Messages     []ConversationMessage `json:"messages"`
	SystemPrompt string                `json:"system_prompt"`
	Config       *ChatConfig           `json:"config"`
}

// ChatConfig 聊天配置
type ChatConfig struct {
	Model              string  `json:"model"`
	MaxTokens          int     `json:"max_tokens"`
	Temperature        float32 `json:"temperature"`
	UseContext         bool    `json:"use_context"`
	MaxContextMessages int     `json:"max_context_messages"`
	SystemPrompt       string  `json:"system_prompt"` // 直接传入系统提示词
}

// startContinuousTyping 启动持续的"正在输入"状态
// 返回一个停止函数
func (s *AIService) startContinuousTyping(c tele.Context) func() {
	if c == nil || c.Chat() == nil {
		// 如果context无效，返回一个空的停止函数
		return func() {}
	}

	var wg sync.WaitGroup
	stop := make(chan bool, 1)

	wg.Add(1)
	go func() {
		defer func() {
			// 防止goroutine中的panic
			if r := recover(); r != nil {
				s.Errorf("typing goroutine panic recovered: %v", r)
			}
			wg.Done()
		}()

		// 立即发送第一次
		c.Notify(tele.Typing)

		ticker := time.NewTicker(4 * time.Second) // 每4秒发送一次
		defer ticker.Stop()

		for {
			select {
			case <-stop:
				return
			case <-ticker.C:
				if err := c.Notify(tele.Typing); err != nil {
					// 如果发送失败（比如用户删除了对话），停止发送
					s.Errorf("发送typing状态失败: %v", err)
					return
				}
			}
		}
	}()

	// 返回停止函数
	return func() {
		defer func() {
			// 防止停止函数中的panic
			if r := recover(); r != nil {
				s.Errorf("stop typing function panic recovered: %v", r)
			}
		}()

		// 安全地发送停止信号
		select {
		case stop <- true:
		default:
			// channel已满或已关闭，避免阻塞
		}

		wg.Wait()
	}
}

// buildMessageContext 构建消息上下文链
// 通过reply_to_message_id递归查找消息链，最多返回maxMessages条消息
func (s *AIService) buildMessageContext(c tele.Context) []*model.ChatMessage {
	var contextMessages []*model.ChatMessage
	chatID := utils.GetChatID(c)

	// 从当前消息的回复开始
	currentMsg := c.Message()

	// 当前消息ID
	var currentMsgID int64 = int64(currentMsg.ID)

	// 只要有回复消息且未达到最大消息数，就继续查找
	for currentMsg.ReplyTo != nil && len(contextMessages) < s.svcCtx.Config.AI.OpenAI.HistoryLimit {
		// 查找被回复的消息
		replyToID := int64(currentMsg.ReplyTo.ID)

		// 从数据库中查找该消息
		msg, err := s.svcCtx.ChatMessageModel.FindByMessageID(s.ctx, chatID, replyToID)
		if err != nil {
			// 消息不存在，中断链
			break
		}

		// 将消息添加到上下文
		contextMessages = append(contextMessages, msg)

		// 继续查找更早的消息
		if msg.ReplyToMessageID == nil {
			// 没有更早的回复了
			break
		}

		// 更新当前消息为下一个要处理的消息
		currentMsgID = *msg.MessageID

		// 构造一个临时的telebot.Message对象，用于下一次循环
		currentMsg = &tele.Message{
			ID: int(currentMsgID),
			ReplyTo: &tele.Message{
				ID: int(*msg.ReplyToMessageID),
			},
		}
	}

	// 反转切片，使消息按时间顺序排列（最早的在前）
	for i, j := 0, len(contextMessages)-1; i < j; i, j = i+1, j-1 {
		contextMessages[i], contextMessages[j] = contextMessages[j], contextMessages[i]
	}

	return contextMessages
}

// downloadAndConvertImage 下载图片并转换为base64
func (s *AIService) downloadAndConvertImage(c tele.Context, fileID string) (string, error) {
	// 创建File对象
	file := tele.File{FileID: fileID}

	// 下载图片文件
	fileReader, err := c.Bot().File(&file)
	if err != nil {
		return "", err
	}
	defer fileReader.Close()

	// 读取文件内容
	fileData, err := io.ReadAll(fileReader)
	if err != nil {
		return "", err
	}

	// 转换为base64编码
	return base64.StdEncoding.EncodeToString(fileData), nil
}

// ===== 核心抽象方法 =====

// GetDefaultChatConfig 获取默认聊天配置
func (s *AIService) GetDefaultChatConfig() *ChatConfig {
	return &ChatConfig{
		Model:              s.svcCtx.Config.AI.OpenAI.Model,
		MaxTokens:          4096, // 默认最大token数
		Temperature:        0.7,  // 默认温度
		UseContext:         true,
		MaxContextMessages: s.svcCtx.Config.AI.OpenAI.HistoryLimit,
		SystemPrompt:       "", // 外部设置
	}
}

// NewChatConfig 创建自定义聊天配置
func NewChatConfig(model string, maxTokens int, temperature float32, useContext bool, maxContextMessages int, systemPrompt string) *ChatConfig {
	return &ChatConfig{
		Model:              model,
		MaxTokens:          maxTokens,
		Temperature:        temperature,
		UseContext:         useContext,
		MaxContextMessages: maxContextMessages,
		SystemPrompt:       systemPrompt,
	}
}

// ExtractCurrentMessage 从Telegram Context提取当前消息内容
func (s *AIService) ExtractCurrentMessage(c tele.Context) (*MessageContent, error) {
	content := &MessageContent{
		Images: make([]string, 0),
	}

	// 检查是否是媒体组消息
	if mediaGroup := types.GetMediaGroupFromContext(c); mediaGroup != nil && len(mediaGroup.Messages) > 0 {
		// 处理媒体组中的图片
		for _, media := range mediaGroup.Messages {
			if media.MediaType == "photo" {
				base64Image, err := s.downloadAndConvertImage(c, media.FileID)
				if err != nil {
					s.Errorf("下载媒体组图片失败: %v", err)
					continue
				}
				content.Images = append(content.Images, base64Image)
			}
		}

		// 使用媒体组的标题作为文本
		content.Text = mediaGroup.Caption
		if content.Text == "" && len(content.Images) > 0 {
			content.Text = "请描述这些图片"
		}

		return content, nil
	}

	// 检查是否是单张图片消息
	if c.Message().Photo != nil {
		base64Image, err := s.downloadAndConvertImage(c, c.Message().Photo.FileID)
		if err != nil {
			return nil, fmt.Errorf("failed to download image: %v", err)
		}
		content.Images = append(content.Images, base64Image)

		// 使用图片标题作为文本
		content.Text = c.Message().Caption
		if content.Text == "" {
			content.Text = "Please describe this image"
		}

		return content, nil
	}

	// 处理文本消息
	text := c.Text()

	// 智能清理命令前缀
	text = s.cleanCommandPrefix(c, text)

	if text == "" {
		return nil, fmt.Errorf("no message content")
	}

	content.Text = text
	return content, nil
}

// BuildConversationContext 构建完整的对话上下文（包含历史消息和图片）
func (s *AIService) BuildConversationContext(c tele.Context, config *ChatConfig) (*ConversationContext, error) {
	// 使用配置中的系统提示词
	systemPrompt := config.SystemPrompt

	// 创建对话上下文
	ctx := &ConversationContext{
		Messages:     make([]ConversationMessage, 0),
		SystemPrompt: systemPrompt,
		Config:       config,
	}

	// 如果启用上下文且是回复消息，构建历史对话
	if config.UseContext && c.Message().ReplyTo != nil {
		// 获取历史消息
		historyMessages := s.buildMessageContext(c)

		// 转换历史消息为对话格式
		for _, msg := range historyMessages {
			role := "user"
			if msg.FromType == "bot" {
				role = "assistant"
			}

			// 构建消息内容
			content := MessageContent{
				Images: make([]string, 0),
			}

			// 添加文本内容
			if msg.MessageText != nil {
				content.Text = *msg.MessageText
			}

			// 处理图片内容
			if photoFileIDs, err := msg.GetPhotoURLs(); err == nil && len(photoFileIDs) > 0 {
				for _, fileID := range photoFileIDs {
					if base64Image, err := s.downloadAndConvertImage(c, fileID); err == nil {
						content.Images = append(content.Images, base64Image)
					} else {
						s.Errorf("Failed to download history image (FileID: %s): %v", fileID, err)
					}
				}
			}

			// 添加到对话上下文
			ctx.Messages = append(ctx.Messages, ConversationMessage{
				Role:    role,
				Content: content,
			})
		}
	}

	// 添加当前消息
	currentContent, err := s.ExtractCurrentMessage(c)
	if err != nil {
		return nil, fmt.Errorf("failed to extract current message: %v", err)
	}

	ctx.Messages = append(ctx.Messages, ConversationMessage{
		Role:    "user",
		Content: *currentContent,
	})

	return ctx, nil
}

// ConvertToOpenAIMessages 将对话上下文转换为OpenAI API格式
func (s *AIService) ConvertToOpenAIMessages(ctx *ConversationContext) ([]openai.ChatCompletionMessage, error) {
	messages := make([]openai.ChatCompletionMessage, 0)

	// 添加系统提示词
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleSystem,
		Content: ctx.SystemPrompt,
	})

	// 转换对话消息
	for _, msg := range ctx.Messages {
		var role string
		switch msg.Role {
		case "user":
			role = openai.ChatMessageRoleUser
		case "assistant":
			role = openai.ChatMessageRoleAssistant
		default:
			continue // 跳过未知角色
		}

		// 如果有图片，使用MultiContent格式
		if len(msg.Content.Images) > 0 {
			multiContent := make([]openai.ChatMessagePart, 0)

			// 添加文本部分
			if msg.Content.Text != "" {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeText,
					Text: msg.Content.Text,
				})
			}

			// 添加图片部分
			for _, base64Image := range msg.Content.Images {
				multiContent = append(multiContent, openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL: "data:image/jpeg;base64," + base64Image,
					},
				})
			}

			messages = append(messages, openai.ChatCompletionMessage{
				Role:         role,
				MultiContent: multiContent,
			})
		} else {
			// 纯文本消息
			messages = append(messages, openai.ChatCompletionMessage{
				Role:    role,
				Content: msg.Content.Text,
			})
		}
	}

	return messages, nil
}

// ChatWithConversation 统一的对话接口
func (s *AIService) ChatWithConversation(ctx *ConversationContext) (string, error) {
	// 转换为OpenAI消息格式
	messages, err := s.ConvertToOpenAIMessages(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to convert message format: %v", err)
	}

	// 调用OpenAI API
	response, err := s.svcCtx.OpenAIClient.ChatWithContext(s.ctx, messages, ctx.Config.Model, ctx.Config.MaxContextMessages, ctx.Config.Temperature)
	if err != nil {
		return "", fmt.Errorf("OpenAI API call failed: %v", err)
	}

	return response, nil
}

// createChatConfigFromCommand 从命令配置创建聊天配置
func (s *AIService) createChatConfigFromCommand(commandConfig *config.AICommand) *ChatConfig {
	chatConfig := s.GetDefaultChatConfig()

	// 使用命令配置中的模型
	if commandConfig.Model != nil && *commandConfig.Model != "" {
		chatConfig.Model = *commandConfig.Model
	}

	// 使用命令配置中的历史限制
	if commandConfig.HistoryLimit > 0 {
		chatConfig.MaxContextMessages = commandConfig.HistoryLimit
	}

	return chatConfig
}

// useBalance 扣除用户余额，按照每日余额、VIP余额、永久余额的顺序扣除
// 返回使用的积分详情和错误
func (s *AIService) useBalance(userID int64, cost int32) (*BalanceUsage, error) {
	// 0. 如果cost为0，直接返回
	if cost == 0 {
		return &BalanceUsage{
			Daily:     0,
			VIP:       0,
			Permanent: 0,
		}, nil
	}

	var usage *BalanceUsage
	var insufficientBalance bool

	// 使用事务确保数据一致性
	err := s.svcCtx.UserModel.Trans(s.ctx, func(tx *gorm.DB) error {
		// 1. 获取用户信息并加行锁
		userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
		if err != nil {
			return fmt.Errorf("failed to get user info: %v", err)
		}

		// 2. 检查用户信息，是否自动签到或者会员到期
		currentTime := time.Now()

		needUpdate := false

		// 自动签到逻辑
		if s.canSignInToday(userInfo) {
			userInfo.DailyPointBalance = s.svcCtx.Config.Operation.DailyPoints
			userInfo.DailyPointLastResetAt = currentTime
			needUpdate = true
		}

		// 获取有效的积分余额（考虑VIP过期）
		dailyBalance := userInfo.DailyPointBalance
		vipBalance := userInfo.VipPointBalance
		permanentBalance := userInfo.PermanentPointBalance

		// 检查VIP是否过期
		var expiredVipPoints int32 = 0
		if userInfo.VipPointExpiredDate.Before(currentTime) {
			if userInfo.VipPointBalance > 0 {
				expiredVipPoints = userInfo.VipPointBalance // 记录过期的VIP积分
				needUpdate = true
			}
			vipBalance = 0
		}

		// 使用defer确保VIP过期日志一定会被记录（无论后续操作是否成功）
		defer func() {
			if expiredVipPoints > 0 {
				go func() {
					defer func() {
						if r := recover(); r != nil {
							logx.Errorf("记录VIP过期日志时发生panic: %v", r)
						}
					}()

					ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
					defer cancel()

					pointChange := &model.PointChange{
						Daily:     0,
						VIP:       -expiredVipPoints, // 负数表示扣除
						Permanent: 0,
					}

					err := s.svcCtx.UserConsumptionLogModel.LogSystemOperation(ctx, nil, &model.SystemLogParams{
						SessionID:   nil,
						UserID:      userID,
						ChatID:      userInfo.ChatID,
						MessageID:   nil,
						Operation:   "vip_expired_during_usage",
						Description: &[]string{"VIP points expired and auto-cleared during AI usage"}[0],
						ExtraData: map[string]interface{}{
							"expired_vip_points": expiredVipPoints,
							"expired_date":       userInfo.VipPointExpiredDate,
							"point_change":       pointChange,
						},
					})
					if err != nil {
						// 日志记录失败不影响主流程，只记录错误
						logx.Errorf("记录VIP过期日志失败: %v", err)
					}
				}()
			}
		}()

		// 3. 检查总余额是否足够
		totalBalance := dailyBalance + vipBalance + permanentBalance
		if totalBalance < cost {
			if needUpdate {
				userInfo.VipPointBalance = vipBalance
				err = s.svcCtx.UserModel.Update(s.ctx, tx, userInfo)
				if err != nil {
					return fmt.Errorf("failed to update user balance: %v", err)
				}
			}

			insufficientBalance = true
			return nil
		}

		// 4. 按顺序扣除余额
		usage = &BalanceUsage{}
		remainingCost := cost

		// 4.1 先扣除每日余额
		if remainingCost > 0 && dailyBalance > 0 {
			deduct := min(remainingCost, dailyBalance)
			usage.Daily = deduct
			remainingCost -= deduct
			dailyBalance -= deduct
		}

		// 4.2 再扣除VIP余额
		if remainingCost > 0 && vipBalance > 0 {
			deduct := min(remainingCost, vipBalance)
			usage.VIP = deduct
			remainingCost -= deduct
			vipBalance -= deduct
		}

		// 4.3 最后扣除永久余额
		if remainingCost > 0 && permanentBalance > 0 {
			deduct := min(remainingCost, permanentBalance)
			usage.Permanent = deduct
			remainingCost -= deduct
			permanentBalance -= deduct
		}

		// 5. 更新用户余额（包括VIP过期清零和正常扣费）
		userInfo.DailyPointBalance = dailyBalance
		userInfo.VipPointBalance = vipBalance
		userInfo.PermanentPointBalance = permanentBalance

		err = s.svcCtx.UserModel.Update(s.ctx, tx, userInfo)
		if err != nil {
			return fmt.Errorf("failed to update user balance: %v", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	if insufficientBalance {
		return nil, ErrInsufficientBalance
	}

	return usage, nil
}

// addBalance 增加用户余额
func (s *AIService) addBalance(userID int64, usage *BalanceUsage) error {
	if usage == nil {
		return nil
	}
	if usage.Daily == 0 && usage.VIP == 0 && usage.Permanent == 0 {
		return nil
	}

	err := s.svcCtx.UserModel.Trans(s.ctx, func(tx *gorm.DB) error {
		userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
		if err != nil {
			return fmt.Errorf("failed to get user info: %v", err)
		}

		userInfo.DailyPointBalance += usage.Daily
		userInfo.VipPointBalance += usage.VIP
		userInfo.PermanentPointBalance += usage.Permanent
		err = s.svcCtx.UserModel.Update(s.ctx, tx, userInfo)
		if err != nil {
			return fmt.Errorf("failed to update user balance: %v", err)
		}

		return nil
	})

	return err
}

// CanSignInToday 检查用户今天是否可以签到
func (s *AIService) canSignInToday(userInfo *model.User) bool {
	currentTime := time.Now()
	return currentTime.Sub(userInfo.DailyPointLastResetAt) >= 18*time.Hour
}

// needsModeration 检查是否需要审核
func (s *AIService) needsModeration(c tele.Context, commandConfig *config.AICommand) bool {
	if commandConfig.Moderation == nil {
		return false
	}

	chatType := utils.GetChatType(c)
	if chatType == "private" {
		return commandConfig.Moderation.Private
	} else {
		return commandConfig.Moderation.Group
	}
}

// moderateImages 审核图片内容
func (s *AIService) moderateImages(c tele.Context) (bool, error) {
	// 检查审核客户端是否可用
	if s.svcCtx.ModerationClient == nil {
		s.Infof("Warning: Moderation client is not available, skipping moderation")
		return true, nil // 如果审核服务不可用，默认通过
	}

	// 获取需要审核的图片URLs
	imageURLs, err := s.extractImageURLsForModeration(c)
	if err != nil {
		return false, fmt.Errorf("failed to extract image URLs for moderation: %v", err)
	}

	if len(imageURLs) == 0 {
		// 没有图片需要审核，直接通过
		return true, nil
	}

	// 逐个审核图片
	for i, imageURL := range imageURLs {
		s.Infof("Moderating image %d/%d: %s", i+1, len(imageURLs), imageURL)

		result, err := s.svcCtx.ModerationClient.ModerateImageSmart(s.ctx, imageURL)
		if err != nil {
			s.Errorf("Image moderation failed for URL %s: %v", imageURL, err)
			return false, fmt.Errorf("image moderation service error: %v", err)
		}

		// 检查审核结果
		if !result.Passed {
			s.Errorf("Image moderation failed for URL %s: %+v", imageURL, result.Results)
			return false, nil // 审核未通过
		}

		s.Infof("Image moderation passed for URL %s", imageURL)
	}

	s.Infof("All %d images passed moderation", len(imageURLs))
	return true, nil // 所有图片都通过审核
}

// extractImageURLsForModeration 提取需要审核的图片URLs
func (s *AIService) extractImageURLsForModeration(c tele.Context) ([]string, error) {
	var imageURLs []string

	// 检查是否是媒体组消息
	if mediaGroup := utils.GetMediaGroupFromContext(c); mediaGroup != nil && len(mediaGroup.Messages) > 0 {
		for _, media := range mediaGroup.Messages {
			if media.MediaType == "photo" {
				// 创建File对象并获取URL
				file := tele.File{FileID: media.FileID}
				fileReader, err := c.Bot().File(&file)
				if err != nil {
					return nil, fmt.Errorf("failed to get image file: %v", err)
				}
				fileReader.Close()

				// 构建图片URL
				imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
				imageURLs = append(imageURLs, imageURL)
			}
		}
		return imageURLs, nil
	}

	// 检查单张图片
	if c.Message().Photo != nil {
		file := tele.File{FileID: c.Message().Photo.FileID}
		fileReader, err := c.Bot().File(&file)
		if err != nil {
			return nil, fmt.Errorf("failed to get image file: %v", err)
		}
		fileReader.Close()

		imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
		imageURLs = append(imageURLs, imageURL)
	}

	// 检查回复的消息中是否有图片
	if c.Message().ReplyTo != nil && c.Message().ReplyTo.Photo != nil {
		file := tele.File{FileID: c.Message().ReplyTo.Photo.FileID}
		fileReader, err := c.Bot().File(&file)
		if err != nil {
			return nil, fmt.Errorf("failed to get reply image file: %v", err)
		}
		fileReader.Close()

		imageURL := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", s.svcCtx.SegmindClient.BotToken, file.FilePath)
		imageURLs = append(imageURLs, imageURL)
	}

	return imageURLs, nil
}

// cleanCommandPrefix 智能清理命令前缀
func (s *AIService) cleanCommandPrefix(c tele.Context, text string) string {
	if text == "" {
		return text
	}

	// 检查是否是空命令场景（私聊或回复机器人消息）
	// 在这些场景下，用户可以直接聊天而不需要命令前缀
	isPrivateChat := c.Chat().Type == tele.ChatPrivate
	isReplyToBot := c.Message().IsReply() && c.Message().ReplyTo.Sender.Username == s.svcCtx.Bot.Me.Username

	// 如果是空命令场景，检查是否配置了空命令("")
	if isPrivateChat || isReplyToBot {
		if emptyCommandConfig, exists := s.svcCtx.Config.AICommands[""]; exists && emptyCommandConfig.Enable {
			// 如果配置了空命令，先尝试匹配具体命令
			// 如果没有匹配到具体命令，则保持原文本不变（用于空命令处理）
			commandConfig, matchedCommand := utils.GetAICommandConfig(text, s.svcCtx.Config.AICommands)
			if commandConfig == nil || matchedCommand == "" {
				// 没有匹配到具体命令，保持原文本用于空命令处理
				return text
			}
			// 匹配到了具体命令，继续下面的前缀清理逻辑
		}
	}

	// 获取匹配的命令配置
	commandConfig, matchedCommand := utils.GetAICommandConfig(text, s.svcCtx.Config.AICommands)
	if commandConfig == nil || matchedCommand == "" {
		// 没有匹配到任何命令，返回原文本
		return text
	}

	// 收集所有可能的命令前缀（包括主命令和别名）
	var prefixes []string

	// 添加主命令名称
	prefixes = append(prefixes, matchedCommand)
	prefixes = append(prefixes, "/"+matchedCommand)

	// 添加别名
	for _, alias := range commandConfig.Aliases {
		prefixes = append(prefixes, alias)
		prefixes = append(prefixes, "/"+alias)
	}

	// 按长度降序排序，确保先匹配较长的前缀
	sort.Slice(prefixes, func(i, j int) bool {
		return len(prefixes[i]) > len(prefixes[j])
	})

	// 尝试匹配和清理前缀（大小写不敏感）
	lowerText := strings.ToLower(text)
	for _, prefix := range prefixes {
		lowerPrefix := strings.ToLower(prefix)
		if strings.HasPrefix(lowerText, lowerPrefix) {
			// 检查前缀后是否是空格或字符串结束
			if len(text) == len(prefix) {
				// 完全匹配，返回空字符串
				return ""
			} else if len(text) > len(prefix) && text[len(prefix)] == ' ' {
				// 前缀后跟空格，清理前缀和空格
				return strings.TrimSpace(text[len(prefix):])
			}
		}
	}

	// 没有匹配到任何前缀，返回原文本
	return text
}
