package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/svc"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// ChatAdsData 聊天广告数据结构
type ChatAdsData struct {
	AdTextMarkdowns interface{} `json:"ad_text_markdowns,omitempty"`
	Buttons         interface{} `json:"buttons,omitempty"`
}

// 初始化随机数生成器
var rng = rand.New(rand.NewSource(time.Now().UnixNano()))

// selectWeightedAdText 根据权重选择广告文本
func selectWeightedAdText(adTexts config.AdTextConfig) *config.WeightedAdText {
	if len(adTexts) == 0 {
		return nil
	}

	// 检查权重设置情况
	hasWeightCount := 0
	noWeightCount := 0
	validItems := []config.WeightedAdText{}

	for _, item := range adTexts {
		if item.Weight == nil {
			noWeightCount++
		} else {
			hasWeightCount++
			// 只有权重大于0的项目才参与分配
			if *item.Weight > 0 {
				validItems = append(validItems, item)
			}
		}
	}

	// 混合情况：有些有权重，有些没有 = 异常
	if hasWeightCount > 0 && noWeightCount > 0 {
		// 返回错误配置
		errorText := "[⚠️ Ad configuration error]"
		return &config.WeightedAdText{Text: errorText, Weight: nil}
	}

	// 如果所有项目都没有权重，均分
	if noWeightCount > 0 && hasWeightCount == 0 {
		index := rng.Intn(len(adTexts))
		return &adTexts[index]
	}

	// 如果没有有效项目（全是权重0），返回错误
	if len(validItems) == 0 {
		errorText := "[⚠️ Ad configuration error]"
		return &config.WeightedAdText{Text: errorText, Weight: nil}
	}

	// 按权重分配（只考虑权重>0的项目）
	totalWeight := 0
	for _, item := range validItems {
		totalWeight += *item.Weight
	}

	if totalWeight <= 0 {
		errorText := "[⚠️ Ad configuration error]"
		return &config.WeightedAdText{Text: errorText, Weight: nil}
	}

	// 根据权重随机选择
	randomValue := rng.Intn(totalWeight)
	currentWeight := 0
	for _, item := range validItems {
		currentWeight += *item.Weight
		if randomValue < currentWeight {
			return &item
		}
	}

	// 兜底返回第一个有效项目
	return &validItems[0]
}

// selectWeightedButtonLayout 根据权重选择按钮布局
func selectWeightedButtonLayout(buttonLayouts config.ButtonsConfig) *config.WeightedButtonLayout {
	if len(buttonLayouts) == 0 {
		return nil
	}

	// 检查权重设置情况
	hasWeightCount := 0
	noWeightCount := 0
	validItems := []config.WeightedButtonLayout{}

	for _, item := range buttonLayouts {
		if item.Weight == nil {
			noWeightCount++
		} else {
			hasWeightCount++
			// 只有权重大于0的项目才参与分配
			if *item.Weight > 0 {
				validItems = append(validItems, item)
			}
		}
	}

	// 混合情况：有些有权重，有些没有 = 异常
	if hasWeightCount > 0 && noWeightCount > 0 {
		// 返回错误配置
		errorLayout := config.ButtonLayout{
			{{Text: "[⚠️ Ad configuration error]", URL: ""}},
		}
		return &config.WeightedButtonLayout{Layout: errorLayout, Weight: nil}
	}

	// 如果所有项目都没有权重，均分
	if noWeightCount > 0 && hasWeightCount == 0 {
		index := rng.Intn(len(buttonLayouts))
		return &buttonLayouts[index]
	}

	// 如果没有有效项目（全是权重0），返回错误
	if len(validItems) == 0 {
		errorLayout := config.ButtonLayout{
			{{Text: "[⚠️ Ad configuration error]", URL: ""}},
		}
		return &config.WeightedButtonLayout{Layout: errorLayout, Weight: nil}
	}

	// 按权重分配（只考虑权重>0的项目）
	totalWeight := 0
	for _, item := range validItems {
		totalWeight += *item.Weight
	}

	if totalWeight <= 0 {
		errorLayout := config.ButtonLayout{
			{{Text: "[⚠️ Ad configuration error]", URL: ""}},
		}
		return &config.WeightedButtonLayout{Layout: errorLayout, Weight: nil}
	}

	// 根据权重随机选择
	randomValue := rng.Intn(totalWeight)
	currentWeight := 0
	for _, item := range validItems {
		currentWeight += *item.Weight
		if randomValue < currentWeight {
			return &item
		}
	}

	// 兜底返回第一个有效项目
	return &validItems[0]
}

// GetChatAdsText 获取聊天广告文本
func GetChatAdsText(svcCtx *svc.ServiceContext, ctx context.Context, chatID int64) (string, error) {
	// 获取配置
	defaultAdText := &svcCtx.Config.Operation.MessageCaption.AdTextMarkdowns
	var overrideAdText *config.AdTextConfig

	if chatID < 0 { // 群组
		overrideAdText = &svcCtx.Config.Operation.MessageCaption.OverrideGroupTextMarkdowns
	} else { // 私聊
		overrideAdText = &svcCtx.Config.Operation.MessageCaption.OverridePrivateTextMarkdowns
	}

	// 获取聊天特定的广告配置
	chatAds, err := getChatAds(svcCtx, ctx, chatID)
	if err != nil {
		// 如果获取失败，使用默认配置
		return formatAdTextConfig(overrideAdText, defaultAdText), nil
	}

	// 优先级：chat_info.ads > override > default
	var adTextConfig *config.AdTextConfig
	if chatAds != nil && chatAds.AdTextMarkdowns != nil {
		// 如果数据库中明确设置了广告配置（即使是空的），就不使用override/default
		if isEmptyAdText(chatAds.AdTextMarkdowns) {
			// 数据库中明确设置为空广告（null 或 []），不显示任何广告
			// 创建一个空配置，不回退到默认配置
			emptyConfig := config.AdTextConfig{}
			adTextConfig = &emptyConfig
		} else {
			// 检查是否是"default"重定向
			if str, ok := chatAds.AdTextMarkdowns.(string); ok && str == "default" {
				adTextConfig = defaultAdText
			} else {
				// 尝试解析为AdTextConfig结构
				if parsedConfig, ok := parseAdTextFromInterface(chatAds.AdTextMarkdowns); ok {
					adTextConfig = parsedConfig
				} else {
					adTextConfig = overrideAdText
				}
			}
		}
	} else {
		// 数据库中没有广告配置，使用override或default配置
		if overrideAdText != nil {
			adTextConfig = overrideAdText
		} else {
			adTextConfig = defaultAdText
		}
	}

	// 格式化广告文本
	adText := formatAdTextConfig(adTextConfig)
	if adText == "" {
		return "", nil
	}

	// 使用svcCtx中的MarkdownRenderer转换Markdown到HTML
	htmlText, err := svcCtx.MarkdownRenderer.ToHTML([]byte(adText))
	if err != nil {
		logx.Errorf("Failed to convert ad text Markdown to HTML: %v", err)
		// 如果转换失败，返回原始文本
		return adText, nil
	}

	return htmlText, nil
}

// GetChatButtonsMarkup 获取聊天按钮标记
func GetChatButtonsMarkup(svcCtx *svc.ServiceContext, ctx context.Context, chatID int64) (*tele.ReplyMarkup, error) {
	// 获取配置
	defaultButtons := &svcCtx.Config.Operation.MessageCaption.Buttons
	var overrideButtons *config.ButtonsConfig

	if chatID < 0 { // 群组
		overrideButtons = &svcCtx.Config.Operation.MessageCaption.OverrideGroupButtons
	} else { // 私聊
		overrideButtons = &svcCtx.Config.Operation.MessageCaption.OverridePrivateButtons
	}

	// 获取聊天特定的广告配置
	chatAds, err := getChatAds(svcCtx, ctx, chatID)
	if err != nil {
		// 如果获取失败，返回空按钮
		return &tele.ReplyMarkup{}, nil
	}

	// 优先级：chat_info.ads > override > default
	var buttonsConfig *config.ButtonsConfig
	if chatAds != nil && chatAds.Buttons != nil {
		// 如果数据库中明确设置了按钮配置（即使是空的），就不使用override/default
		if isEmptyButtons(chatAds.Buttons) {
			// 数据库中明确设置为空按钮（null 或 []），不显示任何按钮
			// 创建一个空配置，不回退到默认配置
			emptyConfig := config.ButtonsConfig{}
			buttonsConfig = &emptyConfig
		} else {
			// 检查是否是"default"重定向
			if str, ok := chatAds.Buttons.(string); ok && str == "default" {
				buttonsConfig = defaultButtons
			} else {
				// 尝试解析为ButtonsConfig结构
				if parsedConfig, ok := parseButtonsFromInterface(chatAds.Buttons); ok {
					buttonsConfig = parsedConfig
				} else {
					buttonsConfig = overrideButtons
				}
			}
		}
	} else {
		// 数据库中没有按钮配置，使用override或default配置
		if overrideButtons != nil {
			buttonsConfig = overrideButtons
		} else {
			buttonsConfig = defaultButtons
		}
	}

	return createButtonsMarkupFromConfig(buttonsConfig)
}

// getChatAds 获取聊天的广告配置
func getChatAds(svcCtx *svc.ServiceContext, ctx context.Context, chatID int64) (*ChatAdsData, error) {
	chat, err := svcCtx.ChatModel.FindOneByChatID(ctx, chatID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 聊天记录不存在，返回nil
		}
		return nil, fmt.Errorf("failed to get chat info: %v", err)
	}

	if chat.Ads == nil || *chat.Ads == "" {
		return nil, nil // 没有广告配置
	}

	var adsData ChatAdsData
	err = json.Unmarshal([]byte(*chat.Ads), &adsData)
	if err != nil {
		// JSON 解析失败，返回错误配置
		errorText := "[⚠️ Ad configuration error]"
		adsData.AdTextMarkdowns = errorText
		adsData.Buttons = []interface{}{
			map[string]interface{}{
				"weight": 1,
				"layout": []interface{}{
					[]interface{}{
						map[string]interface{}{
							"text": errorText,
							"url":  "",
						},
					},
				},
			},
		}
		return &adsData, nil
	}

	return &adsData, nil
}

// formatAdTextConfig 格式化广告文本配置
func formatAdTextConfig(configs ...*config.AdTextConfig) string {
	for _, cfg := range configs {
		if cfg == nil || len(*cfg) == 0 {
			continue
		}

		// 使用权重选择算法选择一个广告文本
		selectedText := selectWeightedAdText(*cfg)
		if selectedText != nil && selectedText.Text != "" {
			return selectedText.Text
		}
	}

	return "" // 所有配置都无效，返回空字符串
}

// parseAdTextFromInterface 从interface{}解析AdTextConfig
func parseAdTextFromInterface(data interface{}) (*config.AdTextConfig, bool) {
	if data == nil {
		return nil, false
	}

	// 尝试直接转换
	if cfg, ok := data.(*config.AdTextConfig); ok {
		return cfg, true
	}

	// 解析数组格式
	if dataArray, ok := data.([]interface{}); ok {
		var adTexts config.AdTextConfig
		for _, item := range dataArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				weightedText := config.WeightedAdText{Weight: nil} // 默认没有权重

				if text, exists := itemMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						weightedText.Text = textStr
					}
				}

				if weight, exists := itemMap["weight"]; exists {
					if weightInt, ok := weight.(float64); ok {
						w := int(weightInt)
						weightedText.Weight = &w
					}
				}

				if weightedText.Text != "" {
					adTexts = append(adTexts, weightedText)
				}
			}
		}

		if len(adTexts) > 0 {
			return &adTexts, true
		}
	}

	return nil, false
}

// createButtonsMarkupFromConfig 从配置创建按钮标记
func createButtonsMarkupFromConfig(buttonsConfig *config.ButtonsConfig) (*tele.ReplyMarkup, error) {
	if buttonsConfig == nil || len(*buttonsConfig) == 0 {
		return &tele.ReplyMarkup{}, nil
	}

	// 使用权重选择算法选择一个按钮布局
	selectedLayout := selectWeightedButtonLayout(*buttonsConfig)
	if selectedLayout == nil || len(selectedLayout.Layout) == 0 {
		return &tele.ReplyMarkup{}, nil
	}

	// 转换为telebot格式
	var rows []tele.Row
	for _, row := range selectedLayout.Layout {
		var buttons tele.Row
		for _, btn := range row {
			button := tele.Btn{
				Text: btn.Text,
				URL:  btn.URL,
			}
			buttons = append(buttons, button)
		}
		if len(buttons) > 0 {
			rows = append(rows, buttons)
		}
	}

	markup := &tele.ReplyMarkup{}
	markup.Inline(rows...)
	return markup, nil
}

// parseButtonsFromInterface 从interface{}解析ButtonsConfig
func parseButtonsFromInterface(data interface{}) (*config.ButtonsConfig, bool) {
	if data == nil {
		return nil, false
	}

	// 尝试直接转换
	if cfg, ok := data.(*config.ButtonsConfig); ok {
		return cfg, true
	}

	// 解析数组格式
	if dataArray, ok := data.([]interface{}); ok {
		var buttonConfigs config.ButtonsConfig
		for _, item := range dataArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				weightedLayout := config.WeightedButtonLayout{Weight: nil} // 默认没有权重

				if layoutData, exists := itemMap["layout"]; exists {
					if layout, ok := parseButtonLayoutFromInterface(layoutData); ok {
						weightedLayout.Layout = layout
					}
				}

				if weight, exists := itemMap["weight"]; exists {
					if weightInt, ok := weight.(float64); ok {
						w := int(weightInt)
						weightedLayout.Weight = &w
					}
				}

				if len(weightedLayout.Layout) > 0 {
					buttonConfigs = append(buttonConfigs, weightedLayout)
				}
			}
		}

		if len(buttonConfigs) > 0 {
			return &buttonConfigs, true
		}
	}

	return nil, false
}

// parseButtonLayoutFromInterface 从interface{}解析ButtonLayout
func parseButtonLayoutFromInterface(data interface{}) (config.ButtonLayout, bool) {
	layoutArray, ok := data.([]interface{})
	if !ok {
		return nil, false
	}

	var layout config.ButtonLayout
	for _, rowData := range layoutArray {
		rowArray, ok := rowData.([]interface{})
		if !ok {
			continue
		}

		var row []config.ButtonConfig
		for _, btnData := range rowArray {
			btnMap, ok := btnData.(map[string]interface{})
			if !ok {
				continue
			}

			text, textOk := btnMap["text"].(string)
			url, urlOk := btnMap["url"].(string)
			if textOk && urlOk {
				row = append(row, config.ButtonConfig{
					Text: text,
					URL:  url,
				})
			}
		}

		if len(row) > 0 {
			layout = append(layout, row)
		}
	}

	return layout, len(layout) > 0
}

// isEmptyAdText 检查广告文本是否为空
func isEmptyAdText(data interface{}) bool {
	// null 视为空
	if data == nil {
		return true
	}

	// 检查数组格式（包括空数组）
	if dataArray, ok := data.([]interface{}); ok {
		if len(dataArray) == 0 {
			return true // 空数组视为空
		}
		// 检查数组中是否有有效的文本
		for _, item := range dataArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if text, exists := itemMap["text"]; exists {
					if textStr, ok := text.(string); ok && textStr != "" {
						return false // 找到非空文本
					}
				}
			}
		}
		return true // 数组中没有有效文本
	}

	// 其他类型认为为空
	return true
}

// isEmptyButtons 检查按钮配置是否为空
func isEmptyButtons(data interface{}) bool {
	// null 视为空
	if data == nil {
		return true
	}

	// 检查数组格式（包括空数组）
	if dataArray, ok := data.([]interface{}); ok {
		if len(dataArray) == 0 {
			return true // 空数组视为空
		}
		// 检查数组中是否有有效的按钮布局
		for _, item := range dataArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if layoutData, exists := itemMap["layout"]; exists {
					if layoutData != nil {
						if layoutArray, ok := layoutData.([]interface{}); ok && len(layoutArray) > 0 {
							return false // 找到非空布局
						}
					}
				}
			}
		}
		return true // 数组中没有有效布局
	}

	// 其他类型认为为空
	return true
}
