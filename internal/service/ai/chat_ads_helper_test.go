package ai

import (
	"encoding/json"
	"telegram-chatbot-go/internal/config"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 辅助函数：创建int指针
func intPtr(i int) *int {
	return &i
}

// TestFormatAdTextConfig 测试广告文本配置格式化功能
func TestFormatAdTextConfig(t *testing.T) {
	// 测试简单文本格式
	textConfig := &config.AdTextConfig{
		{Text: "测试广告文本", Weight: intPtr(1)},
	}
	result := formatAdTextConfig(textConfig)
	assert.Equal(t, "测试广告文本", result)

	// 测试空值
	result = formatAdTextConfig(nil)
	assert.Equal(t, "", result)

	emptyConfig := &config.AdTextConfig{}
	result = formatAdTextConfig(emptyConfig)
	assert.Equal(t, "", result)
}

// TestParseButtonLayoutFromInterface 测试按钮布局解析
func TestParseButtonLayoutFromInterface(t *testing.T) {
	// 测试正常按钮布局
	layoutData := []interface{}{
		[]interface{}{
			map[string]interface{}{
				"text": "按钮1",
				"url":  "https://example.com/1",
			},
			map[string]interface{}{
				"text": "按钮2",
				"url":  "https://example.com/2",
			},
		},
		[]interface{}{
			map[string]interface{}{
				"text": "按钮3",
				"url":  "https://example.com/3",
			},
		},
	}

	layout, ok := parseButtonLayoutFromInterface(layoutData)
	assert.True(t, ok)
	assert.Len(t, layout, 2)
	assert.Len(t, layout[0], 2)
	assert.Len(t, layout[1], 1)
	assert.Equal(t, "按钮1", layout[0][0].Text)
	assert.Equal(t, "https://example.com/1", layout[0][0].URL)

	// 测试无效格式
	invalidData := "invalid"
	_, ok = parseButtonLayoutFromInterface(invalidData)
	assert.False(t, ok)
}

// TestCreateButtonsMarkupFromConfig 测试按钮标记创建
func TestCreateButtonsMarkupFromConfig(t *testing.T) {
	// 测试简单布局格式
	simpleConfig := &config.ButtonsConfig{
		{
			Layout: config.ButtonLayout{
				{
					{Text: "测试按钮", URL: "https://test.com"},
				},
			},
			Weight: intPtr(1),
		},
	}

	markup, err := createButtonsMarkupFromConfig(simpleConfig)
	assert.NoError(t, err)
	assert.NotNil(t, markup)
	assert.Len(t, markup.InlineKeyboard, 1)
	assert.Len(t, markup.InlineKeyboard[0], 1)

	// 测试空配置
	emptyConfig := &config.ButtonsConfig{}
	markup, err = createButtonsMarkupFromConfig(emptyConfig)
	assert.NoError(t, err)
	assert.NotNil(t, markup)
	assert.Len(t, markup.InlineKeyboard, 0)
}

// TestChatAdsDataSerialization 测试聊天广告数据序列化
func TestChatAdsDataSerialization(t *testing.T) {
	// 测试序列化和反序列化
	originalData := &ChatAdsData{
		AdTextMarkdowns: map[string]interface{}{
			"广告1": float64(50),
			"广告2": float64(50),
		},
		Buttons: map[string]interface{}{
			"layout": []interface{}{
				[]interface{}{
					map[string]interface{}{
						"text": "测试按钮",
						"url":  "https://test.com",
					},
				},
			},
		},
	}

	// 序列化
	jsonData, err := json.Marshal(originalData)
	assert.NoError(t, err)

	// 反序列化
	var deserializedData ChatAdsData
	err = json.Unmarshal(jsonData, &deserializedData)
	assert.NoError(t, err)

	// 验证数据完整性
	assert.NotNil(t, deserializedData.AdTextMarkdowns)
	assert.NotNil(t, deserializedData.Buttons)
}

// TestIsEmptyAdText 测试广告文本空值检测
func TestIsEmptyAdText(t *testing.T) {
	// 测试nil
	assert.True(t, isEmptyAdText(nil))

	// 测试空数组
	emptyArray := []interface{}{}
	assert.True(t, isEmptyAdText(emptyArray))

	// 测试数组格式 - 空text
	emptyTextArray := []interface{}{
		map[string]interface{}{
			"text":   "",
			"weight": float64(1),
		},
	}
	assert.True(t, isEmptyAdText(emptyTextArray))

	// 测试数组格式 - 非空text
	nonEmptyTextArray := []interface{}{
		map[string]interface{}{
			"text":   "some ad text",
			"weight": float64(1),
		},
	}
	assert.False(t, isEmptyAdText(nonEmptyTextArray))

	// 测试其他类型
	assert.True(t, isEmptyAdText("some string"))
	assert.True(t, isEmptyAdText(map[string]interface{}{"other": "value"}))
}

// TestIsEmptyButtons 测试按钮配置空值检测
func TestIsEmptyButtons(t *testing.T) {
	// 测试nil
	assert.True(t, isEmptyButtons(nil))

	// 测试空数组
	emptyArray := []interface{}{}
	assert.True(t, isEmptyButtons(emptyArray))

	// 测试数组格式 - 空layout数组
	emptyLayoutArray := []interface{}{
		map[string]interface{}{
			"weight": float64(1),
			"layout": []interface{}{},
		},
	}
	assert.True(t, isEmptyButtons(emptyLayoutArray))

	// 测试数组格式 - 非空layout数组
	nonEmptyLayoutArray := []interface{}{
		map[string]interface{}{
			"weight": float64(1),
			"layout": []interface{}{
				[]interface{}{
					map[string]interface{}{
						"text": "Button",
						"url":  "https://example.com",
					},
				},
			},
		},
	}
	assert.False(t, isEmptyButtons(nonEmptyLayoutArray))

	// 测试数组格式 - layout为null
	nullLayoutArray := []interface{}{
		map[string]interface{}{
			"weight": float64(1),
			"layout": nil,
		},
	}
	assert.True(t, isEmptyButtons(nullLayoutArray))

	// 测试其他类型
	assert.True(t, isEmptyButtons("some string"))
	assert.True(t, isEmptyButtons(map[string]interface{}{"other": "value"}))
}

// TestDatabaseAdFormats 测试数据库中的广告格式
func TestDatabaseAdFormats(t *testing.T) {
	// 格式1: 空数组
	format1JSON := `{
		"ad_text_markdowns": [],
		"buttons": []
	}`

	var format1Data ChatAdsData
	err := json.Unmarshal([]byte(format1JSON), &format1Data)
	assert.NoError(t, err)

	// 验证格式1被识别为空广告
	assert.True(t, isEmptyAdText(format1Data.AdTextMarkdowns))
	assert.True(t, isEmptyButtons(format1Data.Buttons))

	// 格式2: null值
	format2JSON := `{
		"ad_text_markdowns": null,
		"buttons": null
	}`

	var format2Data ChatAdsData
	err = json.Unmarshal([]byte(format2JSON), &format2Data)
	assert.NoError(t, err)

	// 验证格式2被识别为空广告
	assert.True(t, isEmptyAdText(format2Data.AdTextMarkdowns))
	assert.True(t, isEmptyButtons(format2Data.Buttons))

	// 测试非空格式
	nonEmptyJSON := `{
		"ad_text_markdowns": [
			{
				"text": "Some ad text",
				"weight": 1
			}
		],
		"buttons": [
			{
				"weight": 1,
				"layout": [
					[
						{
							"text": "Click me",
							"url": "https://example.com"
						}
					]
				]
			}
		]
	}`

	var nonEmptyData ChatAdsData
	err = json.Unmarshal([]byte(nonEmptyJSON), &nonEmptyData)
	assert.NoError(t, err)

	// 验证非空格式被识别为有广告
	assert.False(t, isEmptyAdText(nonEmptyData.AdTextMarkdowns))
	assert.False(t, isEmptyButtons(nonEmptyData.Buttons))
}

// TestEmptyAdLogic 测试空广告逻辑
func TestEmptyAdLogic(t *testing.T) {
	// 模拟数据库中明确设置为空的广告配置
	emptyAdsData := &ChatAdsData{
		AdTextMarkdowns: "",
		Buttons: map[string]interface{}{
			"layout": nil,
		},
	}

	// 测试广告文本逻辑
	// 当数据库中明确设置了空广告文本时，应该返回nil而不是fallback到override
	var adTextConfig *config.AdTextConfig
	if emptyAdsData != nil && emptyAdsData.AdTextMarkdowns != nil {
		if isEmptyAdText(emptyAdsData.AdTextMarkdowns) {
			adTextConfig = nil // 数据库中明确设置为空广告，不显示任何广告
		} else {
			// 其他逻辑...
		}
	} else {
		// fallback逻辑...
	}

	assert.Nil(t, adTextConfig, "当数据库中明确设置空广告文本时，应该返回nil")

	// 测试按钮逻辑
	var buttonsConfig *config.ButtonsConfig
	if emptyAdsData != nil && emptyAdsData.Buttons != nil {
		if isEmptyButtons(emptyAdsData.Buttons) {
			buttonsConfig = nil // 数据库中明确设置为空按钮，不显示任何按钮
		} else {
			// 其他逻辑...
		}
	} else {
		// fallback逻辑...
	}

	assert.Nil(t, buttonsConfig, "当数据库中明确设置空按钮时，应该返回nil")
}

// TestFormatAdTextConfigOnly 测试仅配置格式化（不涉及数据库）
func TestFormatAdTextConfigOnly(t *testing.T) {
	// 测试简单文本配置
	textConfig := &config.AdTextConfig{
		{Text: "群组覆盖广告", Weight: intPtr(1)},
	}
	result := formatAdTextConfig(textConfig)
	assert.Equal(t, "群组覆盖广告", result)

	// 测试多个配置的优先级
	config1 := &config.AdTextConfig{{Text: "", Weight: intPtr(1)}}
	config2 := &config.AdTextConfig{{Text: "第二个配置", Weight: intPtr(1)}}
	result = formatAdTextConfig(config1, config2)
	assert.Equal(t, "第二个配置", result) // 应该使用第二个非空配置
}

// TestWeightedAdTextSelection 测试权重广告文本选择
func TestWeightedAdTextSelection(t *testing.T) {
	// 测试权重分配
	adTexts := config.AdTextConfig{
		{Text: "广告1", Weight: intPtr(70)},
		{Text: "广告2", Weight: intPtr(30)},
	}

	// 运行多次测试，确保两个广告都能被选中
	results := make(map[string]int)
	for i := 0; i < 1000; i++ {
		selected := selectWeightedAdText(adTexts)
		assert.NotNil(t, selected)
		results[selected.Text]++
	}

	// 验证两个广告都被选中过
	assert.Contains(t, results, "广告1")
	assert.Contains(t, results, "广告2")
	assert.True(t, results["广告1"] > 0)
	assert.True(t, results["广告2"] > 0)

	// 测试权重为0的情况（应该返回错误）
	adTextsZeroWeight := config.AdTextConfig{
		{Text: "广告A", Weight: intPtr(0)},
		{Text: "广告B", Weight: intPtr(0)},
	}

	// 权重为0应该返回错误配置
	selected := selectWeightedAdText(adTextsZeroWeight)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)
}

// TestWeightedButtonLayoutSelection 测试权重按钮布局选择
func TestWeightedButtonLayoutSelection(t *testing.T) {
	// 测试权重分配
	buttonLayouts := config.ButtonsConfig{
		{
			Layout: config.ButtonLayout{
				{{Text: "按钮1", URL: "https://example1.com"}},
			},
			Weight: intPtr(60),
		},
		{
			Layout: config.ButtonLayout{
				{{Text: "按钮2", URL: "https://example2.com"}},
			},
			Weight: intPtr(40),
		},
	}

	// 运行多次测试，确保两个布局都能被选中
	results := make(map[string]int)
	for i := 0; i < 1000; i++ {
		selected := selectWeightedButtonLayout(buttonLayouts)
		assert.NotNil(t, selected)
		assert.True(t, len(selected.Layout) > 0)
		assert.True(t, len(selected.Layout[0]) > 0)
		buttonText := selected.Layout[0][0].Text
		results[buttonText]++
	}

	// 验证两个布局都被选中过
	assert.Contains(t, results, "按钮1")
	assert.Contains(t, results, "按钮2")
	assert.True(t, results["按钮1"] > 0)
	assert.True(t, results["按钮2"] > 0)
}

// TestParseAdText 测试解析广告文本
func TestParseAdText(t *testing.T) {
	// 测试数组格式
	newFormatData := []interface{}{
		map[string]interface{}{
			"text":   "广告文本1",
			"weight": float64(50),
		},
		map[string]interface{}{
			"text":   "广告文本2",
			"weight": float64(50),
		},
	}

	config, ok := parseAdTextFromInterface(newFormatData)
	assert.True(t, ok)
	assert.NotNil(t, config)
	assert.Len(t, *config, 2)
	assert.Equal(t, "广告文本1", (*config)[0].Text)
	assert.Equal(t, 50, *(*config)[0].Weight)
	assert.Equal(t, "广告文本2", (*config)[1].Text)
	assert.Equal(t, 50, *(*config)[1].Weight)

}

// TestParseButtons 测试解析按钮
func TestParseButtons(t *testing.T) {
	// 测试数组格式
	newFormatData := []interface{}{
		map[string]interface{}{
			"weight": float64(70),
			"layout": []interface{}{
				[]interface{}{
					map[string]interface{}{
						"text": "按钮1",
						"url":  "https://example1.com",
					},
				},
			},
		},
		map[string]interface{}{
			"weight": float64(30),
			"layout": []interface{}{
				[]interface{}{
					map[string]interface{}{
						"text": "按钮2",
						"url":  "https://example2.com",
					},
				},
			},
		},
	}

	config, ok := parseButtonsFromInterface(newFormatData)
	assert.True(t, ok)
	assert.NotNil(t, config)
	assert.Len(t, *config, 2)
	assert.Equal(t, 70, *(*config)[0].Weight)
	assert.Equal(t, 30, *(*config)[1].Weight)
	assert.Len(t, (*config)[0].Layout, 1)
	assert.Len(t, (*config)[0].Layout[0], 1)
	assert.Equal(t, "按钮1", (*config)[0].Layout[0][0].Text)

}

// TestIsEmptyFormat 测试空值检测
func TestIsEmptyFormat(t *testing.T) {
	// 测试空广告文本
	emptyAdTextArray := []interface{}{}
	assert.True(t, isEmptyAdText(emptyAdTextArray))

	emptyAdTextWithEmptyItems := []interface{}{
		map[string]interface{}{
			"text":   "",
			"weight": float64(1),
		},
	}
	assert.True(t, isEmptyAdText(emptyAdTextWithEmptyItems))

	nonEmptyAdText := []interface{}{
		map[string]interface{}{
			"text":   "有效广告",
			"weight": float64(1),
		},
	}
	assert.False(t, isEmptyAdText(nonEmptyAdText))

	// 测试新格式空按钮
	emptyButtonsArray := []interface{}{}
	assert.True(t, isEmptyButtons(emptyButtonsArray))

	emptyButtonsWithEmptyLayout := []interface{}{
		map[string]interface{}{
			"weight": float64(1),
			"layout": []interface{}{},
		},
	}
	assert.True(t, isEmptyButtons(emptyButtonsWithEmptyLayout))

	nonEmptyButtons := []interface{}{
		map[string]interface{}{
			"weight": float64(1),
			"layout": []interface{}{
				[]interface{}{
					map[string]interface{}{
						"text": "有效按钮",
						"url":  "https://valid.com",
					},
				},
			},
		},
	}
	assert.False(t, isEmptyButtons(nonEmptyButtons))
}

// TestWeightedSelectionEdgeCases 测试权重选择的边界情况
func TestWeightedSelectionEdgeCases(t *testing.T) {
	// 测试空数组
	emptyAdTexts := config.AdTextConfig{}
	selected := selectWeightedAdText(emptyAdTexts)
	assert.Nil(t, selected)

	emptyButtons := config.ButtonsConfig{}
	selectedButton := selectWeightedButtonLayout(emptyButtons)
	assert.Nil(t, selectedButton)

	// 测试单个元素
	singleAdText := config.AdTextConfig{
		{Text: "唯一广告", Weight: intPtr(100)},
	}
	selected = selectWeightedAdText(singleAdText)
	assert.NotNil(t, selected)
	assert.Equal(t, "唯一广告", selected.Text)

	// 测试权重为0的情况（应该返回错误）
	zeroWeightAdTexts := config.AdTextConfig{
		{Text: "广告1", Weight: intPtr(0)},
		{Text: "广告2", Weight: intPtr(0)},
		{Text: "广告3", Weight: intPtr(0)},
	}

	// 权重为0应该返回错误配置
	selected = selectWeightedAdText(zeroWeightAdTexts)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)

	// 测试混合权重（部分有权重，部分没有）
	mixedWeightAdTexts := config.AdTextConfig{
		{Text: "有权重广告", Weight: intPtr(50)},
		{Text: "无权重广告1", Weight: nil},
		{Text: "无权重广告2", Weight: nil},
	}

	// 混合权重应该返回错误配置
	selected = selectWeightedAdText(mixedWeightAdTexts)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)
}

// TestWeightDistribution 测试权重分布的准确性
func TestWeightDistribution(t *testing.T) {
	// 测试权重分布是否符合预期
	adTexts := config.AdTextConfig{
		{Text: "广告A", Weight: intPtr(80)}, // 80%
		{Text: "广告B", Weight: intPtr(20)}, // 20%
	}

	results := make(map[string]int)
	iterations := 10000

	for i := 0; i < iterations; i++ {
		selected := selectWeightedAdText(adTexts)
		assert.NotNil(t, selected)
		results[selected.Text]++
	}

	// 验证分布大致符合权重比例（允许一定误差）
	aCount := results["广告A"]
	bCount := results["广告B"]

	aPercentage := float64(aCount) / float64(iterations)
	bPercentage := float64(bCount) / float64(iterations)

	// 允许5%的误差
	assert.InDelta(t, 0.8, aPercentage, 0.05, "广告A的选择比例应该接近80%")
	assert.InDelta(t, 0.2, bPercentage, 0.05, "广告B的选择比例应该接近20%")

	t.Logf("广告A被选中 %d 次 (%.2f%%)", aCount, aPercentage*100)
	t.Logf("广告B被选中 %d 次 (%.2f%%)", bCount, bPercentage*100)
}

// TestNullAndEmptyArrayCompatibility 测试null和空数组的兼容性
func TestNullAndEmptyArrayCompatibility(t *testing.T) {
	// 测试 null 和 [] 在广告文本中的兼容性
	nullAdTextJSON := `{
		"ad_text_markdowns": null,
		"buttons": []
	}`

	var nullData ChatAdsData
	err := json.Unmarshal([]byte(nullAdTextJSON), &nullData)
	assert.NoError(t, err)

	// null 应该被视为空
	assert.True(t, isEmptyAdText(nullData.AdTextMarkdowns))
	assert.True(t, isEmptyButtons(nullData.Buttons))

	// 测试 [] 和 null 在按钮中的兼容性
	emptyArrayJSON := `{
		"ad_text_markdowns": [],
		"buttons": null
	}`

	var emptyData ChatAdsData
	err = json.Unmarshal([]byte(emptyArrayJSON), &emptyData)
	assert.NoError(t, err)

	// 空数组应该被视为空
	assert.True(t, isEmptyAdText(emptyData.AdTextMarkdowns))
	assert.True(t, isEmptyButtons(emptyData.Buttons))
}

// TestMixedWeightHandling 测试混合权重的处理
func TestMixedWeightHandling(t *testing.T) {
	// 测试混合权重的广告文本（有些有权重，有些没有）
	mixedWeightJSON := `{
		"ad_text_markdowns": [
			{
				"text": "有权重的广告",
				"weight": 9999
			},
			{
				"text": "没有权重的广告"
			}
		]
	}`

	var mixedData ChatAdsData
	err := json.Unmarshal([]byte(mixedWeightJSON), &mixedData)
	assert.NoError(t, err)

	// 解析配置
	adConfig, ok := parseAdTextFromInterface(mixedData.AdTextMarkdowns)
	assert.True(t, ok)
	assert.NotNil(t, adConfig)
	assert.Len(t, *adConfig, 2)

	// 验证权重解析正确
	assert.Equal(t, "有权重的广告", (*adConfig)[0].Text)
	assert.Equal(t, 9999, *(*adConfig)[0].Weight)
	assert.Equal(t, "没有权重的广告", (*adConfig)[1].Text)
	assert.Nil(t, (*adConfig)[1].Weight) // 没有权重字段应该是nil

	// 测试选择逻辑 - 混合权重应该返回错误
	selected := selectWeightedAdText(*adConfig)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)
}

// TestSpecificScenario 测试您提到的具体场景
func TestSpecificScenario(t *testing.T) {
	// 测试您提到的具体场景
	scenarioJSON := `{
		"ad_text_markdowns": [
			{
				"text": "2222",
				"weight": 9999
			},
			{
				"text": "3333"
			}
		],
		"buttons": []
	}`

	var scenarioData ChatAdsData
	err := json.Unmarshal([]byte(scenarioJSON), &scenarioData)
	assert.NoError(t, err)

	// 验证按钮为空
	assert.True(t, isEmptyButtons(scenarioData.Buttons))

	// 解析广告文本配置
	adConfig, ok := parseAdTextFromInterface(scenarioData.AdTextMarkdowns)
	assert.True(t, ok)
	assert.NotNil(t, adConfig)
	assert.Len(t, *adConfig, 2)

	// 验证解析结果
	assert.Equal(t, "2222", (*adConfig)[0].Text)
	assert.Equal(t, 9999, *(*adConfig)[0].Weight)
	assert.Equal(t, "3333", (*adConfig)[1].Text)
	assert.Nil(t, (*adConfig)[1].Weight) // 没有weight字段应该是nil

	// 测试选择逻辑 - 混合权重应该返回错误
	selected := selectWeightedAdText(*adConfig)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)
	t.Logf("验证通过：混合权重情况下正确返回错误配置")
}

// TestZeroWeightExclusion 测试权重为0的项目不参与分配
func TestZeroWeightExclusion(t *testing.T) {
	// 测试场景：{ "text": "广告A", "weight": 50 }, { "text": "广告B", "weight": 0 }
	// 结果：必定是A
	adTexts := config.AdTextConfig{
		{Text: "广告A", Weight: intPtr(50)},
		{Text: "广告B", Weight: intPtr(0)},
	}

	// 运行多次测试，确保只有A被选中
	results := make(map[string]int)
	iterations := 1000
	for i := 0; i < iterations; i++ {
		selected := selectWeightedAdText(adTexts)
		assert.NotNil(t, selected)
		results[selected.Text]++
	}

	// 验证只有广告A被选中，广告B（权重0）不应该被选中
	assert.Contains(t, results, "广告A")
	assert.NotContains(t, results, "广告B")
	assert.Equal(t, iterations, results["广告A"])

	t.Logf("广告A被选中 %d 次", results["广告A"])
	t.Logf("广告B被选中 %d 次", results["广告B"])
	t.Logf("验证通过：权重为0的项目不参与分配")
}

// TestAllZeroWeight 测试所有权重都为0的情况
func TestAllZeroWeight(t *testing.T) {
	// 测试所有权重都为0的情况
	adTexts := config.AdTextConfig{
		{Text: "广告1", Weight: intPtr(0)},
		{Text: "广告2", Weight: intPtr(0)},
	}

	// 应该返回错误配置
	selected := selectWeightedAdText(adTexts)
	assert.NotNil(t, selected)
	assert.Equal(t, "[⚠️ Ad configuration error]", selected.Text)

	t.Logf("验证通过：所有权重为0时返回错误配置")
}

// TestNoWeightFields 测试所有项目都没有权重字段的情况
func TestNoWeightFields(t *testing.T) {
	// 测试所有项目都没有权重字段的情况（均分）
	adTexts := config.AdTextConfig{
		{Text: "广告X", Weight: nil},
		{Text: "广告Y", Weight: nil},
	}

	// 运行多次测试，确保均分
	results := make(map[string]int)
	iterations := 1000
	for i := 0; i < iterations; i++ {
		selected := selectWeightedAdText(adTexts)
		assert.NotNil(t, selected)
		results[selected.Text]++
	}

	// 验证两个广告都被选中过（均分逻辑）
	assert.Contains(t, results, "广告X")
	assert.Contains(t, results, "广告Y")
	assert.True(t, results["广告X"] > 0)
	assert.True(t, results["广告Y"] > 0)

	// 验证分布相对均匀
	ratio := float64(results["广告X"]) / float64(results["广告Y"])
	assert.True(t, ratio > 0.3 && ratio < 3.0, "无权重字段应该均分，比例应该接近1:1，实际比例: %.2f", ratio)

	t.Logf("广告X被选中 %d 次", results["广告X"])
	t.Logf("广告Y被选中 %d 次", results["广告Y"])
	t.Logf("选择比例: %.2f:1", ratio)
	t.Logf("验证通过：无权重字段时正确执行均分逻辑")
}

// TestJSONParseError 测试JSON解析失败的处理
func TestJSONParseError(t *testing.T) {
	// 模拟JSON解析失败的情况
	invalidJSON := `{"ad_text_markdowns": [invalid json`

	var adsData ChatAdsData
	err := json.Unmarshal([]byte(invalidJSON), &adsData)

	// 验证JSON解析确实失败
	assert.Error(t, err)

	// 在实际代码中，JSON解析失败会返回错误配置
	// 这里我们测试错误配置的解析
	errorConfigJSON := `{
		"ad_text_markdowns": "[⚠️ Ad configuration error]",
		"buttons": [
			{
				"weight": 1,
				"layout": [
					[
						{
							"text": "[⚠️ Ad configuration error]",
							"url": ""
						}
					]
				]
			}
		]
	}`

	var errorAdsData ChatAdsData
	err = json.Unmarshal([]byte(errorConfigJSON), &errorAdsData)
	assert.NoError(t, err)

	// 验证错误配置的内容
	assert.Equal(t, "[⚠️ Ad configuration error]", errorAdsData.AdTextMarkdowns)
	assert.NotNil(t, errorAdsData.Buttons)

	t.Logf("验证通过：JSON解析失败时返回错误配置")
}

// TestNullAndEmptyArrayNoFallback 测试null和空数组不回退到默认配置
func TestNullAndEmptyArrayNoFallback(t *testing.T) {
	// 测试空数组配置
	emptyArrayConfig := config.AdTextConfig{}
	result := formatAdTextConfig(&emptyArrayConfig)
	assert.Equal(t, "", result) // 空配置应该返回空字符串，不回退

	// 测试空按钮配置
	emptyButtonConfig := config.ButtonsConfig{}
	markup, err := createButtonsMarkupFromConfig(&emptyButtonConfig)
	assert.NoError(t, err)
	assert.NotNil(t, markup)
	assert.Len(t, markup.InlineKeyboard, 0) // 空配置应该返回空按钮，不回退

	t.Logf("验证通过：null和空数组不回退到默认配置")
}
