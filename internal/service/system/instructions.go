package system

import (
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// Instructions 处理instructions命令
func (s *SystemService) Instructions(c tele.Context) error {
	title := utils.GetI18nText(c, i18n.Keys.Instructions.Title)
	content := utils.GetI18nText(c, i18n.Keys.Instructions.Content)
	message := title + "\n\n" + content

	return c.Send(message)
}
