package system

import (
	"fmt"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// GetInviteCode 处理get_invite_code命令
func (s *SystemService) GetInviteCode(c tele.Context) error {
	message := s.InviteLink(c)
	return c.Send(message)
}

// InviteLink 生成邀请链接
func (s *SystemService) InviteLink(c tele.Context) string {
	userID := c.Sender().ID

	// 生成邀请链接
	botUsername := s.svcCtx.Bot.Me.Username
	inviteLink := fmt.Sprintf("https://t.me/%s?start=%d", botUsername, userID)

	return utils.GetI18nTextWithArgs(c, i18n.Keys.Invite.Start.Link, map[string]interface{}{
		"link": inviteLink,
	})
}
