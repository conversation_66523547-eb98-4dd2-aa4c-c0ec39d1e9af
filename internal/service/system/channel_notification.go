package system

import (
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// ChannelNotification 处理频道通知命令
func (s *SystemService) ChannelNotification(c tele.Context) error {
	photoID := utils.GetI18nText(c, i18n.Keys.Channel.NotificationPhotoID)
	if photoID == "" {
		return c.Send(utils.GetI18nText(c, i18n.Keys.Channel.Notification))
	}

	photo := tele.Photo{
		File:    tele.FromURL(photoID),
		Caption: utils.GetI18nText(c, i18n.Keys.Channel.Notification),
	}
	return c.Send(&photo)
}
