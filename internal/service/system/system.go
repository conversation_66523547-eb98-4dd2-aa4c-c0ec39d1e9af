package system

import (
	"context"
	"telegram-chatbot-go/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

// SystemService 系统功能服务
type SystemService struct {
	logx.Logger
	svcCtx *svc.ServiceContext
	ctx    context.Context
}

// NewSystemService 创建系统服务
func NewSystemService(svcCtx *svc.ServiceContext, ctx context.Context) *SystemService {
	return &SystemService{
		Logger: logx.WithContext(ctx),
		svcCtx: svcCtx,
		ctx:    ctx,
	}
}
