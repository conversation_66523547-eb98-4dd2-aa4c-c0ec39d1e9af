package system

import (
	"strconv"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/menu"
	"telegram-chatbot-go/internal/service/user"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// Start 处理start命令
func (s *SystemService) Start(c tele.Context) error {
	args := utils.ExtractArgs(c.Text())
	userID := utils.GetUserID(c)

	if args != "" {
		// 不能自己邀请自己
		inviteUserID, err := strconv.ParseInt(args, 10, 64)
		if err == nil && inviteUserID != userID {
			// 不能重复邀请：邀请定义为24小时内注册的
			userService := user.NewUserService(s.svcCtx, s.ctx)
			userInfo, err := userService.GetUserInfo(userID)
			if err == nil && userInfo.CreateAt.After(time.Now().Add(-24*time.Hour)) {
				s.Infof("邀请成功")

				// 1. 标记邀请关系
				err = userService.MarkInvite(userID, inviteUserID)
				if err != nil {
					s.Errorf("标记邀请关系失败: %v", err)
				} else {
					// 2. 处理邀请奖励
					sessionID := utils.GetTraceID(c)
					var sessionIDPtr *string
					if sessionID != "" {
						sessionIDPtr = &sessionID
					}

					var chatID *int64
					if c.Chat() != nil {
						chatID = &[]int64{c.Chat().ID}[0]
					}

					var messageID *int64
					if c.Message() != nil {
						messageID = &[]int64{int64(c.Message().ID)}[0]
					}

					// 处理邀请奖励（给邀请人和被邀请人发放奖励）
					err = userService.ProcessInviteReward(inviteUserID, userID, sessionIDPtr, chatID, messageID)
					if err != nil {
						s.Errorf("处理邀请奖励失败: %v", err)
					} else {
						s.Infof("邀请奖励处理成功: 邀请人ID=%d, 被邀请人ID=%d", inviteUserID, userID)
					}
				}
			}
		}
	}

	return s.SendWelcomeMessage(c)
}

// SendWelcomeMessage 发送欢迎消息，可被其他地方复用
func (s *SystemService) SendWelcomeMessage(c tele.Context) error {
	welcomeMsg, keyboardMarkup, photoID := s.getWelcomeContent(c)

	if photoID != "" {
		// 如果有图片，发送图片消息
		photo := &tele.Photo{
			File:    tele.File{FileID: photoID},
			Caption: welcomeMsg,
		}
		return c.Send(photo, keyboardMarkup)
	}

	// 否则只发送文本消息
	return c.Send(welcomeMsg, keyboardMarkup)
}

// getWelcomeContent 获取欢迎消息的内容
func (s *SystemService) getWelcomeContent(c tele.Context) (string, *tele.ReplyMarkup, string) {
	welcomeMsg := utils.GetI18nText(c, i18n.Keys.Start.Welcome.Message)
	inviteLink := s.InviteLink(c)
	welcomeMsg = welcomeMsg + "\n\n" + inviteLink

	// 获取欢迎图片ID（如果有的话）
	photoID := utils.GetI18nText(c, i18n.Keys.Start.Welcome.MessagePhotoID)

	keyboard := menu.GetMainKeyboard(c)

	return welcomeMsg, keyboard, photoID
}
