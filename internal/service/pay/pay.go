package pay

import (
	"context"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

// PaymentService 支付服务
type PayService struct {
	logx.Logger
	svcCtx       *svc.ServiceContext
	ctx          context.Context
	epusdtClient *request.EPUSDTClient
}

// NewPaymentService 创建支付服务
func NewPayService(svcCtx *svc.ServiceContext, ctx context.Context) *PayService {
	return &PayService{
		Logger:       logx.WithContext(ctx),
		svcCtx:       svcCtx,
		ctx:          ctx,
		epusdtClient: request.NewEPUSDTClient(&svcCtx.Config),
	}
}
