package pay

import (
	"fmt"
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// === 管理员命令实现 ===

// AdminCreatePackage 管理员创建套餐命令
// 用法: /admin_create_package <name> <price> <vip_days> <vip_points> <permanent_points> [description]
// 例如: /admin_create_package "VIP月卡" 29.9 30 1000 0 "包含30天VIP和1000VIP积分"
func (s *PayService) AdminCreatePackage(c tele.Context) error {
	args := strings.Fields(utils.ExtractArgs(c.Text()))
	if len(args) < 5 {
		return c.Send("用法: /admin_create_package <name> <price> <vip_days> <vip_points> <permanent_points> [description]\n例如: /admin_create_package \"VIP月卡\" 29.9 30 1000 0 \"包含30天VIP和1000VIP积分\"")
	}

	// 解析参数
	name := args[0]
	if strings.HasPrefix(name, "\"") && strings.HasSuffix(name, "\"") {
		name = strings.Trim(name, "\"")
	}

	price, err := strconv.ParseFloat(args[1], 64)
	if err != nil {
		return c.Send("价格必须是数字")
	}

	vipDays, err := strconv.ParseInt(args[2], 10, 32)
	if err != nil {
		return c.Send("VIP天数必须是数字")
	}

	vipPoints, err := strconv.ParseInt(args[3], 10, 32)
	if err != nil {
		return c.Send("VIP积分必须是数字")
	}

	permanentPoints, err := strconv.ParseInt(args[4], 10, 32)
	if err != nil {
		return c.Send("永久积分必须是数字")
	}

	// 可选的描述
	var description string
	if len(args) > 5 {
		description = strings.Join(args[5:], " ")
		if strings.HasPrefix(description, "\"") && strings.HasSuffix(description, "\"") {
			description = strings.Trim(description, "\"")
		}
	}

	// 创建套餐
	pkg, err := s.CreatePackage(name, price, int32(vipDays), int32(vipPoints), int32(permanentPoints), description)
	if err != nil {
		return c.Send(fmt.Sprintf("创建套餐失败: %v", err))
	}

	// 构建私发消息
	message := fmt.Sprintf("✅ 套餐创建成功!\n\n"+
		"🆔 套餐ID: %d\n"+
		"📦 套餐名称: %s\n"+
		"💰 价格: %.2f 元\n"+
		"⏰ VIP天数: %d天\n"+
		"💎 VIP积分: %d\n"+
		"🔥 永久积分: %d\n",
		pkg.ID, pkg.Name, pkg.Price, pkg.VipDays, pkg.VipPoints, pkg.PermanentPointBalance)

	if description != "" {
		message += fmt.Sprintf("📝 描述: %s\n", description)
	}

	// 尝试私发给管理员
	adminUser := &tele.User{ID: c.Sender().ID}
	_, err = c.Bot().Send(adminUser, message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
	if err != nil {
		err = c.Send("⚠️ 套餐已创建但私发失败")
	} else {
		err = c.Send("✅ 套餐已创建并私发给您")
	}

	return err
}

// AdminListPackages 管理员查看套餐列表命令
func (s *PayService) AdminListPackages(c tele.Context) error {
	packages, err := s.GetAllPackages()
	if err != nil {
		return c.Send(fmt.Sprintf("获取套餐列表失败: %v", err))
	}

	if len(packages) == 0 {
		return c.Send("📦 暂无套餐")
	}

	// 构建套餐列表消息
	message := "📦 套餐列表:\n\n"
	for _, pkg := range packages {
		status := "🟢 启用"
		if !pkg.IsActive {
			status = "🔴 禁用"
		}

		message += fmt.Sprintf("🆔 ID: %d | %s\n"+
			"📦 名称: %s\n"+
			"💰 价格: %.2f 元\n"+
			"⏰ VIP: %d天 | 💎 VIP积分: %d | 🔥 永久积分: %d\n\n",
			pkg.ID, status, pkg.Name, pkg.Price, pkg.VipDays, pkg.VipPoints, pkg.PermanentPointBalance)
	}

	return c.Send(message)
}

// AdminDeletePackage 管理员删除套餐命令
// 用法: /admin_delete_package <package_id>
func (s *PayService) AdminDeletePackage(c tele.Context) error {
	idStr := strings.TrimSpace(utils.ExtractArgs(c.Text()))
	if idStr == "" {
		return c.Send("用法: /admin_delete_package <package_id>\n例如: /admin_delete_package 1")
	}

	packageID, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		return c.Send("套餐ID必须是数字")
	}

	// 删除套餐
	err = s.DeletePackage(int32(packageID))
	if err != nil {
		return c.Send(fmt.Sprintf("删除套餐失败: %v", err))
	}

	return c.Send(fmt.Sprintf("✅ 套餐 ID:%d 已成功删除", packageID))
}

// AdminTogglePackage 管理员启用/禁用套餐命令
// 用法: /admin_toggle_package <package_id>
func (s *PayService) AdminTogglePackage(c tele.Context) error {
	idStr := strings.TrimSpace(utils.ExtractArgs(c.Text()))
	if idStr == "" {
		return c.Send("用法: /admin_toggle_package <package_id>\n例如: /admin_toggle_package 1")
	}

	packageID, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		return c.Send("套餐ID必须是数字")
	}

	// 切换套餐状态
	pkg, err := s.TogglePackageStatus(int32(packageID))
	if err != nil {
		return c.Send(fmt.Sprintf("切换套餐状态失败: %v", err))
	}

	status := "启用"
	if !pkg.IsActive {
		status = "禁用"
	}

	return c.Send(fmt.Sprintf("✅ 套餐 \"%s\" 已%s", pkg.Name, status))
}

// === 用户命令实现 ===

// ListPackages 用户查看可购买套餐列表
func (s *PayService) ListPackages(c tele.Context) error {
	packages, err := s.GetActivePackages()
	if err != nil {
		return c.Send(utils.GetI18nText(c, i18n.Keys.Common.Errors.General))
	}

	if len(packages) == 0 {
		return c.Send("📦 暂无可购买的套餐")
	}

	// 构建套餐列表消息
	message := "🛒 可购买套餐:\n\n"
	for _, pkg := range packages {
		message += fmt.Sprintf("🆔 ID: %d\n"+
			"📦 %s\n"+
			"💰 价格: %.2f 元\n"+
			"🎁 包含: ",
			pkg.ID, pkg.Name, pkg.Price)

		rewards := []string{}
		if pkg.VipDays > 0 {
			rewards = append(rewards, fmt.Sprintf("%d天VIP", pkg.VipDays))
		}
		if pkg.VipPoints > 0 {
			rewards = append(rewards, fmt.Sprintf("%d VIP积分", pkg.VipPoints))
		}
		if pkg.PermanentPointBalance > 0 {
			rewards = append(rewards, fmt.Sprintf("%d 永久积分", pkg.PermanentPointBalance))
		}

		if len(rewards) > 0 {
			message += strings.Join(rewards, " + ")
		} else {
			message += "无奖励"
		}

		if pkg.Description != nil && *pkg.Description != "" {
			message += fmt.Sprintf("\n📝 %s", *pkg.Description)
		}

		message += "\n\n"
	}

	message += "💡 使用 /buy <套餐ID> 购买套餐"

	return c.Send(message)
}

// === 服务方法实现 ===

// CreatePackage 创建套餐
func (s *PayService) CreatePackage(name string, price float64, vipDays, vipPoints, permanentPoints int32, description string) (*model.Package, error) {
	pkg := &model.Package{
		Name:                  name,
		Price:                 price,
		VipDays:               vipDays,
		VipPoints:             vipPoints,
		PermanentPointBalance: permanentPoints,
		IsActive:              true,
		SortOrder:             0,
	}

	if description != "" {
		pkg.Description = &description
	}

	err := s.svcCtx.PackageModel.Insert(s.ctx, nil, pkg)
	if err != nil {
		return nil, err
	}

	return pkg, nil
}

// GetAllPackages 获取所有套餐
func (s *PayService) GetAllPackages() ([]*model.Package, error) {
	return s.svcCtx.PackageModel.FindAll(s.ctx)
}

// GetActivePackages 获取启用的套餐
func (s *PayService) GetActivePackages() ([]*model.Package, error) {
	return s.svcCtx.PackageModel.FindActivePackages(s.ctx)
}

// GetPackage 获取套餐信息
func (s *PayService) GetPackage(id int32) (*model.Package, error) {
	return s.svcCtx.PackageModel.FindOne(s.ctx, id)
}

// DeletePackage 删除套餐
func (s *PayService) DeletePackage(id int32) error {
	return s.svcCtx.PackageModel.Delete(s.ctx, nil, id)
}

// TogglePackageStatus 切换套餐状态
func (s *PayService) TogglePackageStatus(id int32) (*model.Package, error) {
	pkg, err := s.svcCtx.PackageModel.FindOne(s.ctx, id)
	if err != nil {
		return nil, err
	}

	pkg.IsActive = !pkg.IsActive
	err = s.svcCtx.PackageModel.Update(s.ctx, nil, pkg)
	if err != nil {
		return nil, err
	}

	return pkg, nil
}
