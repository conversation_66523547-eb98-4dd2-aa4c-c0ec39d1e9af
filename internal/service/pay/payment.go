package pay

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/internal/utils"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// CreatePackagePayment 创建套餐支付订单
func (s *PayService) CreatePackagePayment(c tele.Context, packageID int32) error {
	// 检查EPUSDT是否启用
	if !s.svcCtx.Config.EPUSDT.Enabled {
		return c.Reply("❌ 支付服务暂不可用")
	}

	// 获取套餐信息
	pkg, err := s.svcCtx.PackageModel.FindOne(s.ctx, packageID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Reply("❌ 套餐不存在")
		}
		return c.Reply("❌ 获取套餐信息失败")
	}

	// 检查套餐是否启用
	if !pkg.IsActive {
		return c.Reply("❌ 该套餐暂不可购买")
	}

	// 验证金额
	minAmount := s.svcCtx.Config.EPUSDT.PaymentConfig.MinAmount
	maxAmount := s.svcCtx.Config.EPUSDT.PaymentConfig.MaxAmount
	if pkg.Price < minAmount {
		return c.Reply(fmt.Sprintf("❌ 套餐价格不能小于 %.2f 元", minAmount))
	}
	if pkg.Price > maxAmount {
		return c.Reply(fmt.Sprintf("❌ 套餐价格不能大于 %.2f 元", maxAmount))
	}

	userID := utils.GetUserID(c)

	// 生成订单ID
	orderID := s.generateOrderID(userID)

	// 创建支付订单
	response, err := s.epusdtClient.CreateOrder(orderID, pkg.Price)
	if err != nil {
		return c.Reply(fmt.Sprintf("❌ 创建订单失败: %v", err))
	}

	expirationTime := time.Now().Add(time.Duration(s.svcCtx.Config.EPUSDT.PaymentConfig.ExpirationTime) * time.Second)

	// 保存订单到数据库
	paymentOrder := &model.PaymentOrder{
		OrderID:        orderID,
		UserID:         userID,
		PackageID:      &packageID,
		Amount:         pkg.Price,
		ActualAmount:   response.Data.ActualAmount,
		Token:          response.Data.Token,
		Status:         0, // 待支付
		ExpirationTime: expirationTime,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	err = s.svcCtx.PaymentOrderModel.Insert(s.ctx, nil, paymentOrder)
	if err != nil {
		log.Printf("保存支付订单失败: %v", err)
		return c.Reply("❌ 创建订单失败，请稍后重试")
	}

	// 构建回复消息 - 发送钱包地址而不是支付链接
	message := fmt.Sprintf(
		"💰 套餐支付订单创建成功！\n\n"+
			"📦 套餐: %s\n"+
			"🆔 订单号: `%s`\n"+
			"💵 支付金额: %.2f CNY\n"+
			"💎 实际支付: %.6f USDT\n"+
			"📍 钱包地址: `%s`\n"+
			"⏰ 请在 %s 前完成支付\n\n"+
			"⚠️ 请复制钱包地址，使用USDT(TRC20)转账\n"+
			"💡 支付成功后会自动通知您",
		pkg.Name,
		orderID,
		pkg.Price,
		response.Data.ActualAmount,
		response.Data.Token,
		expirationTime.Format("15:04:05"),
	)

	// 发送消息并获取消息ID
	sentMessage, err := c.Bot().Send(c.Recipient(), message, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
	if err != nil {
		return err
	}

	// 启动goroutine处理订单超时
	go s.handlePaymentTimeout(orderID, sentMessage, expirationTime)

	return nil
}

// HandlePaymentCallback 处理支付回调
func (s *PayService) HandlePaymentCallback(callback *request.PaymentCallback) error {
	logger := logx.WithContext(s.ctx)

	// 验证签名
	if !s.epusdtClient.VerifyCallback(callback) {
		log.Printf("支付回调签名验证失败: OrderID=%s", callback.OrderID)
		return fmt.Errorf("invalid callback signature")
	}

	// 查找订单
	order, err := s.svcCtx.PaymentOrderModel.FindOneByOrderID(s.ctx, callback.OrderID)
	if err != nil {
		log.Printf("查找支付订单失败: OrderID=%s, Error=%v", callback.OrderID, err)
		return fmt.Errorf("order not found: %s", callback.OrderID)
	}

	// 检查支付状态
	if callback.Status == 2 { // 2表示支付成功
		// 在事务中更新订单状态并处理业务逻辑
		err = s.svcCtx.PaymentOrderModel.Trans(s.ctx, func(tx *gorm.DB) error {
			// 更新订单状态
			err := s.svcCtx.PaymentOrderModel.UpdateStatus(s.ctx, tx, callback.OrderID, 2, callback.TradeID, callback.BlockTransactionID)
			if err != nil {
				return fmt.Errorf("更新订单状态失败: %v", err)
			}

			// 如果订单关联了套餐，给用户添加套餐奖励
			if order.PackageID != nil {
				err := s.processPackageRewardInTx(tx, order.UserID, *order.PackageID, callback.OrderID)
				if err != nil {
					return fmt.Errorf("处理套餐奖励失败: %v", err)
				}
			}

			return nil
		})

		if err != nil {
			logger.Errorf("处理支付成功回调失败: OrderID=%s, Error=%v", callback.OrderID, err)
			return err
		}

		// 发送支付成功通知给用户
		go s.sendPaymentSuccessNotification(order.UserID, callback.OrderID, order.Amount, order.PackageID)

		logger.Infof("✅ 支付成功: OrderID=%s, UserID=%d, Amount=%.2f CNY, ActualAmount=%.6f USDT",
			callback.OrderID, order.UserID, order.Amount, callback.ActualAmount)
	} else {
		// 更新为其他状态（支付失败等）
		err = s.svcCtx.PaymentOrderModel.UpdateStatus(s.ctx, nil, callback.OrderID, int32(callback.Status), callback.TradeID, callback.BlockTransactionID)
		if err != nil {
			logger.Errorf("更新订单状态失败: OrderID=%s, Status=%d, Error=%v", callback.OrderID, callback.Status, err)
		}
	}

	return nil
}

// processPackageRewardInTx 在事务中处理套餐奖励
func (s *PayService) processPackageRewardInTx(tx *gorm.DB, userID int64, packageID int32, orderID string) error {
	// 获取套餐信息
	pkg, err := s.svcCtx.PackageModel.FindOne(s.ctx, packageID)
	if err != nil {
		return fmt.Errorf("获取套餐信息失败: %v", err)
	}

	// 如果套餐没有任何奖励，直接返回
	if pkg.VipDays == 0 && pkg.VipPoints == 0 && pkg.PermanentPointBalance == 0 {
		return nil
	}

	// 获取用户信息并加行锁
	userInfo, err := s.svcCtx.UserModel.FindOneByUserIDForUpdate(s.ctx, tx, userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 计算新的VIP过期时间
	if pkg.VipDays > 0 {
		now := time.Now()
		if userInfo.VipPointExpiredDate.Before(now) {
			userInfo.VipPointExpiredDate = now.AddDate(0, 0, int(pkg.VipDays))
		} else {
			userInfo.VipPointExpiredDate = userInfo.VipPointExpiredDate.AddDate(0, 0, int(pkg.VipDays))
		}
	}

	// 增加积分
	userInfo.VipPointBalance += pkg.VipPoints
	userInfo.PermanentPointBalance += pkg.PermanentPointBalance

	// 更新用户信息
	err = s.svcCtx.UserModel.Update(s.ctx, tx, userInfo)
	if err != nil {
		return fmt.Errorf("更新用户奖励失败: %v", err)
	}

	// 记录消费日志
	pointChange := &model.PointChange{
		Daily:     0,
		VIP:       pkg.VipPoints,
		Permanent: pkg.PermanentPointBalance,
	}

	var vipDaysChange *int32
	if pkg.VipDays > 0 {
		vipDaysChange = &pkg.VipDays
	}

	// 获取 TraceID 作为 SessionID
	sessionID := fmt.Sprintf("payment_%s", orderID)

	err = s.svcCtx.UserConsumptionLogModel.Insert(s.ctx, tx, &model.UserConsumptionLog{
		UserID:        userID,
		SessionID:     &sessionID,
		ChatID:        nil,
		MessageID:     nil,
		LogType:       "package_purchase",
		PointChange:   pointChange,
		VipDaysChange: vipDaysChange,
		Caption:       &pkg.Name,
	})

	if err != nil {
		return fmt.Errorf("记录消费日志失败: %v", err)
	}

	return nil
}

// sendPaymentSuccessNotification 发送支付成功通知
func (s *PayService) sendPaymentSuccessNotification(userID int64, orderID string, amount float64, packageID *int32) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("发送支付成功通知时发生panic: %v", r)
		}
	}()

	message := fmt.Sprintf(
		"🎉 支付成功！\n\n"+
			"🆔 订单号: %s\n"+
			"💵 支付金额: %.2f CNY\n"+
			"✅ 支付状态: 已完成\n"+
			"⏰ 完成时间: %s\n",
		orderID,
		amount,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	// 如果是套餐购买，添加套餐信息
	if packageID != nil {
		pkg, err := s.svcCtx.PackageModel.FindOne(s.ctx, *packageID)
		if err == nil {
			message += fmt.Sprintf("\n📦 购买套餐: %s\n", pkg.Name)

			rewards := []string{}
			if pkg.VipDays > 0 {
				rewards = append(rewards, fmt.Sprintf("%d天VIP", pkg.VipDays))
			}
			if pkg.VipPoints > 0 {
				rewards = append(rewards, fmt.Sprintf("%d VIP积分", pkg.VipPoints))
			}
			if pkg.PermanentPointBalance > 0 {
				rewards = append(rewards, fmt.Sprintf("%d 永久积分", pkg.PermanentPointBalance))
			}

			if len(rewards) > 0 {
				message += fmt.Sprintf("🎁 获得奖励: %s\n", strings.Join(rewards, " + "))
			}
		}
	}

	message += "\n感谢您的支付！"

	// 发送消息给用户
	_, err := s.svcCtx.Bot.Send(&tele.User{ID: userID}, message)
	if err != nil {
		log.Printf("发送支付成功通知失败: UserID=%d, OrderID=%s, Error=%v", userID, orderID, err)
	} else {
		log.Printf("支付成功通知已发送: UserID=%d, OrderID=%s", userID, orderID)
	}
}

// handlePaymentTimeout 处理支付超时
func (s *PayService) handlePaymentTimeout(orderID string, sentMessage *tele.Message, expirationTime time.Time) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("处理支付超时时发生panic: OrderID=%s, Error=%v", orderID, r)
		}
	}()

	// 等待到过期时间
	time.Sleep(time.Until(expirationTime))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 检查订单状态，如果已支付则不需要编辑消息
	order, err := s.svcCtx.PaymentOrderModel.FindOneByOrderID(ctx, orderID)
	if err != nil {
		log.Printf("查找支付订单失败: OrderID=%s, Error=%v", orderID, err)
		return
	}

	// 如果订单已支付成功，不需要编辑消息
	if order.Status == 2 {
		return
	}

	// 更新订单状态为过期
	err = s.svcCtx.PaymentOrderModel.UpdateStatus(ctx, nil, orderID, 4, "", "")
	if err != nil {
		log.Printf("更新订单状态为过期失败: OrderID=%s, Error=%v", orderID, err)
	}

	// 编辑消息为过期提示
	expiredMessage := "⚠️ 清单已过期请勿支付\n\n" +
		"💡 如需重新购买，请使用 /packages 查看套餐列表"

	_, err = s.svcCtx.Bot.Edit(sentMessage, expiredMessage)
	if err != nil {
		log.Printf("编辑过期消息失败: OrderID=%s, MessageID=%d, Error=%v", orderID, sentMessage.ID, err)
	} else {
		log.Printf("订单已过期，消息已更新: OrderID=%s, MessageID=%d", orderID, sentMessage.ID)
	}
}

// generateOrderID 生成订单ID
func (s *PayService) generateOrderID(userID int64) string {
	t := time.Now().Format("20060102150405") // 日期+时间到秒
	randPart := rand.Intn(10000)             // 0~9999 的随机数
	return fmt.Sprintf("%s%d%04d", t, userID, randPart)
}
