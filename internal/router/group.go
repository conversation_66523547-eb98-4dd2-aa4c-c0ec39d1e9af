package router

import (
	"fmt"
	"log"

	tele "gopkg.in/telebot.v4"
)

// Group 路由组 - 支持组级中间件
type Group struct {
	router      *Router
	middlewares []tele.MiddlewareFunc
	prefix      string
}

// Use 添加中间件到组
func (g *Group) Use(middlewares ...tele.MiddlewareFunc) *Group {
	g.middlewares = append(g.middlewares, middlewares...)
	return g
}

// Prefix 设置路由前缀
func (g *Group) Prefix(prefix string) *Group {
	g.prefix = prefix
	return g
}

// Group 创建子组
func (g *Group) Group() *Group {
	subGroup := &Group{
		router:      g.router,
		middlewares: make([]tele.MiddlewareFunc, len(g.middlewares)), // 继承父组中间件
		prefix:      g.prefix,
	}
	copy(subGroup.middlewares, g.middlewares)
	return subGroup
}

// AddCommand 在组中添加命令路由，支持为单个命令添加额外中间件
func (g *Group) AddCommand(commands []string, handler func(tele.Context) error, extraMiddlewares ...tele.MiddlewareFunc) {
	// 合并组中间件和额外中间件
	allMiddlewares := make([]tele.MiddlewareFunc, 0, len(g.middlewares)+len(extraMiddlewares))
	allMiddlewares = append(allMiddlewares, g.middlewares...)
	allMiddlewares = append(allMiddlewares, extraMiddlewares...)

	// 应用所有中间件
	wrappedHandler := handler
	for i := len(allMiddlewares) - 1; i >= 0; i-- {
		wrappedHandler = allMiddlewares[i](wrappedHandler)
	}

	for _, cmd := range commands {
		fullCmd := g.prefix + cmd
		g.router.bot.Handle("/"+fullCmd, wrappedHandler)

		middlewareInfo := ""
		if len(extraMiddlewares) > 0 {
			middlewareInfo = fmt.Sprintf(" +%d额外中间件", len(extraMiddlewares))
		}
		log.Printf("已注册组命令: /%s (组: %s%s)", fullCmd, g.getGroupInfo(), middlewareInfo)
	}
}

// AddTextCommand 在组中添加命令和文本路由，支持为单个命令添加额外中间件
func (g *Group) AddTextCommand(commands []string, handler func(tele.Context) error, extraMiddlewares ...tele.MiddlewareFunc) {
	// 合并组中间件和额外中间件
	allMiddlewares := make([]tele.MiddlewareFunc, 0, len(g.middlewares)+len(extraMiddlewares))
	allMiddlewares = append(allMiddlewares, g.middlewares...)
	allMiddlewares = append(allMiddlewares, extraMiddlewares...)

	// 应用所有中间件
	wrappedHandler := handler
	for i := len(allMiddlewares) - 1; i >= 0; i-- {
		wrappedHandler = allMiddlewares[i](wrappedHandler)
	}

	for _, cmd := range commands {
		fullCmd := g.prefix + cmd

		if fullCmd != "" {
			// 注册 /command 格式
			g.router.bot.Handle("/"+fullCmd, wrappedHandler)
		}

		// 同时注册为文本处理器 - 保留中间件信息，但标记为组路由
		g.router.textHandlers[fullCmd] = &HandlerWithMiddleware{
			Handler:    wrappedHandler, // 已经应用了所有中间件的处理器
			Middleware: allMiddlewares, // 保留所有中间件信息用于调试/日志
			IsGroup:    true,           // 标记为组路由，避免重复应用中间件
		}

		middlewareInfo := ""
		if len(extraMiddlewares) > 0 {
			middlewareInfo = fmt.Sprintf(" +%d额外中间件", len(extraMiddlewares))
		}
		log.Printf("已注册组命令和文本: /%s 和 %s (组: %s%s)", fullCmd, fullCmd, g.getGroupInfo(), middlewareInfo)
	}
}

// AddCallback 在组中添加回调路由，支持为单个回调添加额外中间件
func (g *Group) AddCallback(pattern string, handler func(tele.Context) error, extraMiddlewares ...tele.MiddlewareFunc) {
	// 合并组中间件和额外中间件
	allMiddlewares := make([]tele.MiddlewareFunc, 0, len(g.middlewares)+len(extraMiddlewares))
	allMiddlewares = append(allMiddlewares, g.middlewares...)
	allMiddlewares = append(allMiddlewares, extraMiddlewares...)

	// 使用 Router 的 AddCallback 方法，传入合并后的中间件
	g.router.AddCallback(pattern, handler, allMiddlewares...)

	middlewareInfo := ""
	if len(extraMiddlewares) > 0 {
		middlewareInfo = fmt.Sprintf(" +%d额外中间件", len(extraMiddlewares))
	}
	log.Printf("已注册组回调: %s (组: %s%s)", pattern, g.getGroupInfo(), middlewareInfo)
}

// getGroupInfo 获取组信息用于日志
func (g *Group) getGroupInfo() string {
	info := fmt.Sprintf("middlewares=%d", len(g.middlewares))
	if g.prefix != "" {
		info += ", prefix=" + g.prefix
	}
	return info
}
