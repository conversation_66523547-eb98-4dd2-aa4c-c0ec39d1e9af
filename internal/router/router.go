package router

import (
	"log"
	"strings"

	tele "gopkg.in/telebot.v4"
)

// Router 路由器结构体 - 专注于路由管理
type Router struct {
	bot              *tele.Bot
	textHandlers     map[string]*HandlerWithMiddleware
	callbackHandlers map[string]*HandlerWithMiddleware
	groups           []*Group
}

// NewRouter 创建新的路由器
func NewRouter(bot *tele.Bot) *Router {
	return &Router{
		bot:              bot,
		textHandlers:     make(map[string]*HandlerWithMiddleware),
		callbackHandlers: make(map[string]*HandlerWithMiddleware),
		groups:           make([]*Group, 0),
	}
}

// AddCommand 添加命令路由 - 只注册 /command 格式
func (r *Router) AddCommand(commands []string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	for _, cmd := range commands {
		r.bot.Handle("/"+cmd, handler, m...)
		log.Printf("已注册命令: /%s", cmd)
	}
}

// AddTextCommand 添加命令和文本路由 - 同时注册 /command 和文本格式
func (r *Router) AddTextCommand(commands []string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	for _, cmd := range commands {

		// 注册 /command 格式
		if cmd != "" {
			r.bot.Handle("/"+cmd, handler, m...)
		}

		// 同时注册为文本处理器
		r.textHandlers[strings.ToLower(cmd)] = &HandlerWithMiddleware{
			Handler:    handler,
			Middleware: m,
			IsGroup:    false, // 普通路由，中间件在调用时应用
		}

		log.Printf("已注册命令和文本: /%s 和 %s", cmd, cmd)
	}
}

// AddTextMessage 添加文本消息路由
func (r *Router) AddTextMessage(handler func(tele.Context) error) {
	r.bot.Handle(tele.OnText, handler)
	log.Printf("已注册文本消息处理器")
}

// AddMediaMessage 添加媒体消息路由
func (r *Router) AddMediaMessage(handler func(tele.Context) error) {
	r.bot.Handle(tele.OnMedia, handler)
	log.Printf("已注册媒体消息处理器")
}

// AddCallback 添加回调路由 - 支持模式匹配
func (r *Router) AddCallback(pattern string, handler func(tele.Context) error, m ...tele.MiddlewareFunc) {
	// 如果是第一次注册回调，注册统一的回调分发器
	if len(r.callbackHandlers) == 0 {
		r.bot.Handle(tele.OnCallback, r.HandleCallbackDispatch)
		log.Printf("已注册回调分发器")
	}

	// 注册模式处理器
	r.callbackHandlers[pattern] = &HandlerWithMiddleware{
		Handler:    handler,
		Middleware: m,
		IsGroup:    false,
	}
	log.Printf("已注册回调模式: %s", pattern)
}

// AddCallbackGlobal 添加全局回调处理器（向后兼容）
func (r *Router) AddCallbackGlobal(handler func(tele.Context) error) {
	r.bot.Handle(tele.OnCallback, handler)
	log.Printf("已注册全局回调处理器")
}

// GetHandlerInfo 获取指定命令的处理器信息（用于调试）
func (r *Router) GetHandlerInfo(command string) map[string]interface{} {
	if handler, exists := r.textHandlers[strings.ToLower(command)]; exists {
		info := handler.GetInfo()
		info["command"] = command
		return info
	}
	return map[string]interface{}{
		"command":     command,
		"exists":      false,
		"description": "命令不存在",
	}
}

// HandleTextMessage 统一文本消息处理器
func (r *Router) HandleTextMessage(c tele.Context) error {
	text := strings.TrimSpace(c.Text())
	if text == "" {
		return nil
	}

	cleanText := strings.TrimPrefix(text, "/")

	// 精确匹配
	if handler, exists := r.textHandlers[strings.ToLower(cleanText)]; exists {
		return handler.Call(c)
	}

	// 命令+参数匹配
	for cmd, handler := range r.textHandlers {
		if strings.HasPrefix(strings.ToLower(cleanText), cmd+" ") {
			return handler.Call(c)
		}
	}

	// 如果没有匹配的处理器，看看能否匹配AI聊天默认规则，否则返回nil
	return r.AIChatFallback(c)
}

// HandleMediaMessage 统一媒体消息处理器
func (r *Router) HandleMediaMessage(c tele.Context) error {
	caption := strings.TrimSpace(c.Message().Caption)
	if caption == "" {
		return nil
	}

	// 去除"/"前缀
	cleanCaption := strings.TrimPrefix(caption, "/")

	// 精确匹配
	if handler, exists := r.textHandlers[strings.ToLower(cleanCaption)]; exists {
		return handler.Call(c)
	}

	// 命令+参数匹配
	for cmd, handler := range r.textHandlers {
		if strings.HasPrefix(strings.ToLower(cleanCaption), cmd+" ") {
			log.Printf("找到带参数的命令处理器: %s", cmd)
			return handler.Call(c)
		}
	}

	// 如果没有匹配的处理器，看看能否匹配AI聊天默认规则，否则返回nil
	return r.AIChatFallback(c)
}

func (r *Router) AIChatFallback(c tele.Context) error {
	if c.Message().Text == "" && c.Message().Caption == "" {
		return nil
	}
	if (c.Message().IsReply() && c.Message().ReplyTo.Sender.Username == r.bot.Me.Username) || c.Chat().Type == tele.ChatPrivate {
		h, exists := r.textHandlers[""]
		if exists && h != nil {
			return h.Call(c)
		}
	}
	return nil
}

// HandleCallbackDispatch 统一回调分发器
func (r *Router) HandleCallbackDispatch(c tele.Context) error {
	if c.Callback() == nil {
		return nil
	}

	// 处理回调数据格式，fix原来库的onCallback问题
	unique := c.Callback().Unique
	data := c.Callback().Data
	if unique == "" && data != "" {
		d := strings.TrimSpace(c.Callback().Data)

		// 拆分回调数据
		parts := strings.SplitN(d, "|", 2)
		unique = parts[0]
		payload := ""
		if len(parts) > 1 {
			payload = parts[1]
		}

		// 覆写 Callback 内的 Unique 和 Data 字段
		c.Callback().Unique = unique
		c.Callback().Data = payload
	}

	// 精确匹配
	if handler, exists := r.callbackHandlers[unique]; exists {
		return handler.Call(c)
	}

	// 模式匹配（支持通配符）
	for pattern, handler := range r.callbackHandlers {
		if matchCallbackPattern(pattern, unique) {
			log.Printf("找到匹配的回调处理器: %s -> %s", unique, pattern)
			return handler.Call(c)
		}
	}

	// 没有找到匹配的处理器
	log.Printf("未找到回调处理器: %s", unique)
	return c.Respond(&tele.CallbackResponse{
		Text:      "Unknown callback",
		ShowAlert: false,
	})
}

// matchCallbackPattern 匹配回调模式
func matchCallbackPattern(pattern, unique string) bool {
	// 支持简单的通配符匹配
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(unique, prefix)
	}

	// 精确匹配
	return pattern == unique
}

// Group 创建路由组
func (r *Router) Group() *Group {
	group := &Group{
		router:      r,
		middlewares: make([]tele.MiddlewareFunc, 0),
		prefix:      "",
	}
	r.groups = append(r.groups, group)
	return group
}
