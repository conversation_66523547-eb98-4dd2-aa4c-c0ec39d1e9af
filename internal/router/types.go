package router

import tele "gopkg.in/telebot.v4"

// HandlerWithMiddleware 包含处理器和其专用中间件的结构体
type HandlerWithMiddleware struct {
	Handler    func(tele.Context) error
	Middleware []tele.MiddlewareFunc
	IsGroup    bool // 标记是否为组路由，组路由中间件已预先应用
}

// Call 调用处理器，如果不是组路由则应用中间件
func (h *HandlerWithMiddleware) Call(c tele.Context) error {
	if h.IsGroup {
		// 组路由，中间件已预先应用，直接调用
		return h.Handler(c)
	}

	// 普通路由，需要应用中间件
	handler := h.Handler
	for i := len(h.Middleware) - 1; i >= 0; i-- {
		handler = h.Middleware[i](handler)
	}
	return handler(c)
}

// GetInfo 获取处理器信息（用于调试）
func (h *HandlerWithMiddleware) GetInfo() map[string]interface{} {
	return map[string]interface{}{
		"exists":           true,
		"middleware_count": len(h.Middleware),
		"is_group":         h.IsGroup,
		"description":      "处理器信息",
	}
}
