package utils

import (
	"errors"
	"strings"

	"github.com/go-sql-driver/mysql"
	"github.com/lib/pq"
)

func IsDuplicateEntryError(err error) bool {
	if err == nil {
		return false
	}

	// ✅ MySQL
	var mysqlErr *mysql.MySQLError
	if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
		return true
	}

	// ✅ PostgreSQL
	var pgErr *pq.Error
	if errors.As(err, &pgErr) && pgErr.Code == "23505" {
		return true
	}

	// ✅ SQLite or fallback
	msg := err.Error()
	return strings.Contains(msg, "Duplicate entry") || // MySQL fallback
		strings.Contains(msg, "duplicate key value violates unique constraint") || // PostgreSQL fallback
		strings.Contains(msg, "UNIQUE constraint failed") // SQLite
}
