package utils

import (
	"strings"
)

func ExtractArgs(text string) string {
	text = strings.TrimSpace(text)

	// 处理 /command args 格式
	if strings.HasPrefix(text, "/") {
		parts := strings.SplitN(text, " ", 2)
		if len(parts) > 1 {
			return strings.TrimSpace(parts[1])
		}
		return ""
	}

	// 处理 command args 格式（纯文本）
	// 简单地查找第一个空格后的内容
	parts := strings.SplitN(text, " ", 2)
	if len(parts) > 1 {
		return strings.TrimSpace(parts[1])
	}

	return ""
}
