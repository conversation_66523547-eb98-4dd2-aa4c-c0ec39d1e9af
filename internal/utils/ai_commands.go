package utils

import (
	"log"
	"strings"
	"telegram-chatbot-go/internal/config"
)

// ParseAICommands 解析AI命令配置，返回所有启用命令的名称和别名列表
func ParseAICommands(aiCommands map[string]config.AICommand) []string {
	var allCommands []string
	commandMap := make(map[string]bool) // 用于去重

	for commandName, commandConfig := range aiCommands {
		// 跳过未启用的命令
		if !commandConfig.Enable {
			continue
		}

		// 添加主命令名称
		if !commandMap[commandName] {
			allCommands = append(allCommands, commandName)
			commandMap[commandName] = true
		}

		// 添加别名
		for _, alias := range commandConfig.Aliases {
			if !commandMap[alias] {
				allCommands = append(allCommands, alias)
				commandMap[alias] = true
			}
		}
	}

	log.Printf("解析到 %d 个AI命令: %v", len(allCommands), allCommands)
	return allCommands
}

// GetAICommandConfig 根据命令文本获取对应的AI命令配置
// 返回配置和匹配的命令名称
func GetAICommandConfig(text string, aiCommands map[string]config.AICommand) (*config.AICommand, string) {
	// 清理输入文本
	cleanText := strings.TrimSpace(text)
	cleanText = strings.TrimPrefix(cleanText, "/")

	// 提取命令部分（去掉参数）
	commandPart := cleanText
	if spaceIndex := strings.Index(cleanText, " "); spaceIndex != -1 {
		commandPart = cleanText[:spaceIndex]
	}

	commandPart = strings.ToLower(commandPart)

	// 首先尝试精确匹配主命令名称
	for commandName, commandConfig := range aiCommands {
		if !commandConfig.Enable {
			continue
		}

		if strings.ToLower(commandName) == commandPart {
			return &commandConfig, commandName
		}
	}

	// 然后尝试匹配别名
	for commandName, commandConfig := range aiCommands {
		if !commandConfig.Enable {
			continue
		}

		for _, alias := range commandConfig.Aliases {
			if strings.ToLower(alias) == commandPart {
				return &commandConfig, commandName
			}
		}
	}

	return nil, ""
}

// GetAICommandType 根据命令文本获取AI命令类型
func GetAICommandType(text string, aiCommands map[string]config.AICommand) string {
	commandConfig, _ := GetAICommandConfig(text, aiCommands)
	if commandConfig != nil {
		return commandConfig.Type
	}
	return ""
}

// IsAICommand 检查给定文本是否是AI命令
func IsAICommand(text string, aiCommands map[string]config.AICommand) bool {
	commandConfig, _ := GetAICommandConfig(text, aiCommands)
	return commandConfig != nil
}
