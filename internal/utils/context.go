package utils

import (
	"context"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/types"

	"github.com/google/uuid"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"
	oteltrace "go.opentelemetry.io/otel/trace"
	tele "gopkg.in/telebot.v4"
)

// 定义Context中的key常量
const (
	ServiceContextKey = "service_context"
	UserLangKey       = "user_lang"
	TimeoutContextKey = "timeout_context"
)

// CreateTraceContext 创建一个带有 TraceID 的 context，使其能够被 logx 正确识别
func CreateTraceContext(baseCtx context.Context, traceID string) context.Context {
	// 处理 UUID 格式的 TraceID（移除连字符）
	hexTraceID := strings.ReplaceAll(traceID, "-", "")
	if len(hexTraceID) > 32 {
		hexTraceID = hexTraceID[:32]
	}

	// 使用 OpenTelemetry 标准方式注入 TraceID
	tid, err := oteltrace.TraceIDFromHex(hexTraceID)
	if err != nil {
		// 如果解析失败，返回原 context
		return baseCtx
	}

	// 创建 SpanContext，但不设置 SpanID（这样日志中就不会显示 span 字段）
	spanCtx := oteltrace.NewSpanContext(oteltrace.SpanContextConfig{
		TraceID: tid,
		// 不设置 SpanID，这样日志中就不会显示 span 字段
	})

	// 将 SpanContext 注入到 Context 中
	return oteltrace.ContextWithSpanContext(baseCtx, spanCtx)
}

// CreateNewTraceContext 创建一个全新的带有 TraceID 的 context
func CreateNewTraceContext(baseCtx context.Context) (context.Context, string) {
	traceID := uuid.NewString()
	ctx := CreateTraceContext(baseCtx, traceID)
	return ctx, traceID
}

// SetupContextWithTrace 为 tele.Context 设置带有 TraceID 的 context
// 这个函数专门给中间件使用
func SetupContextWithTrace(c tele.Context, svcCtx *svc.ServiceContext, timeout context.Context) string {
	// 生成 TraceID
	traceID := uuid.NewString()

	// 存储 ServiceContext
	c.Set(ServiceContextKey, svcCtx)

	// 创建带有 TraceID 的 context
	ctx := CreateTraceContext(timeout, traceID)

	// 保留兼容性：继续设置原有的方式（某些业务代码可能依赖此方式）
	ctx = context.WithValue(ctx, trace.TraceIdKey, &span{traceId: traceID, spanId: "0"})

	// 存储到 tele.Context 中
	c.Set(TimeoutContextKey, ctx)

	return traceID
}

// 保留原有的span结构，用于向后兼容
type span struct {
	traceId string
	spanId  string
}

func (s *span) TraceId() string { return s.traceId }
func (s *span) SpanId() string  { return s.spanId }

// GetServiceContext 从Context中获取ServiceContext
func GetServiceContext(c tele.Context) *svc.ServiceContext {

	if svcCtx, ok := c.Get(ServiceContextKey).(*svc.ServiceContext); ok {
		return svcCtx
	}
	return nil
}

// GetUserLanguage 从Context中获取用户语言
func GetUserLanguage(c tele.Context) string {
	if lang, ok := c.Get(UserLangKey).(string); ok {
		return lang
	}
	return "en"
}

// GetI18nText 便捷函数：直接从Context获取翻译文本
func GetI18nText(c tele.Context, key string) string {
	svcCtx := GetServiceContext(c)
	if svcCtx == nil {
		return key
	}
	userLang := GetUserLanguage(c)
	return svcCtx.I18n.GetText(key, userLang)
}

// GetI18nTextWithArgs 便捷函数：直接从Context获取带参数的翻译文本
func GetI18nTextWithArgs(c tele.Context, key string, args map[string]interface{}) string {
	svcCtx := GetServiceContext(c)
	if svcCtx == nil {
		return key
	}
	userLang := GetUserLanguage(c)
	return svcCtx.I18n.GetTextWithArgs(key, args, userLang)
}

func GetGeneralError(c tele.Context) string {
	return GetI18nText(c, i18n.Keys.Common.Errors.General)
}

// GetMissingPromptError 便捷函数：生成缺少提示词的错误消息
func GetMissingPromptError(c tele.Context) string {
	// 提取命令名称（去掉 / 前缀）
	commandText := strings.TrimSpace(c.Text())
	commandName := commandText
	if strings.HasPrefix(commandText, "/") {
		commandName = strings.TrimPrefix(commandText, "/")
	}

	// 使用国际化错误消息，支持参数替换
	return GetI18nTextWithArgs(c, i18n.Keys.Common.Errors.MissingPrompt, map[string]interface{}{
		"command": commandName,
	})
}

// GetMissingCodeError 便捷函数：生成缺少兑换码的错误消息
func GetMissingCodeError(c tele.Context) string {
	// 提取命令名称（去掉 / 前缀）
	commandText := strings.TrimSpace(c.Text())
	commandName := commandText
	if strings.HasPrefix(commandText, "/") {
		commandName = strings.TrimPrefix(commandText, "/")
	}

	// 使用国际化错误消息，支持参数替换
	return GetI18nTextWithArgs(c, i18n.Keys.Common.Errors.MissingCode, map[string]interface{}{
		"command": commandName,
	})
}

// SetUserLanguage 设置用户语言到Context中
func SetUserLanguage(c tele.Context, lang string) {
	c.Set(UserLangKey, lang)
}

// GetUserID 获取用户ID
func GetUserID(c tele.Context) int64 {
	if c.Sender() != nil {
		return c.Sender().ID
	}
	return 0
}

// GetUsername 获取用户名
func GetUsername(c tele.Context) string {
	if c.Sender() != nil {
		return c.Sender().Username
	}
	return ""
}

// GetChatID 获取聊天ID
func GetChatID(c tele.Context) int64 {
	if c.Chat() != nil {
		return c.Chat().ID
	}
	return 0
}

// IsPrivateChat 判断是否为私聊
func IsPrivateChat(c tele.Context) bool {
	if c.Chat() != nil {
		return c.Chat().Type == tele.ChatPrivate
	}
	return false
}

// IsGroupChat 判断是否为群聊
func IsGroupChat(c tele.Context) bool {
	if c.Chat() != nil {
		return c.Chat().Type == tele.ChatGroup || c.Chat().Type == tele.ChatSuperGroup
	}
	return false
}

// GetChatType 获取聊天类型
func GetChatType(c tele.Context) string {
	if c.Chat() != nil {
		switch c.Chat().Type {
		case tele.ChatPrivate:
			return "private"
		case tele.ChatGroup, tele.ChatSuperGroup:
			return "group"
		case tele.ChatChannel:
			return "channel"
		default:
			return "unknown"
		}
	}
	return "unknown"
}

// GetTimeoutContext 从Context中获取带超时的context.Context
func GetTimeoutContext(c tele.Context) context.Context {
	if ctx, ok := c.Get(TimeoutContextKey).(context.Context); ok {
		return ctx
	}
	// 如果没有设置，返回background context
	return context.Background()
}

// GetTraceID 从Context中获取TraceID（从 context.Context 中获取 OpenTelemetry TraceID）
func GetTraceID(c tele.Context) string {
	// 从 context 中获取 OpenTelemetry TraceID
	ctx := GetTimeoutContext(c)
	return trace.TraceIDFromContext(ctx)
}

// GetTraceIDFromContext 从 context.Context 中获取 TraceID（OpenTelemetry 方式）
func GetTraceIDFromContext(ctx context.Context) string {
	return trace.TraceIDFromContext(ctx)
}

// GetLoggerWithCtx 获取带有 context 的 logger
// 这是最方便的方式，自动从 tele.Context 中获取带 context 的 logger
func GetLoggerWithCtx(c tele.Context) logx.Logger {
	ctx := GetTimeoutContext(c)
	return logx.WithContext(ctx)
}

// GetDisplayName 获取用户显示名称
func GetDisplayName(c tele.Context) string {
	if c.Sender() == nil {
		return ""
	}

	displayName := c.Sender().FirstName
	if c.Sender().LastName != "" {
		displayName += " " + c.Sender().LastName
	}
	return displayName
}

// GetUserInfo 获取用户基本信息
func GetUserInfo(c tele.Context) (userID int64, userName, displayName string, chatID int64) {
	if c.Sender() == nil {
		return 0, "", "", 0
	}

	userID = c.Sender().ID
	userName = c.Sender().Username
	displayName = GetDisplayName(c)
	chatID = GetChatID(c)

	return
}

// GetMediaGroupFromContext 从Context中获取媒体组信息
func GetMediaGroupFromContext(c tele.Context) *types.MediaGroupInfo {
	return types.GetMediaGroupFromContext(c)
}
