package svc

import (
	"log"
	"telegram-chatbot-go/internal/config"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/request"
	"telegram-chatbot-go/pkg/markdown"
	"time"

	"github.com/SpectatorNan/gorm-zero/gormc/config/mysql"
	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm/logger"
)

// ServiceContext 服务上下文 - 参照go-zero的ServiceContext设计
type ServiceContext struct {
	Config                  config.Config
	I18n                    *i18n.Manager
	Bot                     *tele.Bot
	MarkdownRenderer        *markdown.Renderer
	OpenAIClient            *request.OpenAIClient
	ModerationClient        *request.ModerationClient
	SegmindClient           *request.SegmindClient
	ChatModel               model.ChatModel
	ChatMessageModel        model.ChatMessageModel
	UserModel               model.UserModel
	RedemptionCodeModel     model.RedemptionCodeModel
	UserConsumptionLogModel model.UserConsumptionLogModel
	PaymentOrderModel       model.PaymentOrderModel
	PackageModel            model.PackageModel
}

// NewServiceContext 创建服务上下文
func NewServiceContext(c config.Config, bot *tele.Bot) *ServiceContext {
	// 使用gorm-zero连接数据库
	db, err := mysql.Connect(c.Mysql)

	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 配置自定义的 GORM Logger
	gormLog := NewGormLogger().
		SetLogLevel(logger.Info).                // 设置为 Info 级别，可以看到 SQL 执行
		SetSlowThreshold(200 * time.Millisecond) // 设置慢查询阈值为 200ms

	// 设置 GORM Logger
	db.Logger = gormLog

	// 自动迁移数据库表，暂时不自动迁移
	err = db.AutoMigrate(&model.Chat{}, &model.ChatMessage{}, &model.User{}, &model.RedemptionCode{}, &model.UserConsumptionLog{}, &model.PaymentOrder{}, &model.Package{})
	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 创建模型
	chatModel := model.NewChatModel(db)
	chatMessageModel := model.NewChatMessageModel(db)
	userModel := model.NewUserModel(db)
	redemptionCodeModel := model.NewRedemptionCodeModel(db)
	userConsumptionLogModel := model.NewUserConsumptionLogModel(db)
	paymentOrderModel := model.NewPaymentOrderModel(db)
	packageModel := model.NewPackageModel(db)

	// 创建图片审核服务
	moderationClient, err := request.NewModerationClient(c)
	if err != nil {
		log.Printf("Warning: Failed to initialize moderation service: %v", err)
		// 不致命错误，继续运行但审核服务不可用
	}

	return &ServiceContext{
		Config:                  c,
		I18n:                    i18n.DefaultManager,
		Bot:                     bot,
		MarkdownRenderer:        markdown.NewRenderer(),
		OpenAIClient:            request.NewOpenAIClient(&c),
		ModerationClient:        moderationClient,
		SegmindClient:           request.NewSegmindClient(&c, bot.Token),
		ChatModel:               chatModel,
		ChatMessageModel:        chatMessageModel,
		UserModel:               userModel,
		RedemptionCodeModel:     redemptionCodeModel,
		UserConsumptionLogModel: userConsumptionLogModel,
		PaymentOrderModel:       paymentOrderModel,
		PackageModel:            packageModel,
	}
}
