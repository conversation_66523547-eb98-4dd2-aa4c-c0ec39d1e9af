package svc

import (
	"context"
	"errors"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GormLogger GORM 日志适配器，使用 go-zero 的 logx
type GormLogger struct {
	LogLevel      logger.LogLevel
	SlowThreshold time.Duration
}

// NewGormLogger 创建新的 GORM Logger
func NewGormLogger() *GormLogger {
	return &GormLogger{
		LogLevel:      logger.Info,            // 默认 Info 级别
		SlowThreshold: 200 * time.Millisecond, // 慢查询阈值
	}
}

// LogMode 设置日志级别
func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info 打印 Info 日志
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.Log<PERSON>l >= logger.Info {
		logx.WithContext(ctx).Infof("[GORM] "+msg, data...)
	}
}

// Warn 打印 Warn 日志
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		logx.WithContext(ctx).Infof("[GORM WARN] "+msg, data...)
	}
}

// Error 打印 Error 日志
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		logx.WithContext(ctx).Errorf("[GORM ERROR] "+msg, data...)
	}
}

// Trace 打印 SQL 执行日志
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	switch {
	case err != nil && l.LogLevel >= logger.Error:
		// 不记录 "record not found" 错误，这通常是正常的业务逻辑
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		logx.WithContext(ctx).Errorf("[GORM ERROR] SQL执行错误: %v | 耗时: %v | SQL: %s | 影响行数: %d",
			err, elapsed, sql, rows)
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		logx.WithContext(ctx).Slowf("[GORM SLOW] 慢查询警告 | 耗时: %v | 阈值: %v | SQL: %s | 影响行数: %d",
			elapsed, l.SlowThreshold, sql, rows)
	case l.LogLevel == logger.Info:
		logx.WithContext(ctx).Infof("[GORM] SQL执行 | 耗时: %v | SQL: %s | 影响行数: %d",
			elapsed, sql, rows)
	}
}

// SetSlowThreshold 设置慢查询阈值
func (l *GormLogger) SetSlowThreshold(threshold time.Duration) *GormLogger {
	l.SlowThreshold = threshold
	return l
}

// SetLogLevel 设置日志级别
func (l *GormLogger) SetLogLevel(level logger.LogLevel) *GormLogger {
	l.LogLevel = level
	return l
}
