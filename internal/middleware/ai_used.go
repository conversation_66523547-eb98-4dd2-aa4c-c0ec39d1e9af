package middleware

import (
	"context"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/utils"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
)

func AIUsedMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {

		// 如果处理成功，更新用户AI使用状态
		go func() {
			defer func() {
				if r := recover(); r != nil {
					logx.Errorf("❌ [AI_USED] 更新AI使用状态失败: %v", r)
				}
			}()

			if c.Sender() != nil {
				// 获取服务上下文
				svcCtx := utils.GetServiceContext(c)
				if svcCtx == nil {
					logx.Errorf("❌ [AI_USED] 无法获取服务上下文")
					return
				}

				// 获取上下文
				ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
				defer cancel()

				// 直接使用数据库模型更新AI使用状态
				userID := c.Sender().ID
				userInfo, err := svcCtx.UserModel.FindOneByUserID(ctx, userID)
				if err != nil {
					return
				}
				if userInfo.HasUsedAI {
					return
				}
				err = svcCtx.UserModel.UpdateUserFields(ctx, nil, userID, map[string]any{
					"has_used_ai": true,
				})
				if err != nil {
					return
				}

				// 记录AI使用状态变更日志
				var chatID *int64
				if c.Chat() != nil {
					chatID = &[]int64{c.Chat().ID}[0]
				}

				svcCtx.UserConsumptionLogModel.LogUserInfoChange(ctx, nil, &model.UserInfoChangeLogParams{
					UserID:     userID,
					ChatID:     chatID,
					ChangeType: "has_used_ai",
					OldValue:   &[]string{"false"}[0],
					NewValue:   &[]string{"true"}[0],
				})
			}
		}()

		return next(c)
	}
}
