package middleware

import (
	"context"
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
)

// CallbackAuthMiddleware Callback所有权验证中间件
// 确保只有发起callback的用户才能点击使用
// 约定：.Data 字段格式为 "userID|data1|data2..."，第一段为用户ID，空字符串表示不鉴权
func CallbackAuthMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {
		// 只处理callback请求
		if c.Callback() == nil {
			return next(c)
		}

		ctx := utils.GetTimeoutContext(c)
		if ctx == nil {
			ctx = context.Background()
		}

		// 获取当前用户ID
		if c.Sender() == nil {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Errorf("🚫 [CALLBACK_AUTH] 拒绝访问: 无发送者信息")
			return c.Respond(&tele.CallbackResponse{
				Text:      "❌ 无法验证用户身份",
				ShowAlert: true,
			})
		}

		callbackData := c.Callback().Data
		currentUserID := c.Sender().ID

		// 如果没有Data字段，说明是无鉴权的callback，允许访问（向后兼容）
		if callbackData == "" {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("⚠️ [CALLBACK_AUTH] callback '%s' 没有所有者信息，允许访问", c.Callback().Unique)
			return next(c)
		}

		// 解析callback data，格式：userID|data1|data2...
		parts := strings.Split(callbackData, "|")
		if len(parts) == 0 {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("⚠️ [CALLBACK_AUTH] callback data 为空，允许访问")
			return next(c)
		}

		authUserIDStr := parts[0]

		// 如果第一段为空字符串，表示不需要鉴权
		if authUserIDStr == "" {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("⚠️ [CALLBACK_AUTH] callback '%s' 标记为无鉴权，允许访问", c.Callback().Unique)
			return next(c)
		}

		// 解析用户ID
		ownerUserID, err := strconv.ParseInt(authUserIDStr, 10, 64)
		if err != nil {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("⚠️ [CALLBACK_AUTH] 警告: callback data 第一段 '%s' 不是有效的用户ID，允许访问", authUserIDStr)
			return next(c)
		}

		// 验证用户ID范围
		if ownerUserID <= 0 || ownerUserID >= 10000000000 {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("⚠️ [CALLBACK_AUTH] 警告: callback data 第一段 '%s' 不是有效的用户ID范围，允许访问", authUserIDStr)
			return next(c)
		}

		// 验证所有权
		if currentUserID != ownerUserID {
			// Try to get context from middleware, fallback to logx if not available
			logx.WithContext(ctx).Infof("🚫 [CALLBACK_AUTH] 拒绝访问: 用户 %d 尝试使用用户 %d 的callback '%s'",
				currentUserID, ownerUserID, c.Callback().Unique)

			// 获取用户语言并返回友好错误信息
			errorMsg := utils.GetI18nText(c, i18n.Keys.Common.Errors.CallbackNotOwned)
			if errorMsg == "" {
				errorMsg = "❌ 这个按钮不是为您准备的"
			}

			return c.Respond(&tele.CallbackResponse{
				Text:      errorMsg,
				ShowAlert: true,
			})
		}

		// Try to get context from middleware, fallback to logx if not available
		logx.WithContext(ctx).Infof("✅ [CALLBACK_AUTH] 允许访问: 用户 %d 使用自己的callback '%s'",
			currentUserID, c.Callback().Unique)

		return next(c)
	}
}
