package middleware

import (
	"errors"
	"fmt"
	"strings"
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

// 预定义的错误类型
var (
	ErrInvalidCommand     = errors.New("无效的命令")
	ErrPermissionDenied   = errors.New("权限不足")
	ErrVipRequired        = errors.New("需要VIP权限")
	ErrRateLimited        = errors.New("请求过于频繁")
	ErrServiceUnavailable = errors.New("服务暂时不可用")
)

// ErrorHandler 错误处理中间件
func ErrorHandlerMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {
		err := next(c)
		if err != nil {
			handleError(c, err)
		}
		return nil // 返回nil防止telebot继续传播错误
	}
}

// handleError 统一错误处理
func handleError(c tele.Context, err error) {
	// 记录错误信息
	logError(c, err)

	// 根据错误类型返回不同的用户友好消息
	var responseMsg string

	switch {
	case errors.Is(err, ErrInvalidCommand):
		responseMsg = "❌ 无效的命令格式\n\n💡 请输入 `help` 查看可用命令"

	case errors.Is(err, ErrPermissionDenied):
		responseMsg = "🚫 抱歉，您没有权限执行此操作\n\n👤 请联系管理员获取权限"

	case errors.Is(err, ErrVipRequired):
		responseMsg = "💎 此功能需要VIP权限\n\n⬆️ 请升级到VIP获取更多功能"

	case errors.Is(err, ErrRateLimited):
		responseMsg = "⏰ 请求过于频繁，请稍后再试\n\n⏳ 建议等待几秒后重新操作"

	case errors.Is(err, ErrServiceUnavailable):
		responseMsg = "🔧 服务暂时不可用\n\n🔄 请稍后重试，我们正在修复中"

	case strings.Contains(err.Error(), "network"):
		responseMsg = "🌐 网络连接异常\n\n📡 请检查网络连接后重试"

	case strings.Contains(err.Error(), "timeout"):
		responseMsg = "⏱️ 请求超时\n\n🔄 请稍后重试"

	default:
		// 通用错误消息
		responseMsg = utils.GetI18nText(c, i18n.Keys.Common.Errors.General)
	}

	// 发送错误消息给用户
	sendErrorResponse(c, responseMsg)
}

// logError 记录错误日志
func logError(c tele.Context, err error) {
	var userInfo string
	if c != nil && c.Sender() != nil {
		userInfo = fmt.Sprintf("User ID: %d, Username: %s",
			c.Sender().ID, c.Sender().Username)
	} else {
		userInfo = "Unknown user"
	}

	var commandInfo string
	if c != nil && c.Message() != nil {
		commandInfo = fmt.Sprintf("Command: %s", c.Text())
	} else {
		commandInfo = "Unknown command"
	}

	// 使用新的工具函数获取带 trace 的 logger
	logger := utils.GetLoggerWithCtx(c)
	logger.Errorf("❌ [ERROR] Time: %s", time.Now().Format("2006-01-02 15:04:05"))
	logger.Errorf("❌ [ERROR] %s", userInfo)
	logger.Errorf("❌ [ERROR] %s", commandInfo)
	logger.Errorf("❌ [ERROR] Error: %v", err)
}

// sendErrorResponse 发送错误响应
func sendErrorResponse(c tele.Context, message string) {
	if c == nil {
		return
	}

	logger := utils.GetLoggerWithCtx(c)

	// 确保发送错误消息时不会panic
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("❌ Failed to send error response: %v", r)
		}
	}()

	if err := c.Send(message); err != nil {
		logger.Errorf("❌ Failed to send error message to user: %v", err)
	}
}

// NewError 创建新的错误（方便在业务代码中使用）
func NewError(message string) error {
	return errors.New(message)
}

// WrapError 包装错误（添加上下文信息）
func WrapError(err error, context string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", context, err)
}
