package middleware

import (
	"context"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

// ContextMiddleware Context注入中间件
func ContextMiddleware(svcCtx *svc.ServiceContext) func(tele.HandlerFunc) tele.HandlerFunc {
	return func(next tele.HandlerFunc) tele.HandlerFunc {
		return func(c tele.Context) error {
			// 创建带超时的 ctx
			ctx, cancel := context.WithTimeout(
				context.Background(),
				svcCtx.Config.Bot.ContextTimeout,
			)
			defer cancel()

			// 使用 utils 中的函数设置带有 TraceID 的 context
			// 这个函数会处理所有的 TraceID 注入逻辑
			utils.SetupContextWithTrace(c, svcCtx, ctx)

			return next(c)
		}
	}
}
