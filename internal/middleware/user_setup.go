package middleware

import (
	"context"
	"errors"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
	"gorm.io/gorm"
)

// UserSetupMiddleware 用户设置中间件
// 确保用户存在并设置语言环境
func UserSetupMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {
		// 检查是否有发送者信息
		if c.Sender() == nil {
			return next(c)
		}

		// 获取服务上下文
		svcCtx := utils.GetServiceContext(c)
		if svcCtx == nil {
			return errors.New("无法获取服务上下文")
		}

		// 获取上下文
		ctx := utils.GetTimeoutContext(c)

		// 获取用户信息
		userInfo := extractUserInfo(c)

		// 查找或创建用户
		user, err := findOrCreateUser(svcCtx, ctx, userInfo, c)
		if err != nil {
			return err
		}

		// 设置用户语言
		if user != nil {
			utils.SetUserLanguage(c, user.Language)
		}

		return next(c)
	}
}

// userInfo 用户信息结构体
type userInfo struct {
	UserID      int64
	UserName    string
	DisplayName string
	ChatID      int64
}

// extractUserInfo 提取用户信息
func extractUserInfo(c tele.Context) *userInfo {
	sender := c.Sender()
	displayName := sender.FirstName
	if sender.LastName != "" {
		displayName += " " + sender.LastName
	}

	return &userInfo{
		UserID:      sender.ID,
		UserName:    sender.Username,
		DisplayName: displayName,
		ChatID:      c.Chat().ID,
	}
}

// findOrCreateUser 查找或创建用户
func findOrCreateUser(svcCtx *svc.ServiceContext, ctx context.Context, info *userInfo, c tele.Context) (*model.User, error) {

	// 查找用户
	user, err := svcCtx.UserModel.FindOneByUserID(ctx, info.UserID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if user == nil {
		// 用户不存在，创建新用户
		return createNewUser(svcCtx, ctx, info, c)
	}

	// 用户存在，更新必要信息
	return updateUserIfNeeded(svcCtx, ctx, user, info, c)
}

// createNewUser 创建新用户
func createNewUser(svcCtx *svc.ServiceContext, ctx context.Context, info *userInfo, c tele.Context) (*model.User, error) {

	now := time.Now()
	newUser := &model.User{
		UserID:                info.UserID,
		ChatID:                &info.ChatID,
		UserName:              &info.UserName,
		DisplayName:           &info.DisplayName,
		IsBlocked:             false,
		DailyPointBalance:     svcCtx.Config.Operation.DailyPoints,
		DailyPointLastResetAt: now,
		VipPointBalance:       0,
		VipPointExpiredDate:   now,
		PermanentPointBalance: svcCtx.Config.Operation.PermanentPoints,
		HasUsedAI:             false,
		Language:              "en",
	}

	err := svcCtx.UserModel.Insert(ctx, nil, newUser)
	if err != nil && !utils.IsDuplicateEntryError(err) {
		return nil, err
	}

	// 记录用户注册日志
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 日志记录失败不影响主流程
			}
		}()

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// 记录注册时获得的积分
		pointChange := &model.PointChange{
			Daily:     svcCtx.Config.Operation.DailyPoints,
			VIP:       0,
			Permanent: svcCtx.Config.Operation.PermanentPoints,
		}

		// 检查是否有邀请人信息（注册后可能会被设置）
		registrationExtra := map[string]interface{}{
			"initial_daily_points":     svcCtx.Config.Operation.DailyPoints,
			"initial_permanent_points": svcCtx.Config.Operation.PermanentPoints,
		}

		// 获取 TraceID 作为 SessionID
		var sessionID *string
		if traceID := utils.GetTraceID(c); traceID != "" {
			sessionID = &traceID
		}

		var messageID *int64
		if c.Message() != nil {
			messageID = &[]int64{int64(c.Message().ID)}[0]
		}

		svcCtx.UserConsumptionLogModel.LogRegistration(ctx, nil, &model.RegistrationLogParams{
			SessionID:   sessionID,
			UserID:      info.UserID,
			ChatID:      &info.ChatID,
			MessageID:   messageID,
			UserName:    &info.UserName,
			DisplayName: &info.DisplayName,
			Language:    "en",
			PointChange: pointChange,
			ExtraData:   registrationExtra,
		})
	}()

	return newUser, nil
}

// updateUserIfNeeded 如果需要则更新用户信息
func updateUserIfNeeded(svcCtx *svc.ServiceContext, ctx context.Context, user *model.User, info *userInfo, c tele.Context) (*model.User, error) {

	needUpdate := false
	var oldUserName, oldDisplayName *string

	if user.UserName == nil || *user.UserName != info.UserName {
		oldUserName = user.UserName
		user.UserName = &info.UserName
		needUpdate = true
	}

	if user.DisplayName == nil || *user.DisplayName != info.DisplayName {
		oldDisplayName = user.DisplayName
		user.DisplayName = &info.DisplayName
		needUpdate = true
	}

	if needUpdate {
		err := svcCtx.UserModel.UpdateUserFields(ctx, nil, user.UserID, map[string]any{
			"user_name":    user.UserName,
			"display_name": user.DisplayName,
		})
		if err != nil {
			return nil, err
		}

		// 异步记录用户信息变更日志
		go func() {
			defer func() {
				if r := recover(); r != nil {
					// 日志记录失败不影响主流程
				}
			}()

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			// 获取 TraceID 作为 SessionID
			var sessionID *string
			if traceID := utils.GetTraceID(c); traceID != "" {
				sessionID = &traceID
			}

			var messageID *int64
			if c.Message() != nil {
				messageID = &[]int64{int64(c.Message().ID)}[0]
			}

			// 记录用户名变更
			if oldUserName == nil || (user.UserName != nil && *oldUserName != *user.UserName) {
				oldValue := ""
				if oldUserName != nil {
					oldValue = *oldUserName
				}
				svcCtx.UserConsumptionLogModel.LogUserInfoChange(ctx, nil, &model.UserInfoChangeLogParams{
					SessionID:  sessionID,
					UserID:     user.UserID,
					ChatID:     &info.ChatID,
					MessageID:  messageID,
					ChangeType: "username",
					OldValue:   &oldValue,
					NewValue:   user.UserName,
				})
			}

			// 记录显示名变更
			if oldDisplayName == nil || (user.DisplayName != nil && *oldDisplayName != *user.DisplayName) {
				oldValue := ""
				if oldDisplayName != nil {
					oldValue = *oldDisplayName
				}
				svcCtx.UserConsumptionLogModel.LogUserInfoChange(ctx, nil, &model.UserInfoChangeLogParams{
					SessionID:  sessionID,
					UserID:     user.UserID,
					ChatID:     &info.ChatID,
					MessageID:  messageID,
					ChangeType: "display_name",
					OldValue:   &oldValue,
					NewValue:   user.DisplayName,
				})
			}
		}()
	}

	return user, nil
}
