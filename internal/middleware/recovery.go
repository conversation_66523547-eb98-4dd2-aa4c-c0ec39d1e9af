package middleware

import (
	"runtime/debug"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
)

// RecoveryMiddleware 全局panic恢复中间件
func RecoveryMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {
		defer func() {
			if r := recover(); r != nil {
				// 记录panic信息
				stack := debug.Stack()

				logx.Severef("🚨 [PANIC RECOVERED] Time: %s", time.Now().Format("2006-01-02 15:04:05"))
				logx.Severef("🚨 [PANIC RECOVERED] Error: %v", r)
				logx.Severef("🚨 [PANIC RECOVERED] Stack Trace:\n%s", string(stack))

				// 记录用户信息
				if c != nil && c.Sender() != nil {
					logx.Severef("🚨 [PANIC RECOVERED] User ID: %d, Username: %s",
						c.Sender().ID, c.Sender().Username)
				}

				// 发送用户友好的错误消息
				sendPanicResponse(c, r)
			}
		}()

		// 执行下一个处理器
		return next(c)
	}
}

// sendPanicResponse 发送panic恢复后的友好回复
func sendPanicResponse(c tele.Context, panicValue interface{}) {
	if c == nil {
		return
	}

	errorMsg := "😵 系统遇到了意外错误，我们已经记录了这个问题。\n\n" +
		"🔧 请稍后重试，或联系管理员获取帮助。\n" +
		"📝 错误已自动上报，我们会尽快修复。"

	// 尝试发送错误消息，如果发送失败也不要panic
	defer func() {
		if r := recover(); r != nil {
			logx.Severef("❌ Failed to send panic response: %v", r)
		}
	}()

	if err := c.Send(errorMsg); err != nil {
		logx.Severef("❌ Failed to send panic response to user: %v", err)
	}
}
