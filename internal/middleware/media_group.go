package middleware

import (
	"context"
	"sync"
	"telegram-chatbot-go/internal/types"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
)

// MediaGroupDebouncer 媒体组防抖处理器 - 高性能分片设计
type MediaGroupDebouncer struct {
	// 🎯 核心优化：分片锁，减少锁竞争
	shards    []*GroupShard
	shardMask uint32 // 用于快速计算分片索引
	config    MediaGroupConfig
	ctx       context.Context
	cancel    context.CancelFunc
	fullChain tele.HandlerFunc
}

// GroupShard 分片，每个分片独立加锁
type GroupShard struct {
	groups map[string]*MediaGroupBuffer
	mutex  sync.RWMutex // 只锁单个分片
}

// MediaGroupBuffer 媒体组缓冲区
type MediaGroupBuffer struct {
	Info         *types.MediaGroupInfo
	Timer        *time.Timer
	FirstContext tele.Context
	LastUpdated  int64 // 使用纳秒时间戳，避免time.Now()开销
}

// MediaGroupConfig 媒体组配置
type MediaGroupConfig struct {
	DebounceTime    time.Duration
	CleanupInterval time.Duration
	MaxGroupSize    int
	ShardCount      uint32 // 分片数量，必须是2的幂
}

// DefaultMediaGroupConfig 默认媒体组配置
var DefaultMediaGroupConfig = MediaGroupConfig{
	DebounceTime:    1500 * time.Millisecond,
	CleanupInterval: 15000 * time.Millisecond,
	MaxGroupSize:    10,
	ShardCount:      32, // 32个分片
}

// NewMediaGroupDebouncer 创建媒体组防抖处理器
func NewMediaGroupDebouncer(config MediaGroupConfig) *MediaGroupDebouncer {
	// 确保ShardCount是2的幂
	if config.ShardCount&(config.ShardCount-1) != 0 {
		config.ShardCount = 32 // 默认32
	}

	ctx, cancel := context.WithCancel(context.Background())

	processor := &MediaGroupDebouncer{
		shards:    make([]*GroupShard, config.ShardCount),
		shardMask: config.ShardCount - 1,
		config:    config,
		ctx:       ctx,
		cancel:    cancel,
	}

	// 初始化所有分片
	for i := uint32(0); i < config.ShardCount; i++ {
		processor.shards[i] = &GroupShard{
			groups: make(map[string]*MediaGroupBuffer),
		}
	}

	// 启动清理工作器
	go processor.cleanupWorker()

	return processor
}

// getShard 根据albumID获取对应分片
func (d *MediaGroupDebouncer) getShard(albumID string) *GroupShard {
	// 🎯 使用简单哈希函数快速分片
	hash := fnv32(albumID)
	return d.shards[hash&d.shardMask]
}

// fnv32 简单快速的哈希函数
func fnv32(s string) uint32 {
	hash := uint32(2166136261)
	for i := 0; i < len(s); i++ {
		hash ^= uint32(s[i])
		hash *= 16777619
	}
	return hash
}

// SetFullChain 设置完整链条
func (d *MediaGroupDebouncer) SetFullChain(fullChain tele.HandlerFunc) {
	d.fullChain = fullChain
}

// Process 处理消息 - 高性能版本
func (d *MediaGroupDebouncer) Process(c tele.Context) bool {
	msg := c.Message()
	if msg == nil || msg.AlbumID == "" || !isMediaMessage(msg) {
		return true // 普通消息，继续处理
	}

	albumID := msg.AlbumID
	now := time.Now().UnixNano()

	// 🎯 只锁对应的分片，而不是全局锁
	shard := d.getShard(albumID)
	shard.mutex.Lock()

	buffer := shard.groups[albumID]
	if buffer == nil {
		// 创建新组
		buffer = &MediaGroupBuffer{
			Info: &types.MediaGroupInfo{
				GroupID: albumID,
				Messages: []types.MediaMessageInfo{{
					MessageID: msg.ID,
					FileID:    extractFileID(msg),
					MediaType: getMediaType(msg),
				}},
				ChatID:  msg.Chat.ID,
				UserID:  msg.Sender.ID,
				Caption: msg.Caption,
			},
			FirstContext: c,
			LastUpdated:  now,
		}
		shard.groups[albumID] = buffer
		logx.Infof("📸 [MEDIA_GROUP] 创建新媒体组: %s", albumID)
	} else {
		// 检查大小限制
		if len(buffer.Info.Messages) >= d.config.MaxGroupSize {
			shard.mutex.Unlock()
			return true // 当作普通消息处理
		}

		// 添加到现有组
		buffer.Info.Messages = append(buffer.Info.Messages, types.MediaMessageInfo{
			MessageID: msg.ID,
			FileID:    extractFileID(msg),
			MediaType: getMediaType(msg),
		})
		buffer.LastUpdated = now

		if buffer.Info.Caption == "" && msg.Caption != "" {
			buffer.Info.Caption = msg.Caption
		}

		// 停止旧定时器
		if buffer.Timer != nil {
			buffer.Timer.Stop()
		}
		logx.Infof("📸 [MEDIA_GROUP] 添加到媒体组: %s (%d个)", albumID, len(buffer.Info.Messages))
	}

	// 设置新定时器
	buffer.Timer = time.AfterFunc(d.config.DebounceTime, func() {
		defer func() {
			if r := recover(); r != nil {
				logx.Errorf("❌ [MEDIA_GROUP] 处理完成组时发生panic: %v, albumID: %s", r, albumID)
			}
		}()
		d.handleCompletedGroup(albumID)
	})

	shard.mutex.Unlock()
	return false // 拦截消息
}

// handleCompletedGroup 处理完成的组 - 优化版
func (d *MediaGroupDebouncer) handleCompletedGroup(albumID string) {
	shard := d.getShard(albumID)

	// 🎯 最小化锁持有时间
	shard.mutex.Lock()
	buffer := shard.groups[albumID]
	if buffer == nil {
		shard.mutex.Unlock()
		return
	}
	delete(shard.groups, albumID)
	shard.mutex.Unlock()

	logx.Infof("📸 [MEDIA_GROUP] 处理完整媒体组: %s (%d个文件)",
		albumID, len(buffer.Info.Messages))

	// 🎯 在锁外执行耗时操作
	enhancedCtx := &MediaGroupContext{
		Context:    buffer.FirstContext,
		MediaGroup: buffer.Info,
	}

	if d.fullChain != nil {
		if err := d.fullChain(enhancedCtx); err != nil {
			logx.Errorf("❌ [MEDIA_GROUP] 处理错误: %v", err)
		}
	}
}

// cleanupWorker 清理工作器 - 并发清理
func (d *MediaGroupDebouncer) cleanupWorker() {
	defer func() {
		if r := recover(); r != nil {
			logx.Errorf("❌ [MEDIA_GROUP] 清理工作器发生panic: %v", r)
		}
	}()

	ticker := time.NewTicker(d.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			d.parallelCleanup()
		case <-d.ctx.Done():
			return
		}
	}
}

// parallelCleanup 并行清理所有分片
func (d *MediaGroupDebouncer) parallelCleanup() {
	var wg sync.WaitGroup
	expireThreshold := d.config.DebounceTime.Nanoseconds() * 2
	now := time.Now().UnixNano()

	// 🎯 并行清理所有分片
	for i := uint32(0); i < d.config.ShardCount; i++ {
		wg.Add(1)
		go func(shard *GroupShard) {
			defer func() {
				if r := recover(); r != nil {
					logx.Errorf("❌ [MEDIA_GROUP] 清理分片时发生panic: %v", r)
				}
				wg.Done()
			}()
			d.cleanupShard(shard, now, expireThreshold)
		}(d.shards[i])
	}

	wg.Wait()
}

// cleanupShard 清理单个分片
func (d *MediaGroupDebouncer) cleanupShard(shard *GroupShard, now, expireThreshold int64) {
	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	expired := make([]string, 0)
	for albumID, buffer := range shard.groups {
		if now-buffer.LastUpdated > expireThreshold {
			expired = append(expired, albumID)
			if buffer.Timer != nil {
				buffer.Timer.Stop()
			}
		}
	}

	for _, albumID := range expired {
		delete(shard.groups, albumID)
	}

	if len(expired) > 0 {
		logx.Infof("🧹 [MEDIA_GROUP] 清理过期组: %d个", len(expired))
	}
}

// Stop 优雅停止
func (d *MediaGroupDebouncer) Stop() {
	d.cancel()

	// 并行停止所有分片
	var wg sync.WaitGroup
	for i := uint32(0); i < d.config.ShardCount; i++ {
		wg.Add(1)
		go func(shard *GroupShard) {
			defer func() {
				if r := recover(); r != nil {
					logx.Errorf("❌ [MEDIA_GROUP] 停止分片时发生panic: %v", r)
				}
				wg.Done()
			}()
			shard.mutex.Lock()
			defer shard.mutex.Unlock()

			for _, buffer := range shard.groups {
				if buffer.Timer != nil {
					buffer.Timer.Stop()
				}
			}
			shard.groups = make(map[string]*MediaGroupBuffer)
		}(d.shards[i])
	}
	wg.Wait()
}

// MediaGroupContext 媒体组上下文
type MediaGroupContext struct {
	tele.Context
	MediaGroup *types.MediaGroupInfo
}

// GetMediaGroup 获取媒体组信息
func (c *MediaGroupContext) GetMediaGroup() *types.MediaGroupInfo {
	return c.MediaGroup
}

// IsMediaGroup 检查是否是媒体组
func (c *MediaGroupContext) IsMediaGroup() bool {
	return c.MediaGroup != nil && len(c.MediaGroup.Messages) > 1
}

// Message 返回带有正确caption的消息
func (c *MediaGroupContext) Message() *tele.Message {
	msg := c.Context.Message()
	if msg != nil && c.MediaGroup != nil && c.MediaGroup.Caption != "" {
		msgCopy := *msg
		msgCopy.Caption = c.MediaGroup.Caption
		return &msgCopy
	}
	return msg
}

// Text 返回媒体组的统一文本内容
func (c *MediaGroupContext) Text() string {
	// 如果是媒体组且有Caption，返回媒体组的Caption
	if c.MediaGroup != nil && c.MediaGroup.Caption != "" {
		return c.MediaGroup.Caption
	}
	// 否则调用原始的Text()方法
	return c.Context.Text()
}

// 全局实例
var (
	globalDebouncer     *MediaGroupDebouncer
	globalDebouncerOnce sync.Once
)

// GetMediaGroupDebouncer 获取全局防抖器实例
func GetMediaGroupDebouncer() *MediaGroupDebouncer {
	globalDebouncerOnce.Do(func() {
		globalDebouncer = NewMediaGroupDebouncer(DefaultMediaGroupConfig)
	})
	return globalDebouncer
}

// MediaGroupMiddleware 媒体组防抖中间件
func MediaGroupMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	processor := GetMediaGroupDebouncer()
	processor.SetFullChain(next)

	return func(c tele.Context) error {
		if processor.Process(c) {
			return next(c)
		}
		return nil
	}
}

// 公共API函数

// IsMediaGroup 检查是否是媒体组
func IsMediaGroup(c tele.Context) bool {
	if mgc, ok := c.(*MediaGroupContext); ok {
		return mgc.IsMediaGroup()
	}
	return false
}

// GetMediaGroupInfo 获取媒体组信息
func GetMediaGroupInfo(c tele.Context) *types.MediaGroupInfo {
	if mgc, ok := c.(*MediaGroupContext); ok {
		return mgc.GetMediaGroup()
	}
	return nil
}

// 辅助函数

// isMediaMessage 检查是否是媒体消息
func isMediaMessage(msg *tele.Message) bool {
	return msg.Photo != nil || msg.Video != nil || msg.Document != nil || msg.Audio != nil
}

// extractFileID 提取文件ID
func extractFileID(msg *tele.Message) string {
	if msg.Photo != nil {
		return msg.Photo.FileID
	}
	if msg.Video != nil {
		return msg.Video.FileID
	}
	if msg.Document != nil {
		return msg.Document.FileID
	}
	if msg.Audio != nil {
		return msg.Audio.FileID
	}
	return ""
}

// getMediaType 获取媒体类型
func getMediaType(msg *tele.Message) string {
	if msg.Photo != nil {
		return "photo"
	}
	if msg.Video != nil {
		return "video"
	}
	if msg.Document != nil {
		return "document"
	}
	if msg.Audio != nil {
		return "audio"
	}
	return "unknown"
}
