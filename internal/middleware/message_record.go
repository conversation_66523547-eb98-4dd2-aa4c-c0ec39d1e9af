package middleware

import (
	"context"
	"telegram-chatbot-go/internal/model"
	"telegram-chatbot-go/internal/svc"
	"telegram-chatbot-go/internal/types"
	"telegram-chatbot-go/internal/utils"
	"time"

	tele "gopkg.in/telebot.v4"
)

func MessageRecordMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
	return func(c tele.Context) error {
		logger := utils.GetLoggerWithCtx(c)

		// 异步保存用户消息
		go func(ctx tele.Context) {
			defer func() {
				if r := recover(); r != nil {
					logger.Errorf("保存用户消息时发生panic: %v", r)
				}
			}()

			err := saveUserMessage(ctx)
			if err != nil {
				logger.Errorf("保存用户消息失败: %v", err)
			}
		}(c)

		// 创建拦截器包装Context
		interceptor := &messageInterceptor{
			Context:      c,
			sentMessages: make([]*sentMessageRecord, 0),
		}

		// 执行处理器（使用包装的context）
		err := next(interceptor)

		// 异步保存Bot发送的消息
		if len(interceptor.sentMessages) > 0 {
			// 复制消息记录，防止并发访问问题
			records := make([]*sentMessageRecord, len(interceptor.sentMessages))
			copy(records, interceptor.sentMessages)

			go func(ctx tele.Context, msgs []*sentMessageRecord) {
				defer func() {
					if r := recover(); r != nil {
						logger.Errorf("保存机器人消息时发生panic: %v", r)
					}
				}()

				saveBotMessages(ctx, msgs)
			}(c, records)
		}

		return err
	}
}

// saveUserMessage 保存用户消息（支持媒体组）
func saveUserMessage(c tele.Context) error {
	// 跳过回调处理 - 回调不是用户输入的消息
	if c.Callback() != nil {
		return nil
	}

	if c.Message() == nil {
		return nil
	}

	// 为异步操作创建独立的context，避免使用已被取消的请求context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	svcCtx := utils.GetServiceContext(c)

	if svcCtx == nil {
		return nil
	}

	// 检查是否是媒体组消息
	if mediaGroup := types.GetMediaGroupFromContext(c); mediaGroup != nil {
		return saveMediaGroupMessage(ctx, svcCtx, mediaGroup, c)
	}

	// 普通消息处理
	return saveSimpleMessage(ctx, svcCtx, c)
}

// saveMediaGroupMessage 保存媒体组消息
func saveMediaGroupMessage(ctx context.Context, svcCtx *svc.ServiceContext, mediaGroup *types.MediaGroupInfo, c tele.Context) error {
	logger := utils.GetLoggerWithCtx(c)

	// 提取所有图片URL
	var photoURLs []string
	for _, media := range mediaGroup.Messages {
		if media.MediaType == "photo" {
			photoURLs = append(photoURLs, media.FileID)
		}
	}

	var replyToID *int64

	if c.Message().ReplyTo != nil {
		id := int64(c.Message().ReplyTo.ID)
		replyToID = &id
	}

	// 创建媒体组消息
	msg := &model.ChatMessage{
		ChatID:           mediaGroup.ChatID,
		MessageID:        &[]int64{int64(mediaGroup.Messages[0].MessageID)}[0], // 使用第一个消息的ID
		FromType:         "user",
		UserID:           &mediaGroup.UserID,
		ReplyToMessageID: replyToID,
	}

	if mediaGroup.Caption != "" {
		msg.MessageText = &mediaGroup.Caption
	}

	if err := msg.SetPhotoURLs(photoURLs); err != nil {
		return err
	}

	err := svcCtx.ChatMessageModel.Insert(ctx, nil, msg)

	if err != nil {
		logger.Errorf("创建媒体组消息失败: %v", err)
		return err
	}

	return nil
}

// saveSimpleMessage 保存非媒体组消息
func saveSimpleMessage(ctx context.Context, svcCtx *svc.ServiceContext, c tele.Context) error {
	msg := c.Message()

	// 提取消息文本（普通文本或媒体标题）
	text := msg.Text
	if text == "" && msg.Caption != "" {
		text = msg.Caption
	}

	// 提取图片URL列表（如果有）
	var photoURLs []string
	if msg.Photo != nil {
		// Telegram图片有多种尺寸，这里取最大的
		photoURLs = append(photoURLs, msg.Photo.FileID)
	}

	var replyToID *int64

	if c.Message().ReplyTo != nil {
		id := int64(c.Message().ReplyTo.ID)
		replyToID = &id
	}
	// 创建用户消息
	message := &model.ChatMessage{
		ChatID:           msg.Chat.ID,
		MessageID:        &[]int64{int64(msg.ID)}[0],
		FromType:         "user",
		UserID:           &msg.Sender.ID,
		ReplyToMessageID: replyToID,
	}

	if text != "" {
		message.MessageText = &text
	}

	if err := message.SetPhotoURLs(photoURLs); err != nil {
		return err
	}

	return svcCtx.ChatMessageModel.Insert(ctx, nil, message)
}

// saveBotMessages 保存Bot发送的消息
func saveBotMessages(c tele.Context, records []*sentMessageRecord) {
	logger := utils.GetLoggerWithCtx(c)

	logger.Infof("📝 [MESSAGE_RECORD] 开始保存Bot消息，共%d条记录", len(records))
	// 保存所有Bot发送的消息
	for _, record := range records {
		if record.Error == nil { // 只保存成功发送的消息

			// 创建一个模拟的tele.Message对象来传递给SaveBotMessage
			sentMsg := &tele.Message{
				ID:   record.MessageID,
				Chat: &tele.Chat{ID: record.ChatID},
			}

			// 提取图片FileID（如果是图片消息）
			var photoFileIDs []string
			if record.MessageType == "photo" && len(record.FileIDs) > 0 {
				photoFileIDs = record.FileIDs
			}

			saveBotMessage(c, sentMsg, record.Text, photoFileIDs, int64(record.ReplyToMessageID))
		} else {
			logger.Errorf("❌ [MESSAGE_RECORD] 跳过失败的消息，错误: %v", record.Error)
		}
	}
}

// saveBotMessage 保存Bot发送的消息
func saveBotMessage(c tele.Context, sentMsg *tele.Message, messageText string, photoURLs []string, replyToID int64) error {
	logger := utils.GetLoggerWithCtx(c)

	if sentMsg == nil {
		return nil
	}

	// 为异步操作创建独立的context，避免使用已被取消的请求context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	svcCtx := utils.GetServiceContext(c)

	if svcCtx == nil {
		return nil
	}

	var rid *int64
	if replyToID != 0 {
		rid = &replyToID
	}

	// 创建Bot消息
	message := &model.ChatMessage{
		ChatID:           sentMsg.Chat.ID,
		MessageID:        &[]int64{int64(sentMsg.ID)}[0],
		FromType:         "bot",
		UserID:           nil, // Bot消息没有UserID
		ReplyToMessageID: rid,
	}

	if messageText != "" {
		message.MessageText = &messageText
	}

	if err := message.SetPhotoURLs(photoURLs); err != nil {
		return err
	}

	err := svcCtx.ChatMessageModel.Insert(ctx, nil, message)

	if err != nil {
		logger.Errorf("创建Bot消息失败: %v", err)
		return err
	}

	return nil
}

// sentMessageRecord 发送消息记录
type sentMessageRecord struct {
	MessageID        int
	Text             string
	ChatID           int64
	UserID           int64
	SentAt           time.Time
	MessageType      string // text, photo, video, audio, document, sticker, animation, voice
	ReplyToMessageID int    // 0表示不是回复消息
	Error            error
	FileIDs          []string // 存储文件ID列表，用于重建图片URL
}

// messageInterceptor 消息拦截器
type messageInterceptor struct {
	tele.Context
	sentMessages []*sentMessageRecord
}

// GetMediaGroup 实现MediaGroupGetter接口
func (m *messageInterceptor) GetMediaGroup() *types.MediaGroupInfo {
	// 使用统一的接口检测
	return types.GetMediaGroupFromContext(m.Context)
}

// IsMediaGroup 实现MediaGroupGetter接口
func (m *messageInterceptor) IsMediaGroup() bool {
	// 使用统一的接口检测
	return types.GetMediaGroupFromContext(m.Context) != nil
}

// Send 拦截Send方法
func (m *messageInterceptor) Send(what interface{}, opts ...interface{}) error {
	record := &sentMessageRecord{
		SentAt:           time.Now(),
		ChatID:           m.Chat().ID,
		ReplyToMessageID: 0, // 默认不是回复
	}

	if m.Sender() != nil {
		record.UserID = m.Sender().ID
	}

	// 使用Bot.Send获取发送后的消息对象
	sentMsg, err := m.Bot().Send(m.Chat(), what, opts...)

	record.Error = err

	if err == nil && sentMsg != nil {
		record.MessageID = sentMsg.ID

		// 根据消息类型提取内容和设置类型
		switch v := what.(type) {
		case string:
			record.Text = v
			record.MessageType = "text"
		case *tele.Photo:
			record.Text = v.Caption
			record.MessageType = "photo"
			// 尝试从多个可能的字段获取FileID
			fileID := extractFileIDFromMessage(sentMsg)
			if fileID != "" {
				record.FileIDs = []string{fileID}
			} else {
			}
		case *tele.Video:
			record.Text = v.Caption
			record.MessageType = "video"
			if sentMsg.Video != nil {
				record.FileIDs = []string{sentMsg.Video.FileID}
			}
		case *tele.Audio:
			record.Text = v.Caption
			record.MessageType = "audio"
			if sentMsg.Audio != nil {
				record.FileIDs = []string{sentMsg.Audio.FileID}
			}
		case *tele.Document:
			record.Text = v.Caption
			record.MessageType = "document"
			if sentMsg.Document != nil {
				record.FileIDs = []string{sentMsg.Document.FileID}
			}
		case *tele.Sticker:
			record.MessageType = "sticker"
			if sentMsg.Sticker != nil {
				record.FileIDs = []string{sentMsg.Sticker.FileID}
			}
		case *tele.Animation:
			record.Text = v.Caption
			record.MessageType = "animation"
			if sentMsg.Animation != nil {
				record.FileIDs = []string{sentMsg.Animation.FileID}
			}
		case *tele.Voice:
			record.Text = v.Caption
			record.MessageType = "voice"
			if sentMsg.Voice != nil {
				record.FileIDs = []string{sentMsg.Voice.FileID}
			}
		default:
			record.MessageType = "other"
		}
	}

	// 添加到记录列表
	m.sentMessages = append(m.sentMessages, record)

	return err
}

// Reply 拦截Reply方法
func (m *messageInterceptor) Reply(what interface{}, opts ...interface{}) error {
	record := &sentMessageRecord{
		SentAt:           time.Now(),
		ChatID:           m.Chat().ID,
		ReplyToMessageID: m.Message().ID, // 设置回复的消息ID
	}

	if m.Sender() != nil {
		record.UserID = m.Sender().ID
	}

	// 使用Bot.Reply获取发送后的消息对象
	sentMsg, err := m.Bot().Reply(m.Message(), what, opts...)
	record.Error = err

	if err == nil && sentMsg != nil {
		record.MessageID = sentMsg.ID

		// 根据消息类型提取内容和设置类型（与Send方法相同的逻辑）
		switch v := what.(type) {
		case string:
			record.Text = v
			record.MessageType = "text"
		case *tele.Photo:
			record.Text = v.Caption
			record.MessageType = "photo"
			// 尝试从多个可能的字段获取FileID
			fileID := extractFileIDFromMessage(sentMsg)
			if fileID != "" {
				record.FileIDs = []string{fileID}
			} else {
			}
		case *tele.Video:
			record.Text = v.Caption
			record.MessageType = "video"
			if sentMsg.Video != nil {
				record.FileIDs = []string{sentMsg.Video.FileID}
			}
		case *tele.Audio:
			record.Text = v.Caption
			record.MessageType = "audio"
			if sentMsg.Audio != nil {
				record.FileIDs = []string{sentMsg.Audio.FileID}
			}
		case *tele.Document:
			record.Text = v.Caption
			record.MessageType = "document"
			if sentMsg.Document != nil {
				record.FileIDs = []string{sentMsg.Document.FileID}
			}
		case *tele.Sticker:
			record.MessageType = "sticker"
			if sentMsg.Sticker != nil {
				record.FileIDs = []string{sentMsg.Sticker.FileID}
			}
		case *tele.Animation:
			record.Text = v.Caption
			record.MessageType = "animation"
			if sentMsg.Animation != nil {
				record.FileIDs = []string{sentMsg.Animation.FileID}
			}
		case *tele.Voice:
			record.Text = v.Caption
			record.MessageType = "voice"
			if sentMsg.Voice != nil {
				record.FileIDs = []string{sentMsg.Voice.FileID}
			}
		default:
			record.MessageType = "other"
		}
	}

	m.sentMessages = append(m.sentMessages, record)
	return err
}

// extractFileIDFromMessage 从消息对象中提取FileID，尝试多个可能的字段
func extractFileIDFromMessage(msg *tele.Message) string {
	if msg == nil {
		return ""
	}

	// 按优先级检查不同的媒体类型
	if msg.Photo != nil {
		return msg.Photo.FileID
	}

	if msg.Document != nil {
		return msg.Document.FileID
	}

	if msg.Video != nil {
		return msg.Video.FileID
	}

	if msg.Audio != nil {
		return msg.Audio.FileID
	}

	if msg.Sticker != nil {
		return msg.Sticker.FileID
	}

	if msg.Animation != nil {
		return msg.Animation.FileID
	}

	if msg.Voice != nil {
		return msg.Voice.FileID
	}

	return ""
}
