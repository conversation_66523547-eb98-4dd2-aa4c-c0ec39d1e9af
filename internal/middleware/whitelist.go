package middleware

import (
	"fmt"
	"telegram-chatbot-go/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
	tele "gopkg.in/telebot.v4"
)

// Whitelist 创建白名单中间件
// 只允许指定的用户ID访问
func Whitelist(allowedUserIDs ...int64) tele.MiddlewareFunc {
	allowedMap := make(map[int64]bool)
	for _, id := range allowedUserIDs {
		allowedMap[id] = true
	}

	return func(next tele.HandlerFunc) tele.HandlerFunc {
		return func(c tele.Context) error {
			// Try to get context from middleware, fallback to logx if not available
			if ctx := utils.GetTimeoutContext(c); ctx != nil {
				logger := logx.WithContext(ctx)

				if c.Sender() == nil {
					logger.Infof("🚫 [WHITELIST] 拒绝访问: 无发送者信息")
					return c.Send("🚫 访问被拒绝：无法识别用户")
				}

				userID := c.Sender().ID
				if !allowedMap[userID] {
					logger.Infof("🚫 [WHITELIST] 拒绝访问: 用户 %d (@%s) 不在白名单中",
						userID, c.Sender().Username)
					return c.Send(fmt.Sprintf("🚫 访问被拒绝：您的用户ID (%d) 不在授权列表中", userID))
				}

				logger.Infof("✅ [WHITELIST] 允许访问: 用户 %d (@%s)",
					userID, c.Sender().Username)
			} else {
				if c.Sender() == nil {
					logx.Errorf("🚫 [WHITELIST] 拒绝访问: 无发送者信息")
					return c.Send("🚫 访问被拒绝：无法识别用户")
				}

				userID := c.Sender().ID
				if !allowedMap[userID] {
					logx.Infof("🚫 [WHITELIST] 拒绝访问: 用户 %d (@%s) 不在白名单中",
						userID, c.Sender().Username)
					return c.Send(fmt.Sprintf("🚫 访问被拒绝：您的用户ID (%d) 不在授权列表中", userID))
				}

				logx.Infof("✅ [WHITELIST] 允许访问: 用户 %d (@%s)",
					userID, c.Sender().Username)
			}
			return next(c)
		}
	}
}

// AdminOnly 创建管理员专用中间件
// 只允许管理员访问
func AdminOnly(adminIDs ...int64) tele.MiddlewareFunc {
	return Whitelist(adminIDs...)
}

// VipOnly 创建VIP专用中间件
// 只允许VIP用户访问
func VipOnly(vipIDs ...int64) tele.MiddlewareFunc {
	return Whitelist(vipIDs...)
}

// GroupAdminOnly 创建群组管理员专用中间件
// 只允许群组管理员访问
func GroupAdminOnly() tele.MiddlewareFunc {
	return func(next tele.HandlerFunc) tele.HandlerFunc {
		return func(c tele.Context) error {
			// 只在群组中生效
			if c.Chat().Type == tele.ChatPrivate {
				return next(c)
			}

			logger := utils.GetLoggerWithCtx(c)

			// 检查是否是群组管理员
			member, err := c.Bot().ChatMemberOf(c.Chat(), c.Sender())
			if err != nil {
				logger.Infof("🚫 [GROUP_ADMIN] 获取用户权限失败: %v", err)
				return c.Send("🚫 无法验证您的管理员权限")
			}

			if member.Role != tele.Administrator && member.Role != tele.Creator {
				logger.Infof("🚫 [GROUP_ADMIN] 拒绝访问: 用户 %d (@%s) 不是群组管理员",
					c.Sender().ID, c.Sender().Username)
				return c.Send("🚫 此功能仅限群组管理员使用")
			}

			logger.Infof("✅ [GROUP_ADMIN] 允许访问: 管理员 %d (@%s)",
				c.Sender().ID, c.Sender().Username)
			return next(c)
		}
	}
}

// PrivateOnly 创建私聊专用中间件
// 只允许在私聊中使用
func PrivateOnly() tele.MiddlewareFunc {
	return func(next tele.HandlerFunc) tele.HandlerFunc {
		return func(c tele.Context) error {
			logger := utils.GetLoggerWithCtx(c)

			if c.Chat().Type != tele.ChatPrivate {
				logger.Infof("🚫 [PRIVATE_ONLY] 拒绝访问: 非私聊环境 (聊天类型: %s)", c.Chat().Type)
				return c.Send("🚫 此功能仅限私聊使用")
			}

			logger.Infof("✅ [PRIVATE_ONLY] 允许访问: 私聊用户 %d (@%s)",
				c.Sender().ID, c.Sender().Username)
			return next(c)
		}
	}
}

// GroupOnly 创建群组专用中间件
// 只允许在群组中使用
func GroupOnly() tele.MiddlewareFunc {
	return func(next tele.HandlerFunc) tele.HandlerFunc {
		return func(c tele.Context) error {
			logger := utils.GetLoggerWithCtx(c)

			if c.Chat().Type == tele.ChatPrivate {
				logger.Infof("🚫 [GROUP_ONLY] 拒绝访问: 私聊环境")
				return c.Send("🚫 此功能仅限群组使用")
			}

			logger.Infof("✅ [GROUP_ONLY] 允许访问: 群组 %d", c.Chat().ID)
			return next(c)
		}
	}
}
