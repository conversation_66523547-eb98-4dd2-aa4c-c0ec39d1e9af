package menu

import (
	"telegram-chatbot-go/internal/i18n"
	"telegram-chatbot-go/internal/utils"

	tele "gopkg.in/telebot.v4"
)

func GetMainKeyboard(c tele.Context) *tele.ReplyMarkup {
	return &tele.ReplyMarkup{
		ReplyKeyboard: [][]tele.ReplyButton{
			{
				{Text: utils.GetI18nText(c, i18n.Keys.Keyboard.Language)},
				{Text: utils.GetI18nText(c, i18n.Keys.Keyboard.Instructions)},
			},
			{
				{Text: utils.GetI18nText(c, i18n.Keys.Keyboard.ChannelNotifications)},
				{Text: utils.GetI18nText(c, i18n.Keys.Keyboard.GetInviteCode)},
				{Text: utils.GetI18nText(c, i18n.Keys.Keyboard.GroupManagement)},
			},
		},
		ResizeKeyboard:  true,
		OneTimeKeyboard: false,
	}
}
