package types

import tele "gopkg.in/telebot.v4"

// MediaGroupInfo 媒体组信息
type MediaGroupInfo struct {
	GroupID  string
	Messages []MediaMessageInfo
	Caption  string
	ChatID   int64
	UserID   int64
}

// MediaMessageInfo 媒体消息信息
type MediaMessageInfo struct {
	MessageID int
	FileID    string
	MediaType string
}

// MediaGroupContext 定义媒体组Context接口
type MediaGroupContext interface {
	tele.Context
	GetMediaGroup() *MediaGroupInfo
	IsMediaGroup() bool
}

// MediaGroupGetter 定义媒体组获取接口（用于包装类型）
type MediaGroupGetter interface {
	GetMediaGroup() *MediaGroupInfo
	IsMediaGroup() bool
}

// GetMediaGroupFromContext 从Context中安全地提取媒体组信息
func GetMediaGroupFromContext(c tele.Context) *MediaGroupInfo {
	// 直接检查是否实现了MediaGroupGetter接口
	if mgc, ok := c.(MediaGroupGetter); ok && mgc.IsMediaGroup() {
		return mgc.GetMediaGroup()
	}

	return nil
}

// IsMediaGroupContext 检查Context是否包含媒体组
func IsMediaGroupContext(c tele.Context) bool {
	mgc, ok := c.(MediaGroupContext)
	return ok && mgc.IsMediaGroup()
}
