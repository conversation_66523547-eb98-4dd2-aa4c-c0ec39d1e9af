package i18n

// TranslationKeys 翻译键定义
type TranslationKeys struct{}

// CommonTranslationKeys 通用翻译键
type CommonTranslationKeys struct {
	Errors ErrorsKeys
}

type ErrorsKeys struct {
	InvalidCommand      string
	PermissionDenied    string
	VipRequired         string
	General             string
	CommandFailed       string
	MissingPrompt       string
	MissingCode         string
	CallbackNotOwned    string
	InsufficientBalance string
	ServiceUnavailable  string
}

// StartTranslationKeys start命令翻译键
type StartTranslationKeys struct {
	Welcome WelcomeKeys
}

type WelcomeKeys struct {
	Message        string
	MessagePhotoID string
}

// KeyboardTranslationKeys 键盘翻译键
type KeyboardTranslationKeys struct {
	Language             string
	Instructions         string
	ChannelNotifications string
	GetInviteCode        string
	GroupManagement      string
	BackToMenu           string
}

// InstructionsTranslationKeys 说明翻译键
type InstructionsTranslationKeys struct {
	Title   string
	Content string
	PhotoID string
}

// LanguageTranslationKeys 语言翻译键
type LanguageTranslationKeys struct {
	Choose  string
	Changed string
	Error   string
	Current string
}

// InviteTranslationKeys 邀请翻译键
type InviteTranslationKeys struct {
	Start InviteStartKeys
	Error InviteErrorKeys
}

type InviteStartKeys struct {
	Link        string
	Success     string
	Fail        string
	InvitedBy   string
	LinkPhotoID string
}

type InviteErrorKeys struct {
	SelfInvite     string
	AlreadyInvited string
	TimeLimit      string
}

// GroupTranslationKeys 群组翻译键
type GroupTranslationKeys struct {
	Management GroupManagementKeys
	Help       GroupHelpKeys
	Binding    GroupBindingKeys
	Setting    GroupSettingKeys
}

type GroupManagementKeys struct {
	Tutorial      string
	AddBot        string
	PrevPage      string
	NextPage      string
	Refresh       string
	Help          string
	GroupInfo     string
	DeleteBinding string
	BackToList    string
	UnbindSuccess string
	UnbindFailed  string
	GroupNotFound string
}

type GroupHelpKeys struct {
	Title   string
	Content string
	Back    string
}

type GroupBindingKeys struct {
	Success     string
	Failed      string
	SuccessUser string
}

type GroupSettingKeys struct {
}

// VipTranslationKeys VIP翻译键
type VipTranslationKeys struct {
	Status    VipStatusKeys
	ExpiresAt string
}

type VipStatusKeys struct {
	Active   string
	Inactive string
	Expired  string
}

// ChannelTranslationKeys 频道翻译键
type ChannelTranslationKeys struct {
	Notification        string
	NotificationPhotoID string
}

// ChatAdsTranslationKeys 聊天广告翻译键
type ChatAdsTranslationKeys struct {
	Commands        ChatAdsCommandsKeys
	InvalidParams   string
	InvalidCode     string
	RemoveSuccess   string
	RemoveFailed    string
	GenerateSuccess string
	Error           string
}

type ChatAdsCommandsKeys struct {
	RemoveChatAds         string
	GenerateRemoveAdsCode string
}

// GpiKeys gpi命令翻译键
type GpiKeys struct {
	Instruction string
	Result      string
	NoPhoto     string
}

// InfoKeys info命令翻译键
type InfoKeys struct {
	Title            string
	UserID           string
	Username         string
	Language         string
	VipStatus        string
	RemainingPower   string
	VipStatusNormal  string
	VipStatusVip     string
	VipStatusExpired string
}

// SkKeys sk命令翻译键
type SkKeys struct {
	Verifying   string
	Success     string
	Invalid     string
	AlreadyUsed string
}

// SignInKeys 签到命令翻译键
type SignInKeys struct {
	Success         string
	AlreadySignedIn string
	CannotSignIn    string
	CheckStatus     string
}

// AdminKeys 管理员命令翻译键
type AdminKeys struct {
	ModerateImage AdminModerateImageKeys
}

type AdminModerateImageKeys struct {
	Usage string
}

// AICommandKeys AI命令翻译键
type AICommandKeys struct {
	Draw       DrawKeys
	FC         FCKeys
	BR         BRKeys
	BE         BEKeys
	Moderation ModerationKeys
}

// ModerationKeys 审核翻译键
type ModerationKeys struct {
	Failed     string
	Rejected   string
	Passed     string
	Processing string
}

// DrawKeys 生图命令翻译键
type DrawKeys struct {
	Processing string
	Success    string
	Failed     string
	NoPrompt   string
}

// FCKeys 换脸命令翻译键
type FCKeys struct {
	Processing   string
	Success      string
	Failed       string
	NeedTwoPhoto string
}

// BRKeys 背景替换命令翻译键
type BRKeys struct {
	Processing     string
	Success        string
	Failed         string
	NeedTwoPhoto   string
	SameSizeNeeded string
}

// BEKeys 背景去除命令翻译键
type BEKeys struct {
	Processing string
	Success    string
	Failed     string
	NeedPhoto  string
}

// PaymentKeys 支付翻译键
type PaymentKeys struct {
	Errors         PaymentErrorKeys
	OrderCreated   string
	PaymentSuccess string
}

type PaymentErrorKeys struct {
	AmountTooSmall    string
	AmountTooLarge    string
	CreateOrderFailed string
	InvalidAmount     string
}

// Keys 全局翻译键实例
var Keys = struct {
	Common       CommonTranslationKeys
	Start        StartTranslationKeys
	Keyboard     KeyboardTranslationKeys
	Instructions InstructionsTranslationKeys
	Language     LanguageTranslationKeys
	Invite       InviteTranslationKeys
	Group        GroupTranslationKeys
	Vip          VipTranslationKeys
	Channel      ChannelTranslationKeys
	ChatAds      ChatAdsTranslationKeys
	Gpi          GpiKeys
	Info         InfoKeys
	Sk           SkKeys
	SignIn       SignInKeys
	Admin        AdminKeys
	AICommand    AICommandKeys
	Payment      PaymentKeys
}{
	Common: CommonTranslationKeys{
		Errors: ErrorsKeys{
			InvalidCommand:      "errors.invalid_command",
			PermissionDenied:    "errors.permission_denied",
			VipRequired:         "errors.vip_required",
			General:             "errors.general_error",
			CommandFailed:       "errors.command_failed",
			MissingPrompt:       "errors.missing_prompt",
			MissingCode:         "errors.missing_code",
			CallbackNotOwned:    "errors.callback_not_owned",
			InsufficientBalance: "errors.insufficient_balance",
			ServiceUnavailable:  "errors.service_unavailable",
		},
	},
	Start: StartTranslationKeys{
		Welcome: WelcomeKeys{
			Message:        "start.welcome.message",
			MessagePhotoID: "start.welcome.message_photo_id",
		},
	},
	Keyboard: KeyboardTranslationKeys{
		Language:             "keyboard.language",
		Instructions:         "keyboard.instructions",
		ChannelNotifications: "keyboard.channel_notifications",
		GetInviteCode:        "keyboard.get_invite_code",
		GroupManagement:      "keyboard.group_management",
		BackToMenu:           "keyboard.back_to_menu",
	},
	Instructions: InstructionsTranslationKeys{
		Title:   "instructions.title",
		Content: "instructions.content",
		PhotoID: "instructions.photo_id",
	},
	Language: LanguageTranslationKeys{
		Choose:  "language.choose",
		Changed: "language.changed",
		Error:   "language.error",
		Current: "language.current",
	},
	Invite: InviteTranslationKeys{
		Start: InviteStartKeys{
			Link:        "invite.start.link",
			Success:     "invite.start.success",
			Fail:        "invite.start.fail",
			InvitedBy:   "invite.start.invited_by",
			LinkPhotoID: "invite.start.link_photo_id",
		},
		Error: InviteErrorKeys{
			SelfInvite:     "invite.error.self_invite",
			AlreadyInvited: "invite.error.already_invited",
			TimeLimit:      "invite.error.time_limit",
		},
	},
	Group: GroupTranslationKeys{
		Management: GroupManagementKeys{
			Tutorial:      "group.management.tutorial",
			AddBot:        "group.management.add_bot",
			PrevPage:      "group.management.prev_page",
			NextPage:      "group.management.next_page",
			Refresh:       "group.management.refresh",
			Help:          "group.management.help",
			GroupInfo:     "group.management.group_info",
			DeleteBinding: "group.management.delete_binding",
			BackToList:    "group.management.back_to_list",
			UnbindSuccess: "group.management.unbind_success",
			UnbindFailed:  "group.management.unbind_failed",
			GroupNotFound: "group.management.group_not_found",
		},
		Help: GroupHelpKeys{
			Title:   "group.help.title",
			Content: "group.help.content",
			Back:    "group.help.back",
		},
		Binding: GroupBindingKeys{
			Success:     "group.binding.success",
			Failed:      "group.binding.failed",
			SuccessUser: "group.binding.success_user",
		},
		Setting: GroupSettingKeys{},
	},
	Vip: VipTranslationKeys{
		Status: VipStatusKeys{
			Active:   "vip.status.active",
			Inactive: "vip.status.inactive",
			Expired:  "vip.status.expired",
		},
		ExpiresAt: "vip.expires_at",
	},
	Channel: ChannelTranslationKeys{
		Notification:        "channel.notification",
		NotificationPhotoID: "channel.notification_photo_id",
	},
	ChatAds: ChatAdsTranslationKeys{
		Commands: ChatAdsCommandsKeys{
			RemoveChatAds:         "chat_ads.commands.remove_chat_ads",
			GenerateRemoveAdsCode: "chat_ads.commands.generate_remove_ads_code",
		},
		InvalidParams:   "chat_ads.invalid_params",
		InvalidCode:     "chat_ads.invalid_code",
		RemoveSuccess:   "chat_ads.remove_success",
		RemoveFailed:    "chat_ads.remove_failed",
		GenerateSuccess: "chat_ads.generate_success",
		Error:           "chat_ads.error",
	},
	Gpi: GpiKeys{
		Instruction: "gpi.instruction",
		Result:      "gpi.result",
		NoPhoto:     "gpi.no_photo",
	},
	Info: InfoKeys{
		Title:            "info.title",
		UserID:           "info.user_id",
		Username:         "info.username",
		Language:         "info.language",
		VipStatus:        "info.vip_status",
		RemainingPower:   "info.remaining_power",
		VipStatusNormal:  "info.vip_status_normal",
		VipStatusVip:     "info.vip_status_vip",
		VipStatusExpired: "info.vip_status_expired",
	},
	Sk: SkKeys{
		Verifying:   "sk.verifying",
		Success:     "sk.success",
		Invalid:     "sk.invalid",
		AlreadyUsed: "sk.already_used",
	},
	SignIn: SignInKeys{
		Success:         "sign_in.success",
		AlreadySignedIn: "sign_in.already_signed_in",
		CannotSignIn:    "sign_in.cannot_sign_in",
		CheckStatus:     "sign_in.check_status",
	},
	Admin: AdminKeys{
		ModerateImage: AdminModerateImageKeys{
			Usage: "admin.moderate_image.usage",
		},
	},
	AICommand: AICommandKeys{
		Draw: DrawKeys{
			Processing: "ai_command.draw.processing",
			Success:    "ai_command.draw.success",
			Failed:     "ai_command.draw.failed",
			NoPrompt:   "ai_command.draw.no_prompt",
		},
		FC: FCKeys{
			Processing:   "ai_command.fc.processing",
			Success:      "ai_command.fc.success",
			Failed:       "ai_command.fc.failed",
			NeedTwoPhoto: "ai_command.fc.need_two_photo",
		},
		BR: BRKeys{
			Processing:     "ai_command.br.processing",
			Success:        "ai_command.br.success",
			Failed:         "ai_command.br.failed",
			NeedTwoPhoto:   "ai_command.br.need_two_photo",
			SameSizeNeeded: "ai_command.br.same_size_needed",
		},
		BE: BEKeys{
			Processing: "ai_command.be.processing",
			Success:    "ai_command.be.success",
			Failed:     "ai_command.be.failed",
			NeedPhoto:  "ai_command.be.need_photo",
		},
		Moderation: ModerationKeys{
			Failed:     "ai_command.moderation.failed",
			Rejected:   "ai_command.moderation.rejected",
			Passed:     "ai_command.moderation.passed",
			Processing: "ai_command.moderation.processing",
		},
	},
	Payment: PaymentKeys{
		Errors: PaymentErrorKeys{
			AmountTooSmall:    "payment.errors.amount_too_small",
			AmountTooLarge:    "payment.errors.amount_too_large",
			CreateOrderFailed: "payment.errors.create_order_failed",
			InvalidAmount:     "payment.errors.invalid_amount",
		},
		OrderCreated:   "payment.order_created",
		PaymentSuccess: "payment.payment_success",
	},
}
