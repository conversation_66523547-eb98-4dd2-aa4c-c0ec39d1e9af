package i18n

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// Manager 国际化管理器
type Manager struct {
	translations   map[string]map[string]interface{}
	defaultLang    string
	supportedLangs []string
}

// NewManager 创建新的国际化管理器
func NewManager() *Manager {
	m := &Manager{
		translations:   make(map[string]map[string]interface{}),
		defaultLang:    "en",
		supportedLangs: []string{"en", "zh_CN", "ja", "de", "es", "fr", "ko", "ru"},
	}
	m.loadTranslations()
	return m
}

// loadTranslations 加载所有翻译文件
func (m *Manager) loadTranslations() {
	// 尝试多个可能的路径
	possiblePaths := []string{
		"data/i18n_configs/locales",       // 从项目根目录运行
		"../data/i18n_configs/locales",    // 从子目录运行
		"../../data/i18n_configs/locales", // 从更深的子目录运行
		"./data/i18n_configs/locales",     // 明确的当前目录
	}

	var localesDir string
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			localesDir = path
			log.Printf("Found locales directory: %s", localesDir)
			break
		}
	}

	if localesDir == "" {
		log.Printf("Warning: No locales directory found in any of these paths: %v", possiblePaths)
		return
	}

	for _, lang := range m.supportedLangs {
		langFile := filepath.Join(localesDir, lang+".json")
		if _, err := os.Stat(langFile); err == nil {
			if err := m.loadLangFile(lang, langFile); err != nil {
				log.Printf("Error loading translation file %s: %v", langFile, err)
			} else {
				log.Printf("Loaded translation file: %s", langFile)
			}
		} else {
			log.Printf("Translation file does not exist: %s", langFile)
		}
	}
}

// loadLangFile 加载单个语言文件
func (m *Manager) loadLangFile(lang, filepath string) error {
	data, err := os.ReadFile(filepath)
	if err != nil {
		return err
	}

	var translations map[string]interface{}
	if err := json.Unmarshal(data, &translations); err != nil {
		return err
	}

	m.translations[lang] = translations
	return nil
}

// GetText 获取指定语言的翻译文本
func (m *Manager) GetText(key string, lang ...string) string {
	// 确定使用的语言
	targetLang := m.defaultLang
	if len(lang) > 0 && lang[0] != "" {
		targetLang = lang[0]
	}

	// 如果语言不存在，使用默认语言
	if _, exists := m.translations[targetLang]; !exists {
		targetLang = m.defaultLang
	}

	// 从翻译中获取文本
	value := m.getNestedValue(m.translations[targetLang], key)
	if value == "" {
		log.Printf("Translation not found for key: %s, lang: %s", key, targetLang)
		return key // 返回键作为后备
	}

	return value
}

// GetTextWithArgs 获取带参数的翻译文本
func (m *Manager) GetTextWithArgs(key string, args map[string]interface{}, lang ...string) string {
	text := m.GetText(key, lang...)

	// 替换参数
	for k, v := range args {
		placeholder := fmt.Sprintf("{%s}", k)
		text = strings.ReplaceAll(text, placeholder, fmt.Sprintf("%v", v))
	}

	return text
}

// GetI18nCommands 从i18n文件中获取所有语言版本的命令
func (m *Manager) GetI18nCommands(commandKey string) []string {
	commands := make([]string, 0)
	commandMap := make(map[string]bool) // 用于去重

	// 获取主命令名称
	for lang := range m.translations {
		// 尝试获取主命令
		name := m.GetText(commandKey, lang)
		if name != "" && name != commandKey && !commandMap[name] {
			commands = append(commands, name)
			commandMap[name] = true
		}

		// 获取别名列表
		aliasesKey := strings.Replace(commandKey, ".name", ".aliases", 1)
		aliases := m.getNestedValue(m.translations[lang], aliasesKey)

		if aliases != "" {
			// 如果是字符串，直接添加
			if !commandMap[aliases] {
				commands = append(commands, aliases)
				commandMap[aliases] = true
			}
		}
	}

	// 如果没有找到任何命令，使用键的最后一部分作为默认命令
	if len(commands) == 0 {
		parts := strings.Split(commandKey, ".")
		if len(parts) >= 2 {
			defaultCommand := parts[len(parts)-2] // 使用倒数第二个部分作为命令名
			commands = append(commands, defaultCommand)
		}
	}

	return commands
}

// getNestedValue 从嵌套的map中获取值
func (m *Manager) getNestedValue(data map[string]interface{}, key string) string {
	keys := strings.Split(key, ".")
	current := data

	for i, k := range keys {
		if i == len(keys)-1 {
			// 最后一个键，获取值
			if value, ok := current[k]; ok {
				if str, ok := value.(string); ok {
					return str
				}
			}
			return ""
		}

		// 中间键，继续向下查找
		if next, ok := current[k].(map[string]interface{}); ok {
			current = next
		} else {
			return ""
		}
	}

	return ""
}

// GetSupportedLanguages 获取支持的语言列表
func (m *Manager) GetSupportedLanguages() []string {
	return append([]string{}, m.supportedLangs...)
}

// GetDefaultLanguage 获取默认语言
func (m *Manager) GetDefaultLanguage() string {
	return m.defaultLang
}

// ReloadTranslations 重新加载所有翻译文件
func (m *Manager) ReloadTranslations() {
	m.translations = make(map[string]map[string]interface{})
	m.loadTranslations()
}

// 全局单例
var DefaultManager = NewManager()
