package request

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"telegram-chatbot-go/internal/config"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	green20220302 "github.com/alibabacloud-go/green-20220302/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
)

// ModerationClient 图片审核服务
type ModerationClient struct {
	client *green20220302.Client
	config config.Config
}

// ModerationResult 审核结果
type ModerationResult struct {
	DataID    string                 `json:"dataId"`
	Results   []ModerationResultItem `json:"results"`
	Passed    bool                   `json:"passed"`
	RequestID string                 `json:"requestId"`
}

// ModerationResultItem 审核结果项
type ModerationResultItem struct {
	Label      string  `json:"label"`
	Confidence float64 `json:"confidence"`
}

// ServiceParameters 服务参数
type ServiceParameters struct {
	ImageURL string `json:"imageUrl,omitempty"`
	DataID   string `json:"dataId"`
}

// NewModerationClient 创建图片审核服务
func NewModerationClient(cfg config.Config) (*ModerationClient, error) {
	client, err := createClient(cfg.AlibabaCloud.AccessKeyID, cfg.AlibabaCloud.AccessKeySecret, cfg.AlibabaCloud.Green.Endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to create moderation client: %w", err)
	}

	return &ModerationClient{
		client: client,
		config: cfg,
	}, nil
}

// createClient 创建阿里云内容审核客户端
func createClient(accessKeyID, accessKeySecret, endpoint string) (*green20220302.Client, error) {
	config := &openapi.Config{
		AccessKeyId:     &accessKeyID,
		AccessKeySecret: &accessKeySecret,
		Endpoint:        &endpoint,
	}
	return green20220302.NewClient(config)
}

// ModerateImageURL 审核可直接访问的图片URL
func (s *ModerationClient) ModerateImageURL(ctx context.Context, imageURL string) (*ModerationResult, error) {
	dataID := uuid.New().String()

	serviceParams := ServiceParameters{
		ImageURL: imageURL,
		DataID:   dataID,
	}

	serviceParamsJSON, err := json.Marshal(serviceParams)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal service parameters: %w", err)
	}

	serviceParamsStr := string(serviceParamsJSON)
	request := &green20220302.ImageModerationRequest{
		Service:           &s.config.AlibabaCloud.Green.Service,
		ServiceParameters: &serviceParamsStr,
	}

	runtime := &util.RuntimeOptions{}

	response, err := s.client.ImageModerationWithOptions(request, runtime)
	if err != nil {
		return nil, fmt.Errorf("image moderation failed: %w", err)
	}

	return s.parseResponse(response, dataID)
}

// ModerateImageData 审核图片数据（用于需要下载的图片URL）
func (s *ModerationClient) ModerateImageData(ctx context.Context, imageURL string) (*ModerationResult, error) {
	// 下载图片数据
	imageData, err := s.downloadImage(ctx, imageURL)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}

	// 获取上传token
	uploadToken, err := s.getUploadToken()
	if err != nil {
		return nil, fmt.Errorf("failed to get upload token: %w", err)
	}

	// 上传到OSS
	objectName, err := s.uploadToOSS(imageData, uploadToken)
	if err != nil {
		return nil, fmt.Errorf("failed to upload to OSS: %w", err)
	}

	// 构建审核参数
	dataID := uuid.New().String()

	// 安全检查上传token数据
	if uploadToken.Body == nil || uploadToken.Body.Data == nil || uploadToken.Body.Data.BucketName == nil {
		return nil, fmt.Errorf("invalid upload token response")
	}

	serviceParams := map[string]interface{}{
		"ossBucketName": *uploadToken.Body.Data.BucketName,
		"ossObjectName": objectName,
		"dataId":        dataID,
	}

	serviceParamsJSON, err := json.Marshal(serviceParams)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal service parameters: %w", err)
	}

	serviceParamsStr := string(serviceParamsJSON)
	request := &green20220302.ImageModerationRequest{
		Service:           &s.config.AlibabaCloud.Green.Service,
		ServiceParameters: &serviceParamsStr,
	}

	runtime := &util.RuntimeOptions{}

	response, err := s.client.ImageModerationWithOptions(request, runtime)
	if err != nil {
		return nil, fmt.Errorf("image moderation failed: %w", err)
	}

	return s.parseResponse(response, dataID)
}

// downloadImage 下载图片数据
func (s *ModerationClient) downloadImage(ctx context.Context, imageURL string) ([]byte, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", imageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置User-Agent，避免某些服务器拒绝请求
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; TelegramBot/1.0)")

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to download image: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download image: status %d, url: %s", resp.StatusCode, imageURL)
	}

	// 限制文件大小（10MB）
	const maxSize = 10 * 1024 * 1024
	limitedReader := io.LimitReader(resp.Body, maxSize)

	data, err := io.ReadAll(limitedReader)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	if len(data) == 0 {
		return nil, fmt.Errorf("empty image data")
	}

	return data, nil
}

// getUploadToken 获取上传token
func (s *ModerationClient) getUploadToken() (*green20220302.DescribeUploadTokenResponse, error) {
	return s.client.DescribeUploadToken()
}

// uploadToOSS 上传数据到OSS
func (s *ModerationClient) uploadToOSS(data []byte, uploadToken *green20220302.DescribeUploadTokenResponse) (string, error) {
	// 安全检查上传token数据
	if uploadToken.Body == nil || uploadToken.Body.Data == nil || uploadToken.Body.Data.FileNamePrefix == nil {
		return "", fmt.Errorf("invalid upload token response")
	}

	tokenData := uploadToken.Body.Data

	// 检查必要的字段
	if tokenData.AccessKeyId == nil || tokenData.AccessKeySecret == nil ||
		tokenData.SecurityToken == nil || tokenData.BucketName == nil ||
		tokenData.OssInternalEndPoint == nil {
		return "", fmt.Errorf("missing required fields in upload token")
	}

	// 生成对象名
	objectName := fmt.Sprintf("%s%s.jpg", *tokenData.FileNamePrefix, uuid.New().String())

	// 选择合适的endpoint
	endpoint := *tokenData.OssInternalEndPoint
	// 如果是内网endpoint，转换为公网endpoint
	if strings.Contains(endpoint, "-internal.") {
		endpoint = strings.Replace(endpoint, "-internal.", ".", 1)
	}

	// 创建OSS客户端，添加重试和超时配置
	client, err := oss.New(endpoint, *tokenData.AccessKeyId, *tokenData.AccessKeySecret,
		oss.SecurityToken(*tokenData.SecurityToken),
		oss.Timeout(10, 60), // 连接超时10秒，读写超时60秒
	)
	if err != nil {
		return "", fmt.Errorf("failed to create OSS client: %w", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(*tokenData.BucketName)
	if err != nil {
		return "", fmt.Errorf("failed to get OSS bucket: %w", err)
	}

	// 上传数据，添加重试机制
	var uploadErr error
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		reader := bytes.NewReader(data)
		uploadErr = bucket.PutObject(objectName, reader, oss.ContentType("image/jpeg"))
		if uploadErr == nil {
			break
		}

		// 如果不是最后一次重试，等待一段时间后重试
		if i < maxRetries-1 {
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	if uploadErr != nil {
		return "", fmt.Errorf("failed to upload to OSS after %d retries: %w", maxRetries, uploadErr)
	}

	return objectName, nil
}

// parseResponse 解析响应
func (s *ModerationClient) parseResponse(response *green20220302.ImageModerationResponse, dataID string) (*ModerationResult, error) {
	// 添加调试信息
	if response == nil {
		return nil, fmt.Errorf("response is nil")
	}

	if response.StatusCode == nil || *response.StatusCode != 200 {
		return nil, fmt.Errorf("API request failed with status: %v", response.StatusCode)
	}

	if response.Body == nil {
		return nil, fmt.Errorf("empty response body")
	}

	body := response.Body
	if body.Code == nil || *body.Code != 200 {
		return nil, fmt.Errorf("moderation failed with code: %v, message: %v", body.Code, body.Msg)
	}

	requestID := ""
	if body.RequestId != nil {
		requestID = *body.RequestId
	}

	result := &ModerationResult{
		DataID:    dataID,
		RequestID: requestID,
		Results:   make([]ModerationResultItem, 0),
		Passed:    true, // 默认通过，如果有违规内容则设为false
	}

	if body.Data != nil && body.Data.Result != nil {
		for _, item := range body.Data.Result {
			// 安全检查指针
			if item.Label == nil || item.Confidence == nil {
				continue // 跳过无效的结果项
			}

			label := *item.Label
			confidence := float64(*item.Confidence)

			resultItem := ModerationResultItem{
				Label:      label,
				Confidence: confidence,
			}
			result.Results = append(result.Results, resultItem)

			// 如果置信度超过阈值，则认为不通过
			// 这里使用80作为阈值，您可以根据需要调整
			if confidence > 80.0 && s.isViolationLabel(label) {
				result.Passed = false
			}
		}
	}

	return result, nil
}

// isViolationLabel 判断是否为违规标签
func (s *ModerationClient) isViolationLabel(label string) bool {
	violationLabels := []string{
		"pornographic",
		"sexy",
		"violence",
		"terrorism",
		"bloody",
		"drug",
		"gambling",
		"political",
		"contraband",
	}

	for _, vLabel := range violationLabels {
		if strings.Contains(strings.ToLower(label), vLabel) {
			return true
		}
	}
	return false
}

// ModerateImageSmart 智能选择审核方式
func (s *ModerationClient) ModerateImageSmart(ctx context.Context, imageURL string) (*ModerationResult, error) {
	// 基本验证
	if imageURL == "" {
		return nil, fmt.Errorf("image URL cannot be empty")
	}

	if s.client == nil {
		return nil, fmt.Errorf("moderation client is not initialized")
	}

	// 如果是http/https开头的URL，智能选择审核方式
	if strings.HasPrefix(strings.ToLower(imageURL), "http://") || strings.HasPrefix(strings.ToLower(imageURL), "https://") {
		// 先尝试直接URL审核（更快）
		result, err := s.ModerateImageURL(ctx, imageURL)
		if err != nil {
			// 如果直接URL审核失败，尝试下载后上传到OSS审核
			return s.ModerateImageData(ctx, imageURL)
		}
		return result, nil
	}

	// 对于非HTTP URL，直接使用URL审核
	return s.ModerateImageURL(ctx, imageURL)

}
