package request

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"telegram-chatbot-go/internal/config"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// EPUSDTClient EPUSDT API客户端
type EPUSDTClient struct {
	BaseURL     string
	APIToken    string
	NotifyURL   string
	RedirectURL string
	client      *http.Client
}

// NewEPUSDTClient 创建EPUSDT客户端
func NewEPUSDTClient(config *config.Config) *EPUSDTClient {
	return &EPUSDTClient{
		BaseURL:     config.EPUSDT.BaseURL,
		APIToken:    config.EPUSDT.APIToken,
		NotifyURL:   config.EPUSDT.NotifyURL,
		RedirectURL: config.EPUSDT.RedirectURL,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	OrderID     string  `json:"order_id"`
	Amount      float64 `json:"amount"`
	NotifyURL   string  `json:"notify_url"`
	RedirectURL string  `json:"redirect_url,omitempty"`
	Signature   string  `json:"signature"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
	Data       struct {
		TradeID        string  `json:"trade_id"`
		OrderID        string  `json:"order_id"`
		Amount         float64 `json:"amount"`
		ActualAmount   float64 `json:"actual_amount"`
		Token          string  `json:"token"`
		ExpirationTime int64   `json:"expiration_time"`
		PaymentURL     string  `json:"payment_url"`
	} `json:"data"`
	RequestID string `json:"request_id"`
}

// PaymentCallback 支付回调数据
type PaymentCallback struct {
	TradeID            string  `json:"trade_id"`
	OrderID            string  `json:"order_id"`
	Amount             float64 `json:"amount"`
	ActualAmount       float64 `json:"actual_amount"`
	Token              string  `json:"token"`
	BlockTransactionID string  `json:"block_transaction_id"`
	Signature          string  `json:"signature"`
	Status             int     `json:"status"`
}

// generateSignature 生成MD5签名
func (c *EPUSDTClient) generateSignature(params map[string]interface{}) string {
	// 1. 过滤空值并转换为字符串
	filteredParams := make(map[string]string)
	for key, value := range params {
		if value != nil && value != "" {
			switch v := value.(type) {
			case string:
				if v != "" {
					filteredParams[key] = v
				}
			case float64:
				filteredParams[key] = strconv.FormatFloat(v, 'f', -1, 64)
			case int:
				filteredParams[key] = strconv.Itoa(v)
			case int64:
				filteredParams[key] = strconv.FormatInt(v, 10)
			default:
				filteredParams[key] = fmt.Sprintf("%v", v)
			}
		}
	}

	// 2. 按key排序
	keys := make([]string, 0, len(filteredParams))
	for key := range filteredParams {
		if key != "signature" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 3. 拼接参数
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", key, filteredParams[key]))
	}
	queryString := strings.Join(parts, "&")

	// 4. 追加API Token并计算MD5
	signString := queryString + c.APIToken
	hash := md5.Sum([]byte(signString))
	return fmt.Sprintf("%x", hash)
}

// CreateOrder 创建支付订单
func (c *EPUSDTClient) CreateOrder(orderID string, amount float64) (*CreateOrderResponse, error) {
	// 构建请求参数
	params := map[string]interface{}{
		"order_id":     orderID,
		"amount":       amount,
		"notify_url":   c.NotifyURL,
		"redirect_url": c.RedirectURL,
	}

	// 生成签名
	signature := c.generateSignature(params)

	// 构建请求体
	request := CreateOrderRequest{
		OrderID:     orderID,
		Amount:      amount,
		NotifyURL:   c.NotifyURL,
		RedirectURL: c.RedirectURL,
		Signature:   signature,
	}

	// 序列化请求
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// 发送HTTP请求
	url := c.BaseURL + "/api/v1/order/create-transaction"
	logx.Infof("🔗 发送请求到: %s", url)
	logx.Infof("📝 请求数据: %s", string(jsonData))

	resp, err := c.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %v", err)
	}

	logx.Infof("📥 响应状态: %d", resp.StatusCode)
	logx.Infof("📥 响应内容: %s", string(body))

	// 解析响应
	var response CreateOrderResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查响应状态
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("API error: %s (code: %d)", response.Message, response.StatusCode)
	}

	return &response, nil
}

// VerifyCallback 验证支付回调签名
func (c *EPUSDTClient) VerifyCallback(callback *PaymentCallback) bool {
	params := map[string]interface{}{
		"trade_id":             callback.TradeID,
		"order_id":             callback.OrderID,
		"amount":               callback.Amount,
		"actual_amount":        callback.ActualAmount,
		"token":                callback.Token,
		"block_transaction_id": callback.BlockTransactionID,
		"status":               callback.Status,
	}

	expectedSignature := c.generateSignature(params)
	return expectedSignature == callback.Signature
}
