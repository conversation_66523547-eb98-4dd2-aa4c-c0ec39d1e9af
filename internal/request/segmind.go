package request

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"telegram-chatbot-go/internal/config"
	"time"
)

// SegmindClient Segmind API客户端
type SegmindClient struct {
	BaseURL    string
	APIKey     string
	ProxyAuth  string
	BotToken   string
	HTTPClient *http.Client
}

// NewSegmindClient 创建新的Segmind客户端
func NewSegmindClient(cfg *config.Config, botToken string) *SegmindClient {
	return &SegmindClient{
		BaseURL:   cfg.AI.Segmind.BaseURL,
		APIKey:    cfg.AI.Segmind.APIKey,
		ProxyAuth: cfg.AI.Segmind.ProxyAuth,
		BotToken:  botToken,
		HTTPClient: &http.Client{
			Timeout: 600 * time.Second,
		},
	}
}

// PlaygroundV25Request Playground V2.5请求参数
type PlaygroundV25Request struct {
	Prompt            string  `json:"prompt"`
	NegativePrompt    string  `json:"negative_prompt"`
	Samples           int     `json:"samples"`
	NumInferenceSteps int     `json:"num_inference_steps"`
	GuidanceScale     float64 `json:"guidance_scale"`
	Seed              int     `json:"seed"`
	Base64            bool    `json:"base64"`
}

// FaceSwapRequest 换脸请求参数
type FaceSwapRequest struct {
	SourceImg        string `json:"source_img"`
	TargetImg        string `json:"target_img"`
	InputFacesIndex  int    `json:"input_faces_index"`
	SourceFacesIndex int    `json:"source_faces_index"`
	FaceRestore      string `json:"face_restore"`
	Base64           bool   `json:"base64"`
}

// BackgroundEraserRequest 去除背景请求参数
type BackgroundEraserRequest struct {
	Image        string `json:"image"`
	OutputFormat string `json:"output_format"`
	Base64       bool   `json:"base64"`
}

// BackgroundReplaceRequest 替换背景请求参数
type BackgroundReplaceRequest struct {
	Image             string  `json:"image"`
	RefImage          string  `json:"ref_image"`
	Prompt            string  `json:"prompt"`
	NegativePrompt    string  `json:"negative_prompt"`
	Samples           int     `json:"samples"`
	Scheduler         string  `json:"scheduler"`
	NumInferenceSteps int     `json:"num_inference_steps"`
	GuidanceScale     float64 `json:"guidance_scale"`
	Seed              int     `json:"seed"`
	Strength          float64 `json:"strength"`
	CNWeight          float64 `json:"cn_weight"`
	IPAdapterWeight   float64 `json:"ip_adapter_weight"`
	Base64            bool    `json:"base64"`
}

// SegmindResponse Segmind API响应
type SegmindResponse struct {
	Image string `json:"image"`
	Error string `json:"error,omitempty"`
}

// downloadAndConvertToBase64 下载图片并转换为base64
func (c *SegmindClient) downloadAndConvertToBase64(ctx context.Context, imageURL string) (string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", imageURL, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("下载图片失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取图片数据失败: %v", err)
	}

	return base64.StdEncoding.EncodeToString(imageData), nil
}

// makeRequest 发送请求到Segmind API
func (c *SegmindClient) makeRequest(ctx context.Context, endpoint string, payload interface{}) (*SegmindResponse, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	url := c.BaseURL + endpoint
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept-Encoding", "gzip")
	if c.ProxyAuth != "" {
		req.Header.Set("x-proxy-auth", c.ProxyAuth)
	}
	if c.APIKey != "" {
		req.Header.Set("x-api-key", c.APIKey)
	}

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var result SegmindResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return &result, nil
}

// GenerateImage 使用Playground V2.5生成图片
func (c *SegmindClient) GenerateImage(ctx context.Context, req *PlaygroundV25Request) (*SegmindResponse, error) {
	return c.makeRequest(ctx, "/v1/playground-v2.5", req)
}

// FaceSwap 换脸
func (c *SegmindClient) FaceSwap(ctx context.Context, req *FaceSwapRequest) (*SegmindResponse, error) {
	// 转换图片URL为base64
	if req.SourceImg != "" {
		base64Img, err := c.downloadAndConvertToBase64(ctx, req.SourceImg)
		if err != nil {
			return nil, fmt.Errorf("转换源图片失败: %v", err)
		}
		req.SourceImg = base64Img
	}

	if req.TargetImg != "" {
		base64Img, err := c.downloadAndConvertToBase64(ctx, req.TargetImg)
		if err != nil {
			return nil, fmt.Errorf("转换目标图片失败: %v", err)
		}
		req.TargetImg = base64Img
	}

	return c.makeRequest(ctx, "/v1/faceswap-v2", req)
}

// RemoveBackground 去除背景
func (c *SegmindClient) RemoveBackground(ctx context.Context, req *BackgroundEraserRequest) (*SegmindResponse, error) {
	// 转换图片URL为base64
	if req.Image != "" {
		base64Img, err := c.downloadAndConvertToBase64(ctx, req.Image)
		if err != nil {
			return nil, fmt.Errorf("转换图片失败: %v", err)
		}
		req.Image = base64Img
	}

	return c.makeRequest(ctx, "/v1/background-eraser", req)
}

// ReplaceBackground 替换背景
func (c *SegmindClient) ReplaceBackground(ctx context.Context, req *BackgroundReplaceRequest) (*SegmindResponse, error) {
	// 转换图片URL为base64
	if req.Image != "" {
		base64Img, err := c.downloadAndConvertToBase64(ctx, req.Image)
		if err != nil {
			return nil, fmt.Errorf("转换主体图片失败: %v", err)
		}
		req.Image = base64Img
	}

	if req.RefImage != "" {
		base64Img, err := c.downloadAndConvertToBase64(ctx, req.RefImage)
		if err != nil {
			return nil, fmt.Errorf("转换背景图片失败: %v", err)
		}
		req.RefImage = base64Img
	}

	return c.makeRequest(ctx, "/v1/bg-replace", req)
}
