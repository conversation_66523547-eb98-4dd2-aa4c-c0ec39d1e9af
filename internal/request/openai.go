package request

import (
	"context"
	"fmt"
	"strings"
	"telegram-chatbot-go/internal/config"

	openai "github.com/sashabaranov/go-openai"
	"github.com/zeromicro/go-zero/core/logx"
)

// OpenAIRequest OpenAI请求结构
type OpenAIRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   bool      `json:"stream"`
}

// Message 消息结构
type Message struct {
	Role    string      `json:"role"`
	Content interface{} `json:"content"` // 可以是字符串或ContentItem数组
}

// ContentItem 内容项结构，可以是文本或图片URL
type ContentItem struct {
	Type     string    `json:"type"`
	Text     string    `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
}

// ImageURL 图片URL结构
type ImageURL struct {
	URL string `json:"url"`
}

// OpenAIResponse OpenAI响应结构
type OpenAIResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择结构
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}

// Usage 使用量结构
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenAIClient OpenAI客户端
type OpenAIClient struct {
	client *openai.Client
	config *config.Config
}

// NewOpenAIClient 创建新的OpenAI客户端
func NewOpenAIClient(cfg *config.Config) *OpenAIClient {
	config := openai.DefaultConfig(cfg.AI.OpenAI.APIKey)
	if cfg.AI.OpenAI.BaseURL != "" {
		config.BaseURL = cfg.AI.OpenAI.BaseURL
	}

	client := openai.NewClientWithConfig(config)
	return &OpenAIClient{
		client: client,
		config: cfg,
	}
}

// ChatWithContext 发送带上下文的聊天请求到OpenAI
func (c *OpenAIClient) ChatWithContext(ctx context.Context, messages []openai.ChatCompletionMessage, model string, historyLimit int, temperature float32) (string, error) {
	for _, msg := range messages {
		if len(msg.MultiContent) > 0 {
			// 多媒体消息
			for _, part := range msg.MultiContent {
				if part.Type == openai.ChatMessagePartTypeText {
				} else if part.Type == openai.ChatMessagePartTypeImageURL {
					if part.ImageURL != nil {
						// 对于base64图片，显示数据长度
						url := part.ImageURL.URL
						if strings.HasPrefix(url, "data:image/") {

						} else {
							// 普通URL，只显示前50个字符
							if len(url) > 50 {
								url = url[:50] + "..."
							}
						}
					}
				}
			}
		} else {
			// 纯文本消息
		}
	}

	if len(messages) > historyLimit {
		messages = messages[:historyLimit]
	}

	req := openai.ChatCompletionRequest{
		Model:       model,
		Messages:    messages,
		Temperature: temperature,
	}

	resp, err := c.client.CreateChatCompletion(ctx, req)

	if err != nil {
		logx.Errorf("OpenAI API 错误: %v", err)
		return "", fmt.Errorf("OpenAI API error: %w", err)
	}

	response := resp.Choices[0].Message.Content

	return response, nil
}
