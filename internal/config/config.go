package config

import (
	"time"

	"github.com/SpectatorNan/gorm-zero/gormc/config/mysql"
	"github.com/zeromicro/go-zero/core/logx"
)

// Config 应用配置结构
type Config struct {
	// Bot配置
	Bot struct {
		Token          string        `json:",default="`
		Timeout        time.Duration `json:",default=10s"`
		Debug          bool          `json:",default=false"`
		ContextTimeout time.Duration `json:",default=5m"`
	}

	Log logx.LogConf

	// AI服务配置
	AI struct {
		OpenAI struct {
			BaseURL      string `json:",default=https://api.openai.com/v1"`
			APIKey       string `json:",default="`
			HistoryLimit int    `json:",default=10"`
			Model        string `json:",default=gpt-4o-mini"`
		}
		Segmind struct {
			BaseURL   string `json:",default="`
			APIKey    string `json:",default="`
			ProxyAuth string `json:",default="`
		}
	}

	// 阿里云内容审核配置
	AlibabaCloud struct {
		AccessKeyID     string `json:",default="`
		AccessKeySecret string `json:",default="`
		Green           struct {
			Endpoint string `json:",default=green-cip.ap-southeast-1.aliyuncs.com"`
			Service  string `json:",default=baselineCheck_cb"`
		}
	}

	Mysql mysql.Mysql

	Operation struct {
		EnableEavesdropMode bool  `json:",default=false"`
		DailyPoints         int32 `json:",default=30"`
		PermanentPoints     int32 `json:",default=30"`

		// 邀请奖励配置
		InviteReward struct {
			// 邀请人奖励
			InviterVipDays         int32 `json:",default=1"`   // 邀请人获得VIP天数
			InviterVipPoints       int32 `json:",default=200"` // 邀请人获得VIP积分
			InviterDailyPoints     int32 `json:",default=0"`   // 邀请人获得每日积分
			InviterPermanentPoints int32 `json:",default=0"`   // 邀请人获得永久积分

			// 被邀请人奖励
			InviteeVipDays         int32 `json:",default=0"`   // 被邀请人获得VIP天数
			InviteeVipPoints       int32 `json:",default=0"`   // 被邀请人获得VIP积分
			InviteeDailyPoints     int32 `json:",default=0"`   // 被邀请人获得每日积分
			InviteePermanentPoints int32 `json:",default=100"` // 被邀请人获得永久积分
		}

		// 广告配置
		MessageCaption struct {
			// 默认广告文本配置
			AdTextMarkdowns AdTextConfig `json:"ad_text_markdowns,optional"`

			// 群组和私聊的覆盖配置
			OverrideGroupTextMarkdowns   AdTextConfig `json:"override_group_text_markdowns,optional"`
			OverridePrivateTextMarkdowns AdTextConfig `json:"override_private_text_markdowns,optional"`

			// 按钮配置
			Buttons ButtonsConfig `json:"buttons,optional"`

			// 群组和私聊的按钮覆盖配置
			OverrideGroupButtons   ButtonsConfig `json:"override_group_buttons,optional"`
			OverridePrivateButtons ButtonsConfig `json:"override_private_buttons,optional"`
		}
	}

	Admin struct {
		UserIDs []int64 `json:",optional"`
	}

	// HTTP服务配置
	HTTP struct {
		Port    int  `json:",default=8080"`
		Enabled bool `json:",default=true"`
	}

	// EPUSDT支付配置
	EPUSDT struct {
		BaseURL       string `json:",default=http://localhost:8001"`
		APIToken      string `json:",default="`
		NotifyURL     string `json:",default="`
		RedirectURL   string `json:",default="`
		Enabled       bool   `json:",default=false"`
		PaymentConfig struct {
			MinAmount       float64  `json:",default=1.0"`
			MaxAmount       float64  `json:",default=10000.0"`
			ExpirationTime  int      `json:",default=1800"`
			PointsPerUSDT   float64  `json:",default=100"`
		}
	}

	// AI命令配置
	AICommands map[string]AICommand `json:",optional"`
}

// AICommand AI命令配置结构
type AICommand struct {
	Enable          bool              `json:"enable,default=true"`
	Type            string            `json:"type,default=chat"`
	AllowPrivate    bool              `json:"allow_private,default=true"`
	AllowGroup      bool              `json:"allow_group,default=true"`
	AllowAutoVision bool              `json:"allow_auto_vision,default=false"`
	Model           *string           `json:"model,optional"`
	VisionModel     *string           `json:"vision_model,optional"`
	HistoryLimit    int               `json:"history_limit,default=10"`
	Cost            int32             `json:"cost,default=0"`
	Description     string            `json:"description,default="`
	HowToUse        string            `json:"how_to_use,default="`
	SystemPrompt    *string           `json:"system_prompt,optional,default="`
	Aliases         []string          `json:"aliases,optional"`
	Moderation      *ModerationConfig `json:"moderation,optional"`
}

// ModerationConfig 内容审核配置
type ModerationConfig struct {
	Group   bool `json:"group,default=false"`
	Private bool `json:"private,default=false"`
}

// ButtonConfig 按钮配置
type ButtonConfig struct {
	Text string `json:"text"`
	URL  string `json:"url"`
}

// ButtonLayout 按钮布局（二维数组）
type ButtonLayout [][]ButtonConfig

// WeightedButtonLayout 带权重的按钮布局
type WeightedButtonLayout struct {
	Layout ButtonLayout `json:"layout"`
	Weight *int         `json:"weight,omitempty"`
}

// WeightedAdText 带权重的广告文本
type WeightedAdText struct {
	Text   string `json:"text"`
	Weight *int   `json:"weight,omitempty"`
}

// ButtonsConfig 按钮配置结构 - 支持权重分配的按钮布局数组
type ButtonsConfig []WeightedButtonLayout

// AdTextConfig 广告文本配置结构 - 支持权重分配的文本数组
type AdTextConfig []WeightedAdText
