package model

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

var _ ChatMessageModel = (*customChatMessageModel)(nil)

type (
	// ChatMessageModel 聊天消息模型接口
	ChatMessageModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *ChatMessage) error
		FindOne(ctx context.Context, id int32) (*ChatMessage, error)
		FindByMessageID(ctx context.Context, chatID int64, messageID int64) (*ChatMessage, error)
		Update(ctx context.Context, tx *gorm.DB, data *ChatMessage) error
		Delete(ctx context.Context, tx *gorm.DB, id int32) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
	}

	// customChatMessageModel 自定义模型实现
	customChatMessageModel struct {
		*defaultChatMessageModel
	}

	// defaultChatMessageModel 默认模型实现
	defaultChatMessageModel struct {
		conn *gorm.DB
	}

	// ChatMessage 聊天消息模型 - 严格对应 chat_messages 表
	ChatMessage struct {
		ID               int32     `json:"id" gorm:"type:int;primaryKey;autoIncrement"`
		ChatID           int64     `json:"chat_id" gorm:"not null;uniqueIndex:chat_id;index:idx_chat_timestamp"`
		MessageID        *int64    `json:"message_id" gorm:"uniqueIndex:chat_id"`
		FromType         string    `json:"from_type" gorm:"type:enum('user','bot');not null"`
		UserID           *int64    `json:"user_id"`
		MessageText      *string   `json:"message_text" gorm:"type:text"`
		PhotoURLList     *string   `json:"photo_url_list" gorm:"type:text"`
		Timestamp        time.Time `json:"timestamp" gorm:"type:datetime;default:CURRENT_TIMESTAMP;index:idx_chat_timestamp"`
		ReplyToMessageID *int64    `json:"reply_to_message_id"`
	}
)

// NewChatMessageModel 创建聊天消息模型 - 参照go-zero模式
func NewChatMessageModel(conn *gorm.DB) ChatMessageModel {
	return &customChatMessageModel{
		defaultChatMessageModel: &defaultChatMessageModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (ChatMessage) TableName() string {
	return "chat_messages"
}

// BeforeCreate GORM钩子，在创建记录前设置默认值
func (cm *ChatMessage) BeforeCreate(tx *gorm.DB) error {
	if cm.Timestamp.IsZero() {
		cm.Timestamp = time.Now()
	}
	return nil
}

// === ChatMessage 结构体方法 ===

// SetPhotoURLs 设置图片URL列表
func (cm *ChatMessage) SetPhotoURLs(urls []string) error {
	if len(urls) == 0 {
		cm.PhotoURLList = nil
		return nil
	}

	// 直接将 URL 数组序列化为 JSON
	jsonData, err := json.Marshal(urls)
	if err != nil {
		return err
	}

	jsonStr := string(jsonData)
	cm.PhotoURLList = &jsonStr
	return nil
}

// GetPhotoURLs 获取图片URL列表
func (cm *ChatMessage) GetPhotoURLs() ([]string, error) {
	if cm.PhotoURLList == nil {
		return []string{}, nil
	}

	// 直接从 JSON 反序列化为字符串数组
	var urls []string
	err := json.Unmarshal([]byte(*cm.PhotoURLList), &urls)
	if err != nil {
		return []string{}, err
	}

	return urls, nil
}

// Trans 事务处理
func (m *defaultChatMessageModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入记录
func (m *defaultChatMessageModel) Insert(ctx context.Context, tx *gorm.DB, data *ChatMessage) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据ID查找记录
func (m *defaultChatMessageModel) FindOne(ctx context.Context, id int32) (*ChatMessage, error) {
	var message ChatMessage
	err := m.conn.WithContext(ctx).First(&message, id).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// FindByMessageID 根据消息ID获取聊天消息
func (m *defaultChatMessageModel) FindByMessageID(ctx context.Context, chatID int64, messageID int64) (*ChatMessage, error) {
	var message ChatMessage
	err := m.conn.WithContext(ctx).
		Where("chat_id = ? AND message_id = ?", chatID, messageID).
		First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

// Update 更新记录
func (m *defaultChatMessageModel) Update(ctx context.Context, tx *gorm.DB, data *ChatMessage) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除记录
func (m *defaultChatMessageModel) Delete(ctx context.Context, tx *gorm.DB, id int32) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&ChatMessage{}, id).Error
}
