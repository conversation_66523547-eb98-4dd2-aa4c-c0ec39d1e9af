package model

import (
	"context"
	"time"

	"gorm.io/gorm"
)

var _ ChatModel = (*customChatModel)(nil)

type (
	// ChatModel 聊天模型接口
	ChatModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *Chat) error
		FindOne(ctx context.Context, id int64) (*Chat, error)
		FindOneByChatID(ctx context.Context, chatID int64) (*Chat, error)
		FindOneByChatIDForUpdate(ctx context.Context, tx *gorm.DB, chatID int64) (*Chat, error)
		Update(ctx context.Context, tx *gorm.DB, data *Chat) error
		Delete(ctx context.Context, tx *gorm.DB, id int64) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
		UpdateChatFields(ctx context.Context, tx *gorm.DB, chatID int64, updates map[string]any) error
		// 群组管理相关方法
		FindOwnerGroups(ctx context.Context, ownerID int64) ([]*Chat, error)
		BindGroupToUser(ctx context.Context, tx *gorm.DB, chatID int64, userID int64) error
		UnbindGroupFromUser(ctx context.Context, tx *gorm.DB, chatID int64) error
	}

	// customChatModel 自定义聊天模型实现
	customChatModel struct {
		*defaultChatModel
	}

	// defaultChatModel 默认聊天模型实现
	defaultChatModel struct {
		conn *gorm.DB
	}

	// Chat 聊天模型 - 严格对应 chats 表
	Chat struct {
		ID        int64     `json:"id" gorm:"type:bigint;primaryKey;autoIncrement"`
		ChatID    int64     `json:"chat_id" gorm:"uniqueIndex:chat_id;not null"`
		ChatType  string    `json:"chat_type" gorm:"type:enum('private','group','supergroup','channel');not null;index:idx_chat_type"`
		Title     *string   `json:"title" gorm:"type:varchar(255)"`
		OwnerID   *int64    `json:"owner_id" gorm:"index:idx_owner_id"`
		Ads       *string   `json:"ads" gorm:"type:text"`
		CreatedAt time.Time `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		UpdatedAt time.Time `json:"updated_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	}
)

// NewChatModel 创建聊天模型 - 参照go-zero模式
func NewChatModel(conn *gorm.DB) ChatModel {
	return &customChatModel{
		defaultChatModel: &defaultChatModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (Chat) TableName() string {
	return "chats"
}

// Trans 事务处理
func (m *defaultChatModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入聊天记录
func (m *defaultChatModel) Insert(ctx context.Context, tx *gorm.DB, data *Chat) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据ID查找聊天记录
func (m *defaultChatModel) FindOne(ctx context.Context, id int64) (*Chat, error) {
	var chat Chat
	err := m.conn.WithContext(ctx).First(&chat, id).Error
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// FindOneByChatID 根据ChatID查找聊天记录
func (m *defaultChatModel) FindOneByChatID(ctx context.Context, chatID int64) (*Chat, error) {
	var chat Chat
	err := m.conn.WithContext(ctx).
		Where("chat_id = ?", chatID).
		First(&chat).Error
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// FindOneByChatIDForUpdate 根据ChatID查找聊天记录并加行锁
func (m *defaultChatModel) FindOneByChatIDForUpdate(ctx context.Context, tx *gorm.DB, chatID int64) (*Chat, error) {
	db := m.conn
	if tx != nil {
		db = tx
	}

	var chat Chat
	err := db.WithContext(ctx).
		Where("chat_id = ?", chatID).
		First(&chat).Error
	if err != nil {
		return nil, err
	}
	return &chat, nil
}

// Update 更新聊天记录
func (m *defaultChatModel) Update(ctx context.Context, tx *gorm.DB, data *Chat) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除聊天记录
func (m *defaultChatModel) Delete(ctx context.Context, tx *gorm.DB, id int64) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&Chat{}, id).Error
}

// UpdateChatFields 更新聊天字段
func (m *defaultChatModel) UpdateChatFields(ctx context.Context, tx *gorm.DB, chatID int64, updates map[string]any) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).
		Model(&Chat{}).
		Where("chat_id = ?", chatID).
		Updates(updates).Error
}

// FindOwnerGroups 查找用户拥有的所有群组
func (m *defaultChatModel) FindOwnerGroups(ctx context.Context, ownerID int64) ([]*Chat, error) {
	var chats []*Chat
	err := m.conn.WithContext(ctx).
		Where("owner_id = ? AND chat_type IN ('group', 'supergroup')", ownerID).
		Order("updated_at DESC").
		Find(&chats).Error
	if err != nil {
		return nil, err
	}
	return chats, nil
}

// BindGroupToUser 将群组绑定到用户
func (m *defaultChatModel) BindGroupToUser(ctx context.Context, tx *gorm.DB, chatID int64, userID int64) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Model(&Chat{}).
		Where("chat_id = ?", chatID).
		Update("owner_id", userID).Error
}

// UnbindGroupFromUser 解绑群组与用户的关系
func (m *defaultChatModel) UnbindGroupFromUser(ctx context.Context, tx *gorm.DB, chatID int64) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Model(&Chat{}).
		Where("chat_id = ?", chatID).
		Update("owner_id", nil).Error
}
