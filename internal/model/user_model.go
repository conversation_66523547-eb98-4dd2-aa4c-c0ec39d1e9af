package model

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type (
	// UserModel 用户模型接口
	UserModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *User) error
		FindOne(ctx context.Context, id int32) (*User, error)
		FindOneByUserID(ctx context.Context, userID int64) (*User, error)
		FindOneByUserIDForUpdate(ctx context.Context, tx *gorm.DB, userID int64) (*User, error)
		Update(ctx context.Context, tx *gorm.DB, data *User) error
		Delete(ctx context.Context, tx *gorm.DB, id int32) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
		UpdateUserFields(ctx context.Context, tx *gorm.DB, userID int64, updates map[string]any) error
	}

	// customUserModel 自定义用户模型实现
	customUserModel struct {
		*defaultUserModel
	}

	// defaultUserModel 默认用户模型实现
	defaultUserModel struct {
		conn *gorm.DB
	}

	// User 用户模型 - 严格对应 users 表
	User struct {
		ID                    int32     `json:"id" gorm:"type:int;primaryKey;autoIncrement"`
		UserID                int64     `json:"user_id" gorm:"uniqueIndex:user_id;not null"`
		ChatID                *int64    `json:"chat_id" gorm:"index:idx_chat_id"`
		UserName              *string   `json:"user_name" gorm:"type:text"`
		DisplayName           *string   `json:"display_name" gorm:"type:text"`
		IsBlocked             bool      `json:"is_blocked" gorm:"not null;default:false"`
		DailyPointBalance     int32     `json:"daily_point_balance" gorm:"type:int;not null;default:0"`
		DailyPointLastResetAt time.Time `json:"daily_point_last_reset_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		VipPointBalance       int32     `json:"vip_point_balance" gorm:"type:int;not null;default:0"`
		VipPointExpiredDate   time.Time `json:"vip_point_expired_date" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		PermanentPointBalance int32     `json:"permanent_point_balance" gorm:"type:int;not null;default:0"`
		HasUsedAI             bool      `json:"has_used_ai" gorm:"not null;default:false"`
		InvitedByUserID       *int64    `json:"invited_by_user_id"`
		Language              string    `json:"language" gorm:"type:varchar(10);not null;default:'en'"`
		CreateAt              time.Time `json:"create_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;index:idx_create_at"`
		LastUpdateAt          time.Time `json:"last_update_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;index:idx_last_update_at"`
	}
)

// NewUserModel 创建用户模型 - 参照go-zero模式
func NewUserModel(conn *gorm.DB) UserModel {
	return &customUserModel{
		defaultUserModel: &defaultUserModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// Trans 事务处理
func (m *defaultUserModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入用户记录
func (m *defaultUserModel) Insert(ctx context.Context, tx *gorm.DB, data *User) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据主键查找用户
func (m *defaultUserModel) FindOne(ctx context.Context, id int32) (*User, error) {
	var user User
	err := m.conn.WithContext(ctx).Where("id = ?", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindOneByUserID 根据用户ID查找用户
func (m *defaultUserModel) FindOneByUserID(ctx context.Context, userID int64) (*User, error) {
	var user User
	err := m.conn.WithContext(ctx).Where("user_id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// FindOneByUserIDForUpdate 根据用户ID查找用户并加行锁
func (m *defaultUserModel) FindOneByUserIDForUpdate(ctx context.Context, tx *gorm.DB, userID int64) (*User, error) {
	db := m.conn
	if tx != nil {
		db = tx
	}

	var user User
	err := db.WithContext(ctx).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("user_id = ?", userID).
		First(&user).Error

	if err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户记录
func (m *defaultUserModel) Update(ctx context.Context, tx *gorm.DB, data *User) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除用户记录
func (m *defaultUserModel) Delete(ctx context.Context, tx *gorm.DB, id int32) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&User{}, id).Error
}

func (m *defaultUserModel) UpdateUserFields(ctx context.Context, tx *gorm.DB, userID int64, updates map[string]any) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Model(&User{}).Where("user_id = ?", userID).Updates(updates).Error
}
