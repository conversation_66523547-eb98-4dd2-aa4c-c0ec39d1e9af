package model

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ RedemptionCodeModel = (*customRedemptionCodeModel)(nil)

type (
	// RedemptionCodeModel 兑换码模型接口
	RedemptionCodeModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *RedemptionCode) error
		FindOne(ctx context.Context, id int32) (*RedemptionCode, error)
		FindOneByCode(ctx context.Context, code string) (*RedemptionCode, error)
		FindOneByCodeForUpdate(ctx context.Context, tx *gorm.DB, code string) (*RedemptionCode, error)
		BatchInsert(ctx context.Context, tx *gorm.DB, data []*RedemptionCode) error
		Update(ctx context.Context, tx *gorm.DB, data *RedemptionCode) error
		Delete(ctx context.Context, tx *gorm.DB, id int32) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
	}

	// customRedemptionCodeModel 自定义模型实现
	customRedemptionCodeModel struct {
		*defaultRedemptionCodeModel
	}

	// defaultRedemptionCodeModel 默认模型实现
	defaultRedemptionCodeModel struct {
		conn *gorm.DB
	}

	// RedemptionCode 兑换码模型 - 严格对应 redemption_codes 表
	RedemptionCode struct {
		ID                    int32      `json:"id" gorm:"type:int;primaryKey;autoIncrement"`
		Code                  string     `json:"code" gorm:"type:varchar(255);uniqueIndex;not null"`
		CreatedAt             time.Time  `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		UpdatedAt             time.Time  `json:"updated_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
		UserID                *int64     `json:"user_id" gorm:"type:bigint"`
		UsedAt                *time.Time `json:"used_at" gorm:"type:timestamp"`
		VipDays               int32      `json:"vip_days" gorm:"type:int;default:0"`
		VipPoints             int32      `json:"vip_points" gorm:"type:int;default:0"`
		PermanentPointBalance int32      `json:"permanent_point_balance" gorm:"type:int;default:0"`
		IsActive              bool       `json:"is_active" gorm:"type:boolean;default:1"`
		Caption               *string    `json:"caption" gorm:"type:text"`
	}
)

// NewRedemptionCodeModel 创建兑换码模型
func NewRedemptionCodeModel(conn *gorm.DB) RedemptionCodeModel {
	return &customRedemptionCodeModel{
		defaultRedemptionCodeModel: &defaultRedemptionCodeModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (RedemptionCode) TableName() string {
	return "redemption_codes"
}

// Trans 事务处理
func (m *defaultRedemptionCodeModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入记录
func (m *defaultRedemptionCodeModel) Insert(ctx context.Context, tx *gorm.DB, data *RedemptionCode) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据ID查找记录
func (m *defaultRedemptionCodeModel) FindOne(ctx context.Context, id int32) (*RedemptionCode, error) {
	var code RedemptionCode
	err := m.conn.WithContext(ctx).First(&code, id).Error
	if err != nil {
		return nil, err
	}
	return &code, nil
}

// FindOneByCode 根据兑换码查找记录
func (m *defaultRedemptionCodeModel) FindOneByCode(ctx context.Context, code string) (*RedemptionCode, error) {
	var redemptionCode RedemptionCode
	err := m.conn.WithContext(ctx).
		Where("code = ?", code).
		First(&redemptionCode).Error
	if err != nil {
		return nil, err
	}
	return &redemptionCode, nil
}

// FindOneByCodeForUpdate 根据兑换码查找记录并加行锁
func (m *defaultRedemptionCodeModel) FindOneByCodeForUpdate(ctx context.Context, tx *gorm.DB, code string) (*RedemptionCode, error) {
	db := m.conn
	if tx != nil {
		db = tx
	}

	var redemptionCode RedemptionCode
	err := db.WithContext(ctx).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("code = ?", code).
		First(&redemptionCode).Error
		
	if err != nil {
		return nil, err
	}
	return &redemptionCode, nil
}

// BatchInsert 批量插入兑换码
func (m *defaultRedemptionCodeModel) BatchInsert(ctx context.Context, tx *gorm.DB, data []*RedemptionCode) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).CreateInBatches(data, 100).Error
}

// Update 更新记录
func (m *defaultRedemptionCodeModel) Update(ctx context.Context, tx *gorm.DB, data *RedemptionCode) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除记录
func (m *defaultRedemptionCodeModel) Delete(ctx context.Context, tx *gorm.DB, id int32) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&RedemptionCode{}, id).Error
}
