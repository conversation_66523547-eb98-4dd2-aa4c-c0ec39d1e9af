package model

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ PackageModel = (*customPackageModel)(nil)

type (
	// PackageModel 套餐模型接口
	PackageModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *Package) error
		FindOne(ctx context.Context, id int32) (*Package, error)
		FindOneForUpdate(ctx context.Context, tx *gorm.DB, id int32) (*Package, error)
		FindAll(ctx context.Context) ([]*Package, error)
		FindActivePackages(ctx context.Context) ([]*Package, error)
		Update(ctx context.Context, tx *gorm.DB, data *Package) error
		Delete(ctx context.Context, tx *gorm.DB, id int32) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
	}

	// customPackageModel 自定义套餐模型实现
	customPackageModel struct {
		*defaultPackageModel
	}

	// defaultPackageModel 默认套餐模型实现
	defaultPackageModel struct {
		conn *gorm.DB
	}

	// Package 套餐模型 - 严格对应 packages 表
	Package struct {
		ID                    int32     `json:"id" gorm:"type:int;primaryKey;autoIncrement"`
		Name                  string    `json:"name" gorm:"type:varchar(255);not null"`
		Description           *string   `json:"description" gorm:"type:text"`
		Price                 float64   `json:"price" gorm:"type:decimal(10,2);not null"`
		VipDays               int32     `json:"vip_days" gorm:"type:int;default:0"`
		VipPoints             int32     `json:"vip_points" gorm:"type:int;default:0"`
		PermanentPointBalance int32     `json:"permanent_point_balance" gorm:"type:int;default:0"`
		IsActive              bool      `json:"is_active" gorm:"type:boolean;default:1"`
		SortOrder             int32     `json:"sort_order" gorm:"type:int;default:0"`
		CreatedAt             time.Time `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		UpdatedAt             time.Time `json:"updated_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	}
)

// NewPackageModel 创建套餐模型
func NewPackageModel(conn *gorm.DB) PackageModel {
	return &customPackageModel{
		defaultPackageModel: &defaultPackageModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (Package) TableName() string {
	return "packages"
}

// Trans 事务处理
func (m *defaultPackageModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入套餐记录
func (m *defaultPackageModel) Insert(ctx context.Context, tx *gorm.DB, data *Package) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据主键查找套餐
func (m *defaultPackageModel) FindOne(ctx context.Context, id int32) (*Package, error) {
	var pkg Package
	err := m.conn.WithContext(ctx).First(&pkg, id).Error
	if err != nil {
		return nil, err
	}
	return &pkg, nil
}

// FindOneForUpdate 根据主键查找套餐并加行锁
func (m *defaultPackageModel) FindOneForUpdate(ctx context.Context, tx *gorm.DB, id int32) (*Package, error) {
	db := m.conn
	if tx != nil {
		db = tx
	}

	var pkg Package
	err := db.WithContext(ctx).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		First(&pkg, id).Error

	if err != nil {
		return nil, err
	}
	return &pkg, nil
}

// FindAll 查找所有套餐
func (m *defaultPackageModel) FindAll(ctx context.Context) ([]*Package, error) {
	var packages []*Package
	err := m.conn.WithContext(ctx).
		Order("sort_order ASC, id ASC").
		Find(&packages).Error
	return packages, err
}

// FindActivePackages 查找所有启用的套餐
func (m *defaultPackageModel) FindActivePackages(ctx context.Context) ([]*Package, error) {
	var packages []*Package
	err := m.conn.WithContext(ctx).
		Where("is_active = ?", true).
		Order("sort_order ASC, id ASC").
		Find(&packages).Error
	return packages, err
}

// Update 更新套餐记录
func (m *defaultPackageModel) Update(ctx context.Context, tx *gorm.DB, data *Package) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除套餐记录
func (m *defaultPackageModel) Delete(ctx context.Context, tx *gorm.DB, id int32) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&Package{}, id).Error
}
