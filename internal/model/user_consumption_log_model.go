package model

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

var _ UserConsumptionLogModel = (*customUserConsumptionLogModel)(nil)

type (
	// UserConsumptionLogModel 用户消费日志模型接口
	UserConsumptionLogModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *UserConsumptionLog) error
		FindOne(ctx context.Context, id int64) (*UserConsumptionLog, error)
		FindBySessionID(ctx context.Context, sessionID string) (*UserConsumptionLog, error)
		Update(ctx context.Context, tx *gorm.DB, data *UserConsumptionLog) error
		Delete(ctx context.Context, tx *gorm.DB, id int64) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
		FindByUserID(ctx context.Context, userID int64, limit int) ([]*UserConsumptionLog, error)

		// 便捷日志记录方法
		LogConsumption(ctx context.Context, tx *gorm.DB, params *ConsumptionLogParams) error
		LogRedemption(ctx context.Context, tx *gorm.DB, params *RedemptionLogParams) error
		LogRegistration(ctx context.Context, tx *gorm.DB, params *RegistrationLogParams) error
		LogSignIn(ctx context.Context, tx *gorm.DB, params *SignInLogParams) error
		LogUserInfoChange(ctx context.Context, tx *gorm.DB, params *UserInfoChangeLogParams) error
		LogSystemOperation(ctx context.Context, tx *gorm.DB, params *SystemLogParams) error
		LogInviteReward(ctx context.Context, tx *gorm.DB, params *InviteRewardLogParams) error
		LogGroupBinding(ctx context.Context, tx *gorm.DB, params *GroupBindingLogParams) error
	}

	// customUserConsumptionLogModel 自定义模型实现
	customUserConsumptionLogModel struct {
		*defaultUserConsumptionLogModel
	}

	// defaultUserConsumptionLogModel 默认模型实现
	defaultUserConsumptionLogModel struct {
		conn *gorm.DB
	}

	// UserConsumptionLog 用户消费日志模型 - 严格对应 user_consumption_logs 表
	UserConsumptionLog struct {
		ID            int64            `json:"id" gorm:"type:bigint;primaryKey;autoIncrement"`
		SessionID     *string          `json:"session_id" gorm:"type:varchar(255);uniqueIndex"`
		UserID        int64            `json:"user_id" gorm:"type:bigint;not null;index:idx_user_id"`
		ChatID        *int64           `json:"chat_id" gorm:"type:bigint;index:idx_chat_id"`
		MessageID     *int64           `json:"message_id" gorm:"type:bigint"`
		LogType       string           `json:"log_type" gorm:"type:text;not null"`
		UserMessage   *string          `json:"user_message" gorm:"type:text"`
		MsgHistory    *string          `json:"msg_history" gorm:"type:text"`
		BotResponse   *string          `json:"bot_response" gorm:"type:text"`
		Caption       *string          `json:"caption" gorm:"type:text"`
		PointChange   *PointChange     `json:"point_change" gorm:"type:json"`
		VipDaysChange *int32           `json:"vip_days_change" gorm:"type:int"`
		ExtraData     *json.RawMessage `json:"extra_data" gorm:"type:json"`
		CreatedAt     time.Time        `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP;index:idx_created_at"`
		UpdatedAt     time.Time        `json:"updated_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	}

	// PointChange 积分变化结构
	// 正数表示增加积分（签到、兑换等），负数表示消费积分（AI使用等）
	PointChange struct {
		Daily     int32 `json:"daily"`     // 每日积分变化
		VIP       int32 `json:"vip"`       // VIP积分变化
		Permanent int32 `json:"permanent"` // 永久积分变化
	}

	// 各种日志参数结构体
	ConsumptionLogParams struct {
		SessionID   *string
		UserID      int64
		ChatID      *int64
		MessageID   *int64
		UserMessage *string
		MsgHistory  *string
		BotResponse *string
		Caption     *string
		PointChange *PointChange
		ExtraData   interface{}
	}

	RedemptionLogParams struct {
		SessionID      *string
		UserID         int64
		ChatID         *int64
		MessageID      *int64
		RedemptionCode string
		PointChange    *PointChange
		VipDaysChange  *int32
		ExtraData      interface{}
	}

	RegistrationLogParams struct {
		SessionID   *string
		UserID      int64
		ChatID      *int64
		MessageID   *int64
		UserName    *string
		DisplayName *string
		Language    string
		PointChange *PointChange // 注册时的初始积分
		ExtraData   interface{}
	}

	SignInLogParams struct {
		SessionID   *string
		UserID      int64
		ChatID      *int64
		MessageID   *int64
		PointChange *PointChange
		ExtraData   interface{}
	}

	UserInfoChangeLogParams struct {
		SessionID  *string
		UserID     int64
		ChatID     *int64
		MessageID  *int64
		ChangeType string // "username", "display_name", "language", etc.
		OldValue   *string
		NewValue   *string
		ExtraData  interface{}
	}

	SystemLogParams struct {
		SessionID   *string
		UserID      int64
		ChatID      *int64
		MessageID   *int64
		Operation   string
		Description *string
		ExtraData   interface{}
	}

	InviteRewardLogParams struct {
		SessionID     *string
		UserID        int64 // 获得奖励的用户ID
		ChatID        *int64
		MessageID     *int64
		InviterID     *int64 // 邀请人ID（如果当前用户是被邀请人）
		InviteeID     *int64 // 被邀请人ID（如果当前用户是邀请人）
		RewardType    string // "inviter_reward" 或 "invitee_reward"
		PointChange   *PointChange
		VipDaysChange *int32
		ExtraData     interface{}
	}

	GroupBindingLogParams struct {
		SessionID   *string
		UserID      int64
		ChatID      *int64 // 被绑定/解绑的群组ID
		MessageID   *int64
		Operation   string  // "bind" 或 "unbind"
		GroupTitle  *string // 群组标题
		GroupType   *string // 群组类型
		LeaveResult *string // 退群结果（仅解绑时）
		ExtraData   interface{}
	}
)

// NewUserConsumptionLogModel 创建用户消费日志模型
func NewUserConsumptionLogModel(conn *gorm.DB) UserConsumptionLogModel {
	return &customUserConsumptionLogModel{
		defaultUserConsumptionLogModel: &defaultUserConsumptionLogModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (UserConsumptionLog) TableName() string {
	return "user_consumption_logs"
}

// Trans 事务处理
func (m *defaultUserConsumptionLogModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入记录
func (m *defaultUserConsumptionLogModel) Insert(ctx context.Context, tx *gorm.DB, data *UserConsumptionLog) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据ID查找记录
func (m *defaultUserConsumptionLogModel) FindOne(ctx context.Context, id int64) (*UserConsumptionLog, error) {
	var log UserConsumptionLog
	err := m.conn.WithContext(ctx).First(&log, id).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// FindBySessionID 根据会话ID查找记录
func (m *defaultUserConsumptionLogModel) FindBySessionID(ctx context.Context, sessionID string) (*UserConsumptionLog, error) {
	var log UserConsumptionLog
	err := m.conn.WithContext(ctx).
		Where("session_id = ?", sessionID).
		First(&log).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// FindByUserID 根据用户ID查找记录
func (m *defaultUserConsumptionLogModel) FindByUserID(ctx context.Context, userID int64, limit int) ([]*UserConsumptionLog, error) {
	var logs []*UserConsumptionLog
	query := m.conn.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&logs).Error
	if err != nil {
		return nil, err
	}
	return logs, nil
}

// Update 更新记录
func (m *defaultUserConsumptionLogModel) Update(ctx context.Context, tx *gorm.DB, data *UserConsumptionLog) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// Delete 删除记录
func (m *defaultUserConsumptionLogModel) Delete(ctx context.Context, tx *gorm.DB, id int64) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&UserConsumptionLog{}, id).Error
}

// LogConsumption 记录消费日志
func (m *defaultUserConsumptionLogModel) LogConsumption(ctx context.Context, tx *gorm.DB, params *ConsumptionLogParams) error {
	log := &UserConsumptionLog{
		SessionID:   params.SessionID,
		UserID:      params.UserID,
		ChatID:      params.ChatID,
		MessageID:   params.MessageID,
		LogType:     "consumption",
		UserMessage: params.UserMessage,
		MsgHistory:  params.MsgHistory,
		BotResponse: params.BotResponse,
		Caption:     params.Caption,
		PointChange: params.PointChange,
	}

	if params.ExtraData != nil {
		extraDataBytes, err := json.Marshal(params.ExtraData)
		if err == nil {
			rawMessage := json.RawMessage(extraDataBytes)
			log.ExtraData = &rawMessage
		}
	}

	return m.Insert(ctx, tx, log)
}

// LogRedemption 记录兑换日志
func (m *defaultUserConsumptionLogModel) LogRedemption(ctx context.Context, tx *gorm.DB, params *RedemptionLogParams) error {
	log := &UserConsumptionLog{
		SessionID:     params.SessionID,
		UserID:        params.UserID,
		ChatID:        params.ChatID,
		MessageID:     params.MessageID,
		LogType:       "redemption",
		Caption:       &params.RedemptionCode,
		PointChange:   params.PointChange,
		VipDaysChange: params.VipDaysChange,
	}

	if params.ExtraData != nil {
		extraDataBytes, err := json.Marshal(params.ExtraData)
		if err == nil {
			rawMessage := json.RawMessage(extraDataBytes)
			log.ExtraData = &rawMessage
		}
	}

	return m.Insert(ctx, tx, log)
}

// LogRegistration 记录注册日志
func (m *defaultUserConsumptionLogModel) LogRegistration(ctx context.Context, tx *gorm.DB, params *RegistrationLogParams) error {
	log := &UserConsumptionLog{
		SessionID:   params.SessionID,
		UserID:      params.UserID,
		ChatID:      params.ChatID,
		MessageID:   params.MessageID,
		LogType:     "registration",
		PointChange: params.PointChange,
	}

	// 将用户信息放入ExtraData
	registrationData := map[string]interface{}{
		"user_name":    params.UserName,
		"display_name": params.DisplayName,
		"language":     params.Language,
	}

	if params.ExtraData != nil {
		if extraMap, ok := params.ExtraData.(map[string]interface{}); ok {
			for k, v := range extraMap {
				registrationData[k] = v
			}
		}
	}

	extraDataBytes, err := json.Marshal(registrationData)
	if err == nil {
		rawMessage := json.RawMessage(extraDataBytes)
		log.ExtraData = &rawMessage
	}

	return m.Insert(ctx, tx, log)
}

// LogSignIn 记录签到日志
func (m *defaultUserConsumptionLogModel) LogSignIn(ctx context.Context, tx *gorm.DB, params *SignInLogParams) error {
	log := &UserConsumptionLog{
		SessionID:   params.SessionID,
		UserID:      params.UserID,
		ChatID:      params.ChatID,
		MessageID:   params.MessageID,
		LogType:     "sign_in",
		PointChange: params.PointChange,
	}

	if params.ExtraData != nil {
		extraDataBytes, err := json.Marshal(params.ExtraData)
		if err == nil {
			rawMessage := json.RawMessage(extraDataBytes)
			log.ExtraData = &rawMessage
		}
	}

	return m.Insert(ctx, tx, log)
}

// LogUserInfoChange 记录用户信息变更日志
func (m *defaultUserConsumptionLogModel) LogUserInfoChange(ctx context.Context, tx *gorm.DB, params *UserInfoChangeLogParams) error {
	log := &UserConsumptionLog{
		SessionID: params.SessionID,
		UserID:    params.UserID,
		ChatID:    params.ChatID,
		MessageID: params.MessageID,
		LogType:   "user_info_change",
	}

	// 将变更信息放入ExtraData
	changeData := map[string]interface{}{
		"change_type": params.ChangeType,
		"old_value":   params.OldValue,
		"new_value":   params.NewValue,
	}

	if params.ExtraData != nil {
		if extraMap, ok := params.ExtraData.(map[string]interface{}); ok {
			for k, v := range extraMap {
				changeData[k] = v
			}
		}
	}

	extraDataBytes, err := json.Marshal(changeData)
	if err == nil {
		rawMessage := json.RawMessage(extraDataBytes)
		log.ExtraData = &rawMessage
	}

	return m.Insert(ctx, tx, log)
}

// LogSystemOperation 记录系统操作日志
func (m *defaultUserConsumptionLogModel) LogSystemOperation(ctx context.Context, tx *gorm.DB, params *SystemLogParams) error {
	log := &UserConsumptionLog{
		SessionID: params.SessionID,
		UserID:    params.UserID,
		ChatID:    params.ChatID,
		MessageID: params.MessageID,
		LogType:   "system",
		Caption:   params.Description,
	}

	// 将操作信息放入ExtraData
	systemData := map[string]interface{}{
		"operation": params.Operation,
	}

	if params.ExtraData != nil {
		if extraMap, ok := params.ExtraData.(map[string]interface{}); ok {
			for k, v := range extraMap {
				systemData[k] = v
			}
		}
	}

	extraDataBytes, err := json.Marshal(systemData)
	if err == nil {
		rawMessage := json.RawMessage(extraDataBytes)
		log.ExtraData = &rawMessage
	}

	return m.Insert(ctx, tx, log)
}

// LogInviteReward 记录邀请奖励日志
func (m *defaultUserConsumptionLogModel) LogInviteReward(ctx context.Context, tx *gorm.DB, params *InviteRewardLogParams) error {
	log := &UserConsumptionLog{
		SessionID:     params.SessionID,
		UserID:        params.UserID,
		ChatID:        params.ChatID,
		MessageID:     params.MessageID,
		LogType:       "invite_reward",
		PointChange:   params.PointChange,
		VipDaysChange: params.VipDaysChange,
	}

	// 将邀请信息放入ExtraData
	inviteData := map[string]interface{}{
		"reward_type": params.RewardType,
	}

	if params.InviterID != nil {
		inviteData["inviter_id"] = *params.InviterID
	}
	if params.InviteeID != nil {
		inviteData["invitee_id"] = *params.InviteeID
	}

	if params.ExtraData != nil {
		if extraMap, ok := params.ExtraData.(map[string]interface{}); ok {
			for k, v := range extraMap {
				inviteData[k] = v
			}
		}
	}

	extraDataBytes, err := json.Marshal(inviteData)
	if err == nil {
		rawMessage := json.RawMessage(extraDataBytes)
		log.ExtraData = &rawMessage
	}

	return m.Insert(ctx, tx, log)
}

// LogGroupBinding 记录群组绑定/解绑日志
func (m *defaultUserConsumptionLogModel) LogGroupBinding(ctx context.Context, tx *gorm.DB, params *GroupBindingLogParams) error {
	log := &UserConsumptionLog{
		SessionID: params.SessionID,
		UserID:    params.UserID,
		ChatID:    params.ChatID,
		MessageID: params.MessageID,
		LogType:   "group_binding",
	}

	// 将绑定信息放入ExtraData
	bindingData := map[string]interface{}{
		"operation": params.Operation,
	}

	if params.GroupTitle != nil {
		bindingData["group_title"] = *params.GroupTitle
	}
	if params.GroupType != nil {
		bindingData["group_type"] = *params.GroupType
	}
	if params.LeaveResult != nil {
		bindingData["leave_result"] = *params.LeaveResult
	}

	if params.ExtraData != nil {
		if extraMap, ok := params.ExtraData.(map[string]interface{}); ok {
			for k, v := range extraMap {
				bindingData[k] = v
			}
		}
	}

	extraDataBytes, err := json.Marshal(bindingData)
	if err == nil {
		rawMessage := json.RawMessage(extraDataBytes)
		log.ExtraData = &rawMessage
	}

	return m.Insert(ctx, tx, log)
}

// Value 实现 driver.Valuer 接口，用于将 PointChange 转换为数据库值
func (p PointChange) Value() (driver.Value, error) {
	return json.Marshal(p)
}

// Scan 实现 sql.Scanner 接口，用于从数据库值扫描到 PointChange
func (p *PointChange) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into PointChange", value)
	}

	return json.Unmarshal(bytes, p)
}
