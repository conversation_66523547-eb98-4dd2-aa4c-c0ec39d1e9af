package model

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ PaymentOrderModel = (*customPaymentOrderModel)(nil)

type (
	// PaymentOrderModel 支付订单模型接口
	PaymentOrderModel interface {
		Insert(ctx context.Context, tx *gorm.DB, data *PaymentOrder) error
		FindOne(ctx context.Context, id int32) (*PaymentOrder, error)
		FindOneByOrderID(ctx context.Context, orderID string) (*PaymentOrder, error)
		FindOneByOrderIDForUpdate(ctx context.Context, tx *gorm.DB, orderID string) (*PaymentOrder, error)
		FindByUserID(ctx context.Context, userID int64, limit int) ([]*PaymentOrder, error)
		Update(ctx context.Context, tx *gorm.DB, data *PaymentOrder) error
		UpdateStatus(ctx context.Context, tx *gorm.DB, orderID string, status int32, tradeID, blockTxID string) error
		Delete(ctx context.Context, tx *gorm.DB, id int32) error
		ExpireOrders(ctx context.Context, tx *gorm.DB) error
		Trans(ctx context.Context, fn func(db *gorm.DB) error) error
	}

	// customPaymentOrderModel 自定义支付订单模型实现
	customPaymentOrderModel struct {
		*defaultPaymentOrderModel
	}

	// defaultPaymentOrderModel 默认支付订单模型实现
	defaultPaymentOrderModel struct {
		conn *gorm.DB
	}

	// PaymentOrder 支付订单模型 - 严格对应 payment_orders 表
	PaymentOrder struct {
		ID             int32     `json:"id" gorm:"type:int;primaryKey;autoIncrement"`
		OrderID        string    `json:"order_id" gorm:"type:varchar(64);uniqueIndex;not null"`
		UserID         int64     `json:"user_id" gorm:"type:bigint;not null"`
		PackageID      *int32    `json:"package_id" gorm:"type:int"`
		Amount         float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
		ActualAmount   float64   `json:"actual_amount" gorm:"type:decimal(18,8);not null"`
		Token          string    `json:"token" gorm:"type:varchar(128);not null"`
		Status         int32     `json:"status" gorm:"type:int;not null;default:0"`
		TradeID        *string   `json:"trade_id" gorm:"type:varchar(128)"`
		BlockTxID      *string   `json:"block_tx_id" gorm:"type:varchar(128)"`
		ExpirationTime time.Time `json:"expiration_time" gorm:"type:timestamp;not null"`
		CreatedAt      time.Time `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
		UpdatedAt      time.Time `json:"updated_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	}
)

// NewPaymentOrderModel 创建支付订单模型
func NewPaymentOrderModel(conn *gorm.DB) PaymentOrderModel {
	return &customPaymentOrderModel{
		defaultPaymentOrderModel: &defaultPaymentOrderModel{
			conn: conn,
		},
	}
}

// TableName 指定表名
func (PaymentOrder) TableName() string {
	return "payment_orders"
}

// Trans 事务处理
func (m *defaultPaymentOrderModel) Trans(ctx context.Context, fn func(db *gorm.DB) error) error {
	return m.conn.WithContext(ctx).Transaction(fn)
}

// Insert 插入支付订单记录
func (m *defaultPaymentOrderModel) Insert(ctx context.Context, tx *gorm.DB, data *PaymentOrder) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Create(data).Error
}

// FindOne 根据主键查找支付订单
func (m *defaultPaymentOrderModel) FindOne(ctx context.Context, id int32) (*PaymentOrder, error) {
	var order PaymentOrder
	err := m.conn.WithContext(ctx).First(&order, id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// FindOneByOrderID 根据订单ID查找支付订单
func (m *defaultPaymentOrderModel) FindOneByOrderID(ctx context.Context, orderID string) (*PaymentOrder, error) {
	var order PaymentOrder
	err := m.conn.WithContext(ctx).
		Where("order_id = ?", orderID).
		First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

// FindOneByOrderIDForUpdate 根据订单ID查找支付订单并加行锁
func (m *defaultPaymentOrderModel) FindOneByOrderIDForUpdate(ctx context.Context, tx *gorm.DB, orderID string) (*PaymentOrder, error) {
	db := m.conn
	if tx != nil {
		db = tx
	}

	var order PaymentOrder
	err := db.WithContext(ctx).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("order_id = ?", orderID).
		First(&order).Error

	if err != nil {
		return nil, err
	}
	return &order, nil
}

// FindByUserID 根据用户ID查找支付订单列表
func (m *defaultPaymentOrderModel) FindByUserID(ctx context.Context, userID int64, limit int) ([]*PaymentOrder, error) {
	var orders []*PaymentOrder
	err := m.conn.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at desc").
		Limit(limit).
		Find(&orders).Error
	return orders, err
}

// Update 更新支付订单记录
func (m *defaultPaymentOrderModel) Update(ctx context.Context, tx *gorm.DB, data *PaymentOrder) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Save(data).Error
}

// UpdateStatus 更新支付状态
func (m *defaultPaymentOrderModel) UpdateStatus(ctx context.Context, tx *gorm.DB, orderID string, status int32, tradeID, blockTxID string) error {
	db := m.conn
	if tx != nil {
		db = tx
	}

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if tradeID != "" {
		updates["trade_id"] = tradeID
	}
	if blockTxID != "" {
		updates["block_tx_id"] = blockTxID
	}

	return db.WithContext(ctx).
		Model(&PaymentOrder{}).
		Where("order_id = ?", orderID).
		Updates(updates).Error
}

// Delete 删除支付订单记录
func (m *defaultPaymentOrderModel) Delete(ctx context.Context, tx *gorm.DB, id int32) error {
	db := m.conn
	if tx != nil {
		db = tx
	}
	return db.WithContext(ctx).Delete(&PaymentOrder{}, id).Error
}

// ExpireOrders 过期未支付的订单
func (m *defaultPaymentOrderModel) ExpireOrders(ctx context.Context, tx *gorm.DB) error {
	db := m.conn
	if tx != nil {
		db = tx
	}

	return db.WithContext(ctx).
		Model(&PaymentOrder{}).
		Where("status IN (0, 1) AND expiration_time < ?", time.Now()).
		Updates(map[string]interface{}{
			"status":     4, // 已过期
			"updated_at": time.Now(),
		}).Error
}
